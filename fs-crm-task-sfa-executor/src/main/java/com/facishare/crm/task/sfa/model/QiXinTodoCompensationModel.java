package com.facishare.crm.task.sfa.model;

import lombok.Builder;
import lombok.Data;

/**
 * 企信待办补偿 class
 *
 * <AUTHOR>
 * @date 2019/10/23
 */
public interface QiXinTodoCompensationModel {
    @Data
    @Builder
    class Arg {
        Integer session_boc_item_key;
        String object_api_name;
        Integer not_deal_count;
    }

    @Data
    class Result {
        Integer errCode;
        String errMessage;
    }
}
