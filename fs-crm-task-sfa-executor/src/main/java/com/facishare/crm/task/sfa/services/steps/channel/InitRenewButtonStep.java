package com.facishare.crm.task.sfa.services.steps.channel;

import com.beust.jcommander.internal.Sets;
import com.facishare.crm.sfa.prm.platform.infrastructure.execution.InitStep;
import com.facishare.crm.task.sfa.common.gray.GrayUtils;
import com.facishare.crm.task.sfa.services.EnterpriseInitService;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.ButtonUsePageType;
import com.facishare.paas.appframework.metadata.button.ButtonDefineType;
import com.facishare.paas.appframework.metadata.button.ButtonType;
import com.facishare.paas.appframework.privilege.util.PrivilegeConstants;
import com.facishare.paas.metadata.api.IUdefButton;
import com.facishare.paas.metadata.impl.UdefButton;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Set;

import static com.facishare.crm.task.sfa.services.steps.constants.ChannelStepConstants.CHANNEL_ACCESS;

/**
 * ============================================================
 *
 * @Description:
 * @CreatedBy: Sundy on 2025-06-18
 * ============================================================
 */
@Component
@Slf4j
public class InitRenewButtonStep implements InitStep<ChannelStepContext> {
    @Resource
    private EnterpriseInitService enterpriseInitService;
    @Resource
    private ServiceFacade serviceFacade;

    @Override
    public boolean execute(ChannelStepContext channelStepContext) {
        User user = channelStepContext.getUser();
        if (!GrayUtils.isVersionGrayTenant(user.getTenantId())) {
            return true;
        }
        IUdefButton button = buildButtonInfo(ObjectAction.RENEW, "PartnerAgreementDetailObj");
        enterpriseInitService.addCustomButton(user, button);
        Set<String> actionCodes = Sets.newHashSet();
        actionCodes.add(ObjectAction.RENEW.getActionCode());
        initRenewFuncAndAccess(user, actionCodes, "PartnerAgreementDetailObj");
        return true;
    }

    private void initRenewFuncAndAccess(User user, Set<String> actionCodes, String objectApiName) {
        if (CollectionUtils.isEmpty(actionCodes)) {
            return;
        }
        try {
            serviceFacade.batchCreateFunc(user, objectApiName, Lists.newArrayList(actionCodes));
            serviceFacade.updateUserDefinedFuncAccess(user, PrivilegeConstants.ADMIN_ROLE_CODE, objectApiName, Lists.newArrayList(actionCodes), Lists.newArrayList());
        } catch (Exception e) {
            log.warn("InitRenewButtonStep error, tenant:{}", user.getTenantId(), e);
        }

    }

    private IUdefButton buildButtonInfo(ObjectAction objectAction, String objectApiName) {
        IUdefButton createButton = new UdefButton();
        createButton.setApiName(objectAction.getButtonApiName());
        createButton.setLabel(objectAction.getActionLabel());
        createButton.setUsePages(Lists.newArrayList(ButtonUsePageType.Detail.getId(), ButtonUsePageType.DataList.getId(),
                ButtonUsePageType.RelatedList.getId()));
        createButton.setDescribeApiName(objectApiName);
        createButton.setButtonType(ButtonType.COMMON.getId());
        createButton.setIsActive(true);
        createButton.setDefineType(ButtonDefineType.SYSTEM.getId());
        return createButton;
    }

    @Override
    public String getTaskName() {
        return CHANNEL_ACCESS;
    }

    @Override
    public int getOrder() {
        return 10;
    }
}
