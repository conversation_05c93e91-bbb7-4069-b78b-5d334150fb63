package com.facishare.crm.task.sfa.access;

import com.beust.jcommander.internal.Lists;
import com.facishare.crm.task.sfa.enums.ChannelStage;
import com.facishare.crm.task.sfa.model.ActivationTask;
import com.facishare.crm.task.sfa.model.ActivationTaskModel;
import com.facishare.crm.task.sfa.services.MetadataServiceExt;
import com.facishare.crm.task.sfa.util.SearchUtil;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.google.common.collect.Maps;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.validation.constraints.NotNull;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @time 2024-07-04 10:55
 * @Description
 */
@Service
public class ActivationTaskAccess {
    @Resource
    private ServiceFacade serviceFacade;
    @Resource
    private MetadataServiceExt metadataServiceExt;


    public IObjectData saveActivationTask(User user, ActivationTask activationTask) {
        IObjectData data = convertObjectData(user, activationTask);
        return serviceFacade.saveObjectData(user, data);
    }

    public List<IObjectData> deleteActivationTaskByPartnerId(User user, String partnerId) {
        List<IObjectData> dataList = queryTaskByPartnerId(user, partnerId);
        if (CollectionUtils.isEmpty(dataList)) {
            return dataList;
        }
        return serviceFacade.bulkDeleteDirect(dataList, user);
    }

    private IObjectData convertObjectData(User user, ActivationTask activationTask) {
        IObjectData data = ObjectDataDocument.of(Maps.newHashMap()).toObjectData();
        data.set(IObjectData.ID, serviceFacade.generateId());
        data.setTenantId(user.getTenantId());
        data.setDescribeApiName(ActivationTaskModel.APPROVAL_ACTIVATION_TASK_OBJ);
        data.set(ActivationTaskModel.ACTIVATION_PARTNER_ID, activationTask.getActivationPartnerId());
        data.set(ActivationTaskModel.TASK_ID, activationTask.getTaskId());
        data.set(ActivationTaskModel.ACTIVATION_SETTING_ID, activationTask.getActivationSettingId());
        data.set(ActivationTaskModel.ENTERPRISE_ID, activationTask.getEnterpriseId());
        data.set(ActivationTaskModel.BIZ_SCOPE, activationTask.getBizScope().getScope());
        data.set(ActivationTaskModel.TASK_PROGRESS, activationTask.getTaskProgress().getStage());
        data.set(ActivationTaskModel.ASSOCIATION_OBJECT, activationTask.getAssociationObject());
        data.set(ActivationTaskModel.ASSOCIATION_DATA_ID, activationTask.getAssociationDataId());
        return data;
    }

    public IObjectData queryTaskById(User user, String activationTaskId) {
        return metadataServiceExt.findObjectById(user, activationTaskId, ActivationTaskModel.APPROVAL_ACTIVATION_TASK_OBJ);
    }

    public List<IObjectData> queryTaskByPartnerId(User user, @NotNull String partnerId) {
        List<IObjectData> result = Lists.newArrayList();
        SearchTemplateQuery query = new SearchTemplateQuery();
        SearchUtil.fillFilterEq(query.getFilters(), ActivationTaskModel.ACTIVATION_PARTNER_ID, partnerId);
        metadataServiceExt.findObjectBySearchQueryIgnoreAll(user, ActivationTaskModel.APPROVAL_ACTIVATION_TASK_OBJ, query, result::addAll);
        return result;
    }

    public List<IObjectData> queryAllPendingActivationTask(User user) {
        List<IObjectData> result = Lists.newArrayList();
        SearchTemplateQuery query = new SearchTemplateQuery();
        metadataServiceExt.findObjectBySearchQueryIgnoreAll(user, ActivationTaskModel.APPROVAL_ACTIVATION_TASK_OBJ, query, result::addAll);
        return result;

    }
}
