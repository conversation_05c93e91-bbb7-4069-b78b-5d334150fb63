package com.facishare.crm.task.sfa.services.channel.rules;

import com.facishare.crm.sfa.prm.api.enums.SignStatus;
import com.facishare.crm.sfa.prm.platform.infrastructure.statemachine.core.StateAction;
import com.facishare.crm.sfa.prm.platform.infrastructure.statemachine.rule.AbstractTransitionRuleRule;
import com.facishare.crm.sfa.prm.platform.infrastructure.statemachine.rule.StateRulesBuilder;
import com.facishare.crm.task.sfa.services.channel.model.ChannelContext;
import com.facishare.crm.task.sfa.services.channel.enums.ChannelEvent;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

import static com.facishare.crm.task.sfa.services.channel.ChannelMachine.STATE_MACHINE_NAME;

/**
 * ============================================================
 *
 * @Description:
 * @CreatedBy: Sundy on 2025-05-20
 * ============================================================
 */
@Component
public class ChannelTransitionRule extends AbstractTransitionRuleRule<SignStatus, ChannelEvent, ChannelContext> {
    @Resource(name = "registerPassAction")
    private StateAction<SignStatus, ChannelEvent, ChannelContext> registerPassAction;
    @Resource(name = "registerRejetAction")
    private StateAction<SignStatus, ChannelEvent, ChannelContext> registerRejetAction;
    @Resource(name = "signPassAction")
    private StateAction<SignStatus, ChannelEvent, ChannelContext> signPassAction;
    @Resource(name = "signRejetAction")
    private StateAction<SignStatus, ChannelEvent, ChannelContext> signRejetAction;
    @Resource(name = "renewPassAction")
    private StateAction<SignStatus, ChannelEvent, ChannelContext> renewPassAction;
    @Resource(name = "renewRejetAction")
    private StateAction<SignStatus, ChannelEvent, ChannelContext> renewRejetAction;

    @Override
    protected StateRulesBuilder<SignStatus, ChannelEvent, ChannelContext> initTransitionRules() {
        return StateRulesBuilder.<SignStatus, ChannelEvent, ChannelContext>builder()
                // 注册
                .addTransition(SignStatus.PENDING_SIGNATURE, ChannelEvent.REGISTER_PASS, SignStatus.PENDING_SIGNATURE, registerPassAction)
                .addTransition(SignStatus.PENDING_SIGNATURE, ChannelEvent.REGISTER_REJET, SignStatus.PENDING_SIGNATURE, registerRejetAction)
                // 签约
                .addTransition(SignStatus.PENDING_SIGNATURE, ChannelEvent.SIGN_PASS, SignStatus.SIGNED, signPassAction)
                .addTransition(SignStatus.PENDING_SIGNATURE, ChannelEvent.SIGN_REJET, SignStatus.REJECT, signRejetAction)
                .addTransition(SignStatus.REJECT, ChannelEvent.SIGN_PASS, SignStatus.SIGNED, signPassAction)
                .addTransition(SignStatus.REJECT, ChannelEvent.SIGN_REJET, SignStatus.REJECT, signRejetAction)
                // 续约
                .addTransition(SignStatus.PENDING_RENEWAL, ChannelEvent.RENEW_PASS, SignStatus.RENEWAL, renewPassAction)
                .addTransition(SignStatus.PENDING_RENEWAL, ChannelEvent.RENEW_REJET, SignStatus.REJECT, renewRejetAction)
                .addTransition(SignStatus.REJECT, ChannelEvent.RENEW_PASS, SignStatus.RENEWAL, renewPassAction)
                .addTransition(SignStatus.REJECT, ChannelEvent.RENEW_REJET, SignStatus.REJECT, renewRejetAction)
                ;
    }

    @Override
    public String getTransitionKey() {
        return STATE_MACHINE_NAME;
    }
}
