package com.facishare.crm.task.sfa.services.channel.actions;

import com.facishare.crm.sfa.prm.api.enums.SignStatus;
import com.facishare.crm.sfa.prm.platform.infrastructure.statemachine.core.StateAction;
import com.facishare.crm.task.sfa.enums.BizScopeEnums;
import com.facishare.crm.task.sfa.enums.ChannelStage;
import com.facishare.crm.task.sfa.model.ChannelModel;
import com.facishare.crm.task.sfa.services.*;
import com.facishare.crm.task.sfa.services.channel.core.ChannelAssertValidator;
import com.facishare.crm.task.sfa.services.channel.exception.ChannelFlowException;
import com.facishare.crm.task.sfa.services.channel.exception.ItemNullException;
import com.facishare.crm.task.sfa.services.channel.model.ChannelContext;
import com.facishare.crm.task.sfa.services.channel.enums.ChannelEvent;
import com.facishare.crm.task.sfa.services.channel.model.RegisterMessage;
import com.facishare.crm.task.sfa.services.channel.utils.StepRecorder;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.IObjectData;
import com.fxiaoke.enterpriserelation2.result.OuterAccountVo;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * ============================================================
 *
 * @Description:
 * @CreatedBy: Sundy on 2025-05-20
 * ============================================================
 */
@Component
@Slf4j
public class RegisterPassAction implements StateAction<SignStatus, ChannelEvent, ChannelContext> {
    @Resource
    private MetadataServiceExt metadataServiceExt;
    @Resource(name = "channelRestServiceImpl")
    private ChannelRestService channelRestService;
    @Resource
    private ChannelNotificationService channelNotificationService;
    @Resource
    private ChannelEnterpriseRelationService channelEnterpriseRelationService;
    @Resource
    private ChannelRegisterTaskService channelRegisterTaskService;
    @Resource
    private ChannelRegisterService channelRegisterService;
    @Resource
    private OutRelationService outRelationService;
    @Resource
    private ChannelAssertValidator channelAssertValidator;
    @Resource
    private ActivationTaskService activationTaskService;

    @Override
    public void execute(SignStatus sourceState, ChannelEvent event, ChannelContext context) {
        log.info("RegisterPassAction#execute Register Pass, sourceState: {}, event: {}, context:{}", sourceState, event, context);
        StepRecorder.record("RegisterPassAction#execute");
        RegisterMessage registerMessage = (RegisterMessage) context.getPayload();
        User user = context.getUser();
        IObjectData objectData = channelAssertValidator.assertValidData(user, registerMessage.getObjectApiName(), registerMessage.getDataId());
        ChannelModel.EnterpriseActivationSettingDTO enterpriseActivationSettingDTO = assertValidEnterpriseActivationSetting(user, registerMessage.getObjectApiName(), registerMessage.getDataId());
        StepRecorder.record("fetchMatchEnterpriseSetting");
        ChannelModel.CreateDataResult createContactResult = channelRestService.createRelationContactByData(user, registerMessage.getObjectApiName(), objectData.getId());
        StepRecorder.record("createRelationContactByData");
        if (!createContactResult.isSuccess()) {
            doActionWhenCreateContactFail(user, registerMessage, createContactResult, objectData);
            throw new ChannelFlowException("create Contact failed");
        }
        log.info("RegisterPassAction#createEnterpriseRelationWhenRegister 创建联系人成功");
        OuterAccountVo outerAccountVo = createOuterAccountVo(user, registerMessage, enterpriseActivationSettingDTO, objectData, createContactResult);
        StepRecorder.record("createEnterpriseWithRetry");
        if (outerAccountVo == null) {
            doActionWhenCreateEnterpriseFail(user, createContactResult, registerMessage, objectData);
            throw new ChannelFlowException("createEnterprise Failed");
        }
        IObjectData enterpriseData = assertValidEnterpriseData(user, String.valueOf(outerAccountVo.getOuterTenantId()));
        StepRecorder.record("queryLinkEnterpriseDataByOutTenantId");
        log.info("RegisterPassAction#createEnterpriseRelationWhenRegister 创建互联企业成功");
        ChannelModel.OutInfoArg outInfoArg = ChannelModel.OutInfoArg
                .builder()
                .objectApiName(registerMessage.getObjectApiName())
                .dataId(registerMessage.getDataId())
                .outerTenantId(outerAccountVo.getOuterTenantId())
                .outUserId(outerAccountVo.getOuterUid())
                .build();
        log.info("RegisterPassAction#createEnterpriseRelationWhenRegister allocateOutInfoForData outInfoArg:{}", outInfoArg);
        outRelationService.allocateOutInfoForData(user, outInfoArg);
        StepRecorder.record("allocateOutInfoForData");
        channelNotificationService.sendRegisterEnterpriseSuccessCrmNotice(user, registerMessage.getOperator(), registerMessage.getObjectApiName(), objectData);
        StepRecorder.record("sendRegisterEnterpriseSuccessCrmNotice");
        channelNotificationService.sendRegisterEnterpriseSuccessChannelNotice(user, registerMessage.getObjectApiName(), objectData);
        StepRecorder.record("sendRegisterEnterpriseSuccessChannelNotice");
        ChannelModel.RegisterActivationTaskArg registerActivationTaskArg = ChannelModel.RegisterActivationTaskArg
                .builder()
                .objectApiName(registerMessage.getObjectApiName())
                .enterpriseActivationSetting(enterpriseActivationSettingDTO)
                .enterpriseRelationId(enterpriseData.getId())
                .objectData(objectData)
                .createTime(enterpriseData.getCreateTime())
                .build();
        channelRegisterTaskService.scheduleEnterpriseDeactivation(user, registerActivationTaskArg);
        StepRecorder.record("scheduleEnterpriseDeactivation");
        activationTaskService.createActivationTask(user, ChannelStage.REGISTERED, BizScopeEnums.REGISTER,
                registerMessage.getObjectApiName(),
                registerMessage.getDataId(),
                enterpriseActivationSettingDTO.getEnterpriseActivationSettingId(),
                enterpriseData.getId());
        StepRecorder.record("createActivationTask#REGISTERED");
    }

    private void doActionWhenCreateEnterpriseFail(User user, ChannelModel.CreateDataResult createContactResult, RegisterMessage registerMessage, IObjectData objectData) {
        log.info("RegisterPassAction#createEnterpriseRelationWhenRegister 创建互联企业失败");
        channelRegisterService.registerFailedHandler(user, createContactResult.getObjectData());
        StepRecorder.record("registerFailedHandler");
        log.info("RegisterPassAction#createEnterpriseRelationWhenRegister 创建互联企业失败 CRM 提醒");
        channelNotificationService.sendRegisterEnterpriseFailedCrmNotice(user, registerMessage.getOperator(), "create Enterprise link failed", objectData);
        StepRecorder.record("sendRegisterEnterpriseFailedCrmNotice");
    }

    private OuterAccountVo createOuterAccountVo(User user, RegisterMessage registerMessage, ChannelModel.EnterpriseActivationSettingDTO enterpriseActivationSettingDTO, IObjectData objectData, ChannelModel.CreateDataResult createContactResult) {
        ChannelModel.CreateEnterpriseArg createEnterpriseArg = ChannelModel.CreateEnterpriseArg
                .builder()
                .app(registerMessage.getApp())
                .objectApiName(registerMessage.getObjectApiName())
                .defaultRoles(enterpriseActivationSettingDTO.getDefaultRoles())
                .objectData(objectData)
                .contactData(createContactResult.getObjectData())
                .enterpriseType("crm".equals(enterpriseActivationSettingDTO.getEnterpriseType()))
                .build();
        log.info("RegisterPassAction#createEnterpriseRelationWhenRegister 开始创建互联企业");
        try {
            return channelEnterpriseRelationService.createEnterpriseWithRetry(user, createEnterpriseArg);
        } catch (Exception e) {
            log.warn("createEnterpriseWithRetry but failed");
        }
        return null;
    }

    private void doActionWhenCreateContactFail(User user, RegisterMessage registerMessage, ChannelModel.CreateDataResult createContactResult, IObjectData objectData) {
        log.info("RegisterPassAction#execute create Contact failed");
        channelNotificationService.sendCreatePartnerContactFailedCrmNotice(user, registerMessage.getObjectApiName(), registerMessage.getOperator(), createContactResult, objectData);
        StepRecorder.record("sendCreatePartnerContactFailedCrmNotice");
    }

    @NotNull
    private IObjectData assertValidEnterpriseData(User user, String outTenantId) {
        IObjectData enterpriseData = channelEnterpriseRelationService.queryLinkEnterpriseDataByOutTenantId(user, outTenantId);
        if (enterpriseData == null) {
            log.warn("queryLinkEnterpriseDataByOutTenantId but data is null, outTenantId:{}", outTenantId);
            throw new ItemNullException("queryLinkEnterpriseDataByOutTenantId but data is null");
        }
        return enterpriseData;
    }

    private ChannelModel.EnterpriseActivationSettingDTO assertValidEnterpriseActivationSetting(User user, String objectApiName, String dataId) {
        ChannelModel.EnterpriseActivationSettingDTO enterpriseActivationSettingDTO = channelRestService.fetchMatchEnterpriseSetting(user, objectApiName, dataId);
        if (enterpriseActivationSettingDTO == null) {
            log.warn("RegisterPassAction#execute enterpriseActivationSettingDTO is null");
            throw new ItemNullException("enterpriseActivationSettingDTO is null");
        }
        return enterpriseActivationSettingDTO;
    }
}
