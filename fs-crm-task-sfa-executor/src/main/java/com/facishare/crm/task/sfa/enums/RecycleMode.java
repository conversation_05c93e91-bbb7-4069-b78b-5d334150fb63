package com.facishare.crm.task.sfa.enums;

import com.facishare.paas.I18N;
import com.facishare.paas.appframework.core.exception.ValidateException;
import lombok.Getter;

import static com.facishare.crm.sfa.prm.core.constants.PrmI18NConstants.PRM_ENUM_PARAM_TYPE_ERROR;

/**
 * <AUTHOR>
 * @time 2024-07-04 15:24
 * @Description
 */
@Getter
public enum RecycleMode {
    FIXED_DAY("fixed_day"),
    FIXED_DATE("fixed_date");

    private final String mode;

    RecycleMode(String mode) {
        this.mode = mode;
    }

    public static RecycleMode from(String mode) {
        for (RecycleMode e : values()) {
            if (e.mode.equals(mode)) {
                return e;
            }
        }
        throw new ValidateException(I18N.text(PRM_ENUM_PARAM_TYPE_ERROR, mode));
    }

    public static RecycleMode find(String mode) {
        return find(mode, null);
    }

    public static RecycleMode find(String mode, RecycleMode defaultValue) {
        for (RecycleMode e : values()) {
            if (e.mode.equals(mode)) {
                return e;
            }
        }
        return defaultValue;
    }


}
