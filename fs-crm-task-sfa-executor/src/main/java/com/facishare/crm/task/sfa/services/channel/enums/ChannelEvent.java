package com.facishare.crm.task.sfa.services.channel.enums;

import com.facishare.paas.I18N;
import com.facishare.paas.appframework.core.exception.ValidateException;

import static com.facishare.crm.sfa.prm.core.constants.PrmI18NConstants.PRM_ENUM_PARAM_TYPE_ERROR;

/**
 * ============================================================
 *
 * @Description:
 * @CreatedBy: Sundy on 2025-05-20
 * ============================================================
 */
public enum ChannelEvent {
    REGISTER_PASS("register_pass"),
    REGISTER_REJET("register_rejet"),
    SIGN_PASS("sign_pass"),
    SIGN_REJET("sign_rejet"),
    RENEW_PASS("renew_pass"),
    RENEW_REJET("renew_rejet")
    ;

    private final String event;

    ChannelEvent(String event) {
        this.event = event;
    }

    public static ChannelEvent from(String event) {
        for (ChannelEvent e : values()) {
            if (e.event.equals(event)) {
                return e;
            }
        }
        throw new ValidateException(I18N.text(PRM_ENUM_PARAM_TYPE_ERROR, event));
    }

    public static ChannelEvent find(String event) {
        return find(event, null);
    }

    public static ChannelEvent find(String event, ChannelEvent defaultValue) {
        for (ChannelEvent e : values()) {
            if (e.event.equals(event)) {
                return e;
            }
        }
        return defaultValue;
    }
}
