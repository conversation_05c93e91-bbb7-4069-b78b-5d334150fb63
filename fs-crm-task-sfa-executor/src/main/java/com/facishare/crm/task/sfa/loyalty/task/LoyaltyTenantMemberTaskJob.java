package com.facishare.crm.task.sfa.loyalty.task;

import com.alibaba.fastjson.JSON;
import com.facishare.crm.task.sfa.loyalty.model.TaskLoyalty;
import com.facishare.crm.task.sfa.loyalty.task.mq.LoyaltyMqHandler;
import com.fxiaoke.rocketmq.producer.AutoConfMQProducer;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.biz.model.TriggerParam;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHander;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.common.message.Message;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

@Slf4j
@Component
@JobHander(value = "LoyaltyTenantMemberTaskJob")
public class LoyaltyTenantMemberTaskJob extends IJobHandler {

    @Resource
    LoyaltyGrayService loyaltyGrayService;
    @Resource(name = "loyaltyMq")
    AutoConfMQProducer mqProducer;

    @Override
    public ReturnT<String> execute(TriggerParam triggerParam) throws Exception {
        List<String> tenantIdList = loyaltyGrayService.getTenantIdListForCurrentEnv();
        if (!CollectionUtils.isEmpty(tenantIdList)) {
            List<Message> messageList = new ArrayList<>();
            for (String tenantId : tenantIdList) {
                TaskLoyalty.CoreTaskInfo coreTaskInfo = new TaskLoyalty.CoreTaskInfo();
                coreTaskInfo.setTenantId(tenantId);
                messageList.add(new Message(mqProducer.getDefaultTopic(), LoyaltyMqHandler.Tag.tenantMemberTask.name(), JSON.toJSONBytes(coreTaskInfo)));
            }
            mqProducer.send(messageList);
        }
        return ReturnT.SUCCESS;
    }
}
