package com.facishare.crm.task.sfa.follow.model;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class ObjectDescribeChangeMQMessage implements Serializable {
    private String tenantId;
    private String op;
    private String name;
    private List<Content> body;

    @Data
    public static class Content {
        @JSONField(name = "tenant_id")
        private String tenantID;
        @JSONField(name = "describe_api_name")
        private String objectApiName;
        @JSONField(name = "describe_display_name")
        private String objectLabel;
        @JSONField(name = "icon_path")
        private String iconPath;
        @JSONField(name = "name_label")
        private String nameLabel;
    }
}
