package com.facishare.crm.task.sfa.model.qywx;


import com.facishare.common.fsi.ProtoBase;
import io.protostuff.Tag;
import lombok.Data;

@Data
public class ChangeContactEvent extends ProtoBase {
    @Tag(1)
    private String appId;

    @Tag(2)
    private String corpId;

    @Tag(3)
    private String fsEa;

    @Tag(4)
    private String changeType;

    @Tag(5)
    private String userId;//这个是加密id 对应 CIPHER_USER_ID

    @Tag(6)
    private String fsUserId;//纷享员工账号id，不存在绑定关系时为空
}
