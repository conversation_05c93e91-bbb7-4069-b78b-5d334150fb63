package com.facishare.crm.task.sfa.model;

import com.alibaba.fastjson.JSONArray;
import com.facishare.paas.metadata.api.search.Wheres;
import lombok.Data;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;

public interface SuspectedOpportunity {

    String RULE_KEY = "suspected_opportunity_rule_key";

    @Data
    class Rule {
        /**
         * 名称匹配相似度
         */
        Integer nameSimilarity;
        /**
         * 自动关联商机
         */
        Boolean autoRelated;
        /**
         * 自动关联商机相似度, autoRelated=true 且 相似度大于这个自动关联
         */
        Integer autoRelatedSimilarity;
        /**
         * 商机过滤条件
         */
        JSONArray opportunityFilters;
        /**
         * 是否选择使用客户 为空表示使用（兼容老数据）
         */
        Boolean useAccount;

        public List<Wheres> getWheres() {
            List<Wheres> wheresList = new ArrayList<>();
            if (CollectionUtils.isEmpty(opportunityFilters)) {
                return wheresList;
            }
            for (int i = 0; i < opportunityFilters.size(); i++) {
                Wheres wheres = new Wheres();
                wheres.fromJsonString(opportunityFilters.getString(i));
                wheresList.add(wheres);
            }
            return wheresList;
        }
    }
}
