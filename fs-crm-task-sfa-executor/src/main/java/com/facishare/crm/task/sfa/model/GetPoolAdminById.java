package com.facishare.crm.task.sfa.model;

import lombok.Builder;
import lombok.Data;

import java.util.List;

public interface GetPoolAdminById {
    @Data
    @Builder
    class Arg {
        private String apiName;
        private String poolId;
    }

    @Data
    class Result {
        private PoolAdminResult result;

    }

    @Data
    class PoolAdminResult{
        private List<String> adminIds;
    }
}
