package com.facishare.crm.task.sfa.services.channel.core;

import com.facishare.crm.sfa.audit.log.EntityConverter;
import com.facishare.crm.sfa.audit.log.model.AuditArg;
import com.facishare.crm.task.sfa.services.channel.enums.RouterType;

/**
 * ============================================================
 *
 * @Description:
 * @CreatedBy: Sundy on 2025-05-23
 * ============================================================
 */
public class ChannelLogConvert implements EntityConverter<RouterType> {
    @Override
    public AuditArg convert(RouterType routerType) {
        return AuditArg.builder()
                .actionCode(routerType.name())
                .build();
    }
}
