package com.facishare.crm.task.sfa.model;

import lombok.Builder;
import lombok.Data;

@Data
public class FundAccountCancelChargeOffModel {
//    {
//        "objectApiName":"",           //业务对象
//            "objectDataId":"",
//            "chargeOffObjectApiName":""   //红冲对象apiName；流水红冲：AccountTransactionFlowObj；冻结红冲：AccountFrozenRecordObj
//    }
    @Data
    @Builder
    public static class Arg {
        private String objectApiName;
        private String objectDataId;
        private String chargeOffObjectApiName;
    }

    @Data
    public static class Result {
        private Integer errCode;
        private String errMessage;
    }

}