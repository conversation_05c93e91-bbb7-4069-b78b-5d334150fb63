package com.facishare.crm.task.sfa.executor.consumer;

import com.alibaba.fastjson.JSON;
import com.facishare.crm.task.sfa.services.domain.DomainMessage;
import com.facishare.crm.task.sfa.services.domain.DomainPluginManager;
import com.fxiaoke.rocketmq.util.MessageHelper;
import com.github.trace.TraceContext;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.rocketmq.client.consumer.listener.ConsumeConcurrentlyContext;
import org.apache.rocketmq.client.consumer.listener.ConsumeConcurrentlyStatus;
import org.apache.rocketmq.client.consumer.listener.MessageListenerConcurrently;
import org.apache.rocketmq.common.message.MessageExt;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.nio.charset.Charset;
import java.util.List;

/**
 * 插件操作监听类
 */
@Slf4j
@Component
public class DomainPluginListener implements MessageListenerConcurrently {

    @Autowired
    private DomainPluginManager domainPluginManager;

    /**
     * @param {       "tenantId":"74255",
     *                "action":"Add",  操作类型（Add-新建；Edit-编辑；Abolish-禁用；Recover-启用；Delete-删除）
     *                "id":"xxxxxxxx",  插件配置的id（表mt_domain_plugin_instance的id）
     *                "pluginApiName":"test_plugin",  插件的apiName
     *                "objectApiName":"SalesOrderObj",  对象的apiName
     *                "fieldApiName":"field_xxx"    字段的apiName（预留属性，给字段插件使用）
     *                }
     * @param context
     * @return
     */
    @Override
    public ConsumeConcurrentlyStatus consumeMessage(List<MessageExt> msgList, ConsumeConcurrentlyContext context) {
        MessageHelper.fillContextFromMessage(TraceContext.get(), msgList.get(0));
        try {
            for (MessageExt messageExt : msgList) {
                String body = new String(messageExt.getBody(), Charset.forName("UTF-8"));
                DomainMessage actionBody = JSON.parseObject(body, DomainMessage.class);
                if(!StringUtils.equalsIgnoreCase(messageExt.getProperty("sourceApp"),"fs-crm-task-sfa")){
                    try {
                        this.consume(actionBody);
                    } catch (Exception e) {
                        log.error("domainListener参数：{}", body, e);
                    }
                }
            }
        } finally {
            TraceContext.remove();
        }
        return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
    }

    private void consume(DomainMessage message) {
        domainPluginManager.apply(message);
    }


}

