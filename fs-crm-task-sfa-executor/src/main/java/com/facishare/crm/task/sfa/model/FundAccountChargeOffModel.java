package com.facishare.crm.task.sfa.model;

import lombok.Builder;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class FundAccountChargeOffModel {
    //    //流水红冲
//    {
//        "objectApiName":"",  //业务对象
//            "objectDataId":"",
//            "chargeOffObjectApiName":"AccountTransactionFlowObj",
//            "amount":1           //红冲金额
//    }

    public static final String CHARGE_OFF_OBJ = "AccountTransactionFlowObj";
    @Data
    @Builder
    public static class Arg {
        private String objectApiName;
        private String objectDataId;
        private String chargeOffObjectApiName;
        private BigDecimal amount;
    }

    @Data
    public static class Result {
        private Integer errCode;
        private String errMessage;
    }

}