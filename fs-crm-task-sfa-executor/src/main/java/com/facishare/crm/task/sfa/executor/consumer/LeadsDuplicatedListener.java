package com.facishare.crm.task.sfa.executor.consumer;

import com.alibaba.fastjson.JSON;
import com.facishare.crm.task.sfa.common.SfaTaskRateLimiterService;
import com.facishare.crm.task.sfa.model.LeadsDuplicatedProcessingMessage;
import com.facishare.crm.task.sfa.services.LeadsDuplicatedProcessingService;
import com.fxiaoke.rocketmq.consumer.AutoConfMQPushConsumer;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.client.consumer.listener.ConsumeConcurrentlyStatus;
import org.apache.rocketmq.client.consumer.listener.MessageListenerConcurrently;
import org.apache.rocketmq.common.message.MessageExt;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationListener;
import org.springframework.context.event.ContextRefreshedEvent;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;
import java.nio.charset.Charset;

/**
 * <AUTHOR> lik
 * @date : 2022/6/1 15:50
 */
@Slf4j
@Component
public class LeadsDuplicatedListener implements ApplicationListener<ContextRefreshedEvent> {
    private AutoConfMQPushConsumer consumer;
    @Autowired
    private LeadsDuplicatedProcessingService processingService;
    @Autowired
    private SfaTaskRateLimiterService sfaTaskRateLimiterService;

    @PostConstruct
    public void init() {
        consumer = new AutoConfMQPushConsumer("sfa-recalculate-consumer", "sfa-leads-duplicated-new",(MessageListenerConcurrently) (msgs, context) -> {
            if (!msgs.isEmpty()) {
                for(MessageExt msg: msgs) {
                    // 取出traceContext
//                    MessageHelper.fillContextFromMessage(TraceContext.get(), msg);
                    consumeMessage(msg);
                }
            }
            return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
        });
    }

    @Override
    public void onApplicationEvent(ContextRefreshedEvent event) {
        if (consumer != null && event.getApplicationContext().getParent() == null) {
            consumer.start();
        }
    }
    @PreDestroy
    public void close() {
        consumer.close();
    }

    private void consumeMessage(MessageExt messageExt) {
        String body = new String(messageExt.getBody(), Charset.forName("UTF-8"));
        try {
            LeadsDuplicatedProcessingMessage msg = JSON.parseObject(body, LeadsDuplicatedProcessingMessage.class);
            // 增强日志：添加数据ID列表信息，便于追踪
            String dataIdListStr = msg.getDataIdList() != null ? String.join(",", msg.getDataIdList()) : "null";
            log.warn("LeadsDuplicatedListener LeadsDuplicatedProcessingMessage, msgId:{},queue:{},reconsumeTimes:{},tenantId:{},dataIdList:[{}],saveType:{},refreshVersion:{},RecalculateMessage:{}",
                    messageExt.getMsgId(), messageExt.getQueueId(), messageExt.getReconsumeTimes(),
                    msg.getTenantId(), dataIdListStr, msg.getSaveType(), msg.getRefreshVersion(), msg);

            // 对于INVALID类型的消息，如果重试次数过多，直接跳过避免长时间处理
            if("INVALID".equals(msg.getSaveType()) && messageExt.getReconsumeTimes() > 1) {
                log.warn("LeadsDuplicatedListener skip INVALID message due to reconsume times, msgId:{},dataIdList:[{}],saveType:{},reconsumeTimes:{}",
                        messageExt.getMsgId(), dataIdListStr, msg.getSaveType(), messageExt.getReconsumeTimes());
                return;
            }

            if((("NONE".equals(msg.getSaveType()) || msg.getSaveType() == null || "RULE_CHANGE".equals(msg.getSaveType()))
                    && messageExt.getReconsumeTimes() > 0) || messageExt.getReconsumeTimes() > 2) {
                log.info("LeadsDuplicatedListener skip processing due to reconsume times, msgId:{},dataIdList:[{}],saveType:{},reconsumeTimes:{}",
                        messageExt.getMsgId(), dataIdListStr, msg.getSaveType(), messageExt.getReconsumeTimes());
                return;
            }

            // 记录开始处理的日志
            log.info("LeadsDuplicatedListener start processing, msgId:{},tenantId:{},dataIdList:[{}],saveType:{},refreshVersion:{}",
                    messageExt.getMsgId(), msg.getTenantId(), dataIdListStr, msg.getSaveType(), msg.getRefreshVersion());

            if("INVALID".equals(msg.getSaveType())
                    || "OPEN_API_EDIT".equals(msg.getSaveType())
                    || "EDIT".equals(msg.getSaveType())) {
                sfaTaskRateLimiterService.getLeadsDuplicatedSlowMsgLimiter().acquire();
                processingService.execute(msg);
            } else {
                sfaTaskRateLimiterService.getLeadsDuplicatedNormalMsgLimiter().acquire();
                processingService.execute(msg);
            }

            // 记录处理完成的日志
            log.info("LeadsDuplicatedListener processing completed, msgId:{},tenantId:{},dataIdList:[{}],saveType:{}",
                    messageExt.getMsgId(), msg.getTenantId(), dataIdListStr, msg.getSaveType());
        } catch (Exception e) {
            log.error("LeadsDuplicatedListener consumeMessage error, msgId:{},body:{}",messageExt.getMsgId(), body, e);
            throw new RuntimeException(e);
        }
    }
}
