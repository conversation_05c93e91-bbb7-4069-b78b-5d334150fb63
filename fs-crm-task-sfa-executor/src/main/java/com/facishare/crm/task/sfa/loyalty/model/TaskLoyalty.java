package com.facishare.crm.task.sfa.loyalty.model;

import com.facishare.crm.sfa.audit.log.EntityConverter;
import com.facishare.crm.sfa.audit.log.model.AuditArg;
import com.facishare.crm.sfa.lto.loyalty.model.Loyalty;
import com.github.autoconf.helper.ConfigHelper;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

public interface TaskLoyalty {

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    class PointPoolParam {
        private String tenantId;
        /**
         * 成本组织id
         */
        private String orgId;
        /**
         * 门店id
         */
        private String storeId;
        /**
         * 积分分类id
         */
        private String pointTypeId;
    }

    @Data
    class TaskAuditLogConverter implements EntityConverter<String> {

        static String APP_NAME = ConfigHelper.getProcessInfo().getName();

        @Override
        public AuditArg convert(String tenantId) {
            return AuditArg.builder()
                    .appName(APP_NAME)
                    .bizName("loyalty_member_task")
                    .ei(tenantId)
                    .build();
        }
    }

    @Data
    class MemberTaskAuditLogConverter implements EntityConverter<Loyalty.MemberTask> {

        static String APP_NAME = ConfigHelper.getProcessInfo().getName();

        @Override
        public AuditArg convert(Loyalty.MemberTask memberTask) {
            return AuditArg.builder()
                    .appName(APP_NAME)
                    .bizName("loyalty_single_member_task")
                    .ei(memberTask.getTenantId())
                    .messageId(memberTask.getTraceId())
                    .objectIds(memberTask.getMemberId())
                    .build();
        }
    }

    @Data
    @NoArgsConstructor
    class TierBenefitsParam {
        private String tenantId;
        private String tierId;
        private String benefitsType;

        public TierBenefitsParam(String tenantId, String tierId, String benefitsType) {
            this.tenantId = tenantId;
            this.tierId = tierId;
            this.benefitsType = benefitsType;
        }
    }

    @Data
    @NoArgsConstructor
    class CoreTaskInfo {
        private String tenantId;
        private String traceId;
        /**
         * 当前任务执行状态
         * 0.开始执行，更新过期积分状态
         * 1.更新冻结积分状态->可用
         * 2.执行评定日
         */
        private int status;
        /**
         * 过期积分状态更新偏移量
         */
        private String pointsDetailExpiredOffset;
        /**
         * 可用积分状态更新偏移量
         */
        private String pointsDetailAvailableOffset;
        /**
         * 评定日执行偏移量
         */
        private String evaluationDateOffset;
    }

    @Data
    @NoArgsConstructor
    class EvaluationDateTaskInfo {
        private String tenantId;
        private String traceId;
        /**
         * 每个计划一个任务
         */
        private String programId;
        /**
         * 本计划执行的偏移量，会员id
         */
        private String offset;
    }
}