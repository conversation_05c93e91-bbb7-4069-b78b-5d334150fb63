package com.facishare.crm.task.sfa.model;

import java.math.BigDecimal;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PolicyOccupyData {

    private String id;
    private String describeApiName;
    private String orderId;
    private String approvalProcess;
    private String detailLimitId;
    private BigDecimal occupy;
    private String equalId;
}
