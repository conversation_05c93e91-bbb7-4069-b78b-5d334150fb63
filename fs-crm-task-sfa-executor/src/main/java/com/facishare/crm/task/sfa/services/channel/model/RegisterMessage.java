package com.facishare.crm.task.sfa.services.channel.model;

import com.facishare.paas.appframework.core.model.User;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * ============================================================
 *
 * @Description:
 * @CreatedBy: Sundy on 2025-05-20
 * ============================================================
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class RegisterMessage extends BaseMessage {
    private String objectApiName;
    private String dataId;
    private String approvalStatus;
    private String app;
    private String enterpriseActivationSettingId;

    public boolean isPass() {
        return "pass".equals(this.approvalStatus);
    }
}
