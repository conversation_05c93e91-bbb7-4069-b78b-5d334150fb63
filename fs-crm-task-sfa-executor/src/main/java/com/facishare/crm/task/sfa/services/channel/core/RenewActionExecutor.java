package com.facishare.crm.task.sfa.services.channel.core;

import com.facishare.crm.sfa.prm.platform.utils.ObjectUtils;
import com.facishare.crm.task.sfa.services.channel.ActionExecutor;
import com.facishare.crm.task.sfa.services.channel.ChannelMachine;
import com.facishare.crm.task.sfa.services.channel.enums.ChannelEvent;
import com.facishare.crm.task.sfa.services.channel.enums.RouterType;
import com.facishare.crm.task.sfa.services.channel.exception.ItemNullException;
import com.facishare.crm.task.sfa.services.channel.model.ChannelContext;
import com.facishare.crm.task.sfa.services.channel.model.RenewMessage;
import com.facishare.crm.task.sfa.services.channel.model.SignMessage;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.core.model.User;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * ============================================================
 *
 * @Description:
 * @CreatedBy: Sundy on 2025-05-23
 * ============================================================
 */
@Slf4j
@Component
public class RenewActionExecutor implements ActionExecutor {
    @Resource
    private ChannelMachine channelMachine;

    @Override
    public void execute(String body) {
        RenewMessage renewMessage = ObjectUtils.convertObject(body, RenewMessage.class);
        if (renewMessage == null) {
            throw new ItemNullException("renewMessage is null");
        }
        User systemUser = renewMessage.createSystemUser();
        I18N.setContext(systemUser.getTenantId(), renewMessage.getLanguage());
        log.info("RenewActionExecutor#sign mq consume, message:{}", renewMessage);
        log.info("RenewActionExecutor#sign begin");
        ChannelContext channelContext = ChannelContext.builder()
                .user(systemUser)
                .payload(renewMessage)
                .build();
        if (renewMessage.getPass()) {
            channelMachine.getInstance().fire(renewMessage.fetchCurrentStatus(), ChannelEvent.RENEW_PASS, channelContext);
        } else {
            channelMachine.getInstance().fire(renewMessage.fetchCurrentStatus(), ChannelEvent.RENEW_REJET, channelContext);
        }
        log.info("RenewActionExecutor#sign end");
    }

    @Override
    public RouterType getRouterType() {
        return RouterType.RENEW;
    }
}
