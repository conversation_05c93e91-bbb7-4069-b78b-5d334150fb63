package com.facishare.crm.task.sfa.enums;

import com.facishare.paas.I18N;
import com.facishare.paas.appframework.core.exception.ValidateException;
import lombok.Getter;

import static com.facishare.crm.task.sfa.util.constant.I18nKeyConstants.PRM_PARAM_PROTOCOL_ERROR;

/**
 * Created by Sundy on 2024/10/21 11:38
 */
@Getter
public enum IdentityEnums {

    INTERNAL("internal"),
    EXTERNAL("external");

    private final String identity;

    IdentityEnums(String identity) {
        this.identity = identity;
    }
    public static IdentityEnums of(String trigger) {
        for (IdentityEnums e : values()) {
            if (e.identity.equals(trigger)) {
                return e;
            }
        }
        throw new ValidateException(I18N.text(PRM_PARAM_PROTOCOL_ERROR, "identity"));
    }
}
