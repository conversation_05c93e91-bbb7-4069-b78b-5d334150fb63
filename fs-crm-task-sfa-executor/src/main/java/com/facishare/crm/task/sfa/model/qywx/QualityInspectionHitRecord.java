package com.facishare.crm.task.sfa.model.qywx;

import com.alibaba.fastjson.annotation.JSONField;
import com.facishare.crm.sfa.lto.qywx.proxy.models.BaseQywxResult;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

@EqualsAndHashCode(callSuper = true)
@Data
public class QualityInspectionHitRecord extends BaseQywxResult {

    private String fsEa;
    private String qywxCropId;
    private DataItem data;

    @Data
    public static class DataItem {
        @JsonProperty("errcode")
        @JSONField(name = "errcode")
        private Integer errCode;
        @JsonProperty("errmsg")
        @JSONField(name = "errmsg")
        private String errMsg;
        @JsonProperty("has_more")
        @JSONField(name = "has_more")
        private Integer hasMore;
        @JsonProperty("next_cursor")
        @JSONField(name = "next_cursor")
        private String nextCursor;
        @JsonProperty("msg_list")
        @JSONField(name = "msg_list")
        private List<MsgListItem> msgList;
    }

    @Data
    public static class MsgListItem {
        @JsonProperty("msgid")
        @JSONField(name = "msgid")
        private String msgId;
        @JsonProperty("msg_detail")
        @JSONField(name = "msg_detail")
        private MsgDetail msgDetail;
        @JsonProperty("hit_rule_list")
        @JSONField(name = "hit_rule_list")
        private List<HitRuleListItem> hitRuleList;
    }

    @Data
    public static class MsgDetail {
        @JsonProperty("sender")
        @JSONField(name = "sender")
        private Sender sender;
        @JsonProperty("receiver_list")
        @JSONField(name = "receiver_list")
        private List<ReceiverListItem> receiverList;
        @JsonProperty("chatid")
        @JSONField(name = "chatid")
        private String chatId;
        @JsonProperty("send_time")
        @JSONField(name = "send_time")
        private Long sendTime;
        @JsonProperty("msgtype")
        @JSONField(name = "msgtype")
        private Integer msgType;
        @JsonProperty("service_encrypt_info")
        @JSONField(name = "service_encrypt_info")
        private ServiceEncryptInfo serviceEncryptInfo;
    }

    @Data
    public static class Sender {
        @JsonProperty("type")
        @JSONField(name = "type")
        private Integer type;
        @JsonProperty("id")
        @JSONField(name = "id")
        private String id;
    }

    @Data
    public static class ReceiverListItem {
        @JsonProperty("type")
        @JSONField(name = "type")
        private Integer type;
        @JsonProperty("id")
        @JSONField(name = "id")
        private String id;
    }

    @Data
    public static class ServiceEncryptInfo {
        @JsonProperty("encrypted_secret_key")
        @JSONField(name = "encrypted_secret_key")
        private String encryptedSecretKey;
        @JsonProperty("public_key_ver")
        @JSONField(name = "public_key_ver")
        private Integer publicKeyVer;
    }

    @Data
    public static class HitRuleListItem {
        @JsonProperty("rule_id")
        @JSONField(name = "rule_id")
        private String ruleId;
        @JsonProperty("has_hit_keyword")
        @JSONField(name = "has_hit_keyword")
        private Boolean hasHitKeyword;
        @JsonProperty("semantics_list")
        @JSONField(name = "semantics_list")
        private List<Integer> semanticsList;
    }
}
