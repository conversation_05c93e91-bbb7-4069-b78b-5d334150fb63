package com.facishare.crm.task.sfa.model;

import com.alibaba.fastjson.JSONObject;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2020/4/28
 */
@Data
public class PaasObjectChangeMessage {
    private String tenantId;
    private String name;
    private String op;
    private List<ObjectChange> body;

    @Data
    public static class ObjectChange {
        private String eventId;
        private String name;
        private String entityId;
        private String triggerType;
        private String objectId;
        private Context context;
        private boolean batch;
        private JSONObject beforeTriggerData;
        private JSONObject afterTriggerData;
    }

    @Data
    public static class Context {
        private String appId;
        private String tenantId;
        private String userId;
    }
}
