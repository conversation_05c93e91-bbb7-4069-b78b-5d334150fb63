package com.facishare.crm.task.sfa.loyalty.service;

import com.facishare.crm.sfa.lto.loyalty.constants.LoyaltyConstants;
import com.facishare.crm.task.sfa.common.util.Safes;
import com.facishare.crm.task.sfa.loyalty.model.TaskLoyalty;
import com.facishare.crm.task.sfa.util.SearchUtil;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.QueryResult;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.impl.search.OrderBy;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.List;

@Slf4j
@Service
public class LoyaltyPointsPoolService {

    @Resource
    ServiceFacade serviceFacade;

    /**
     * 根据参数查询积分池
     */
    public IObjectData findPointPool(TaskLoyalty.PointPoolParam param) {
        String tenantId = param.getTenantId();
        if (StringUtils.isEmpty(tenantId)) {
            return null;
        }
        String storeId = param.getStoreId();
        String orgId = param.getOrgId();
        if (!StringUtils.isEmpty(storeId)) {
            IObjectData partner = serviceFacade.findObjectDataIgnoreAll(User.systemUser(tenantId), storeId, "PartnerObj");
            if (partner != null) {
                orgId = partner.get("org_id", String.class);
            }
        }
        if (StringUtils.isEmpty(orgId)) {
            return null;
        }
        String pointTypeId = param.getPointTypeId();

        SearchTemplateQuery query = new SearchTemplateQuery();
        List<IFilter> filters = Lists.newArrayList();
        SearchUtil.fillFilterEq(filters, IObjectData.IS_DELETED, 0);
        SearchUtil.fillFilterEq(filters, LoyaltyConstants.LoyaltyPointPool.ENABLE_STATUS, "enable");
        SearchUtil.fillFilterEq(filters, LoyaltyConstants.LoyaltyPointPool.ORG_ID, orgId);
        if (!StringUtils.isEmpty(pointTypeId)) {
            SearchUtil.fillFilterEq(filters, LoyaltyConstants.LoyaltyPointPool.POINT_TYPE_ID, pointTypeId);
        }
        query.setFilters(filters);
        query.setLimit(1);
        query.setOrders(Lists.newArrayList(
                new OrderBy(LoyaltyConstants.LoyaltyPointPool.IS_DEFAULT, false),
                new OrderBy(IObjectData.CREATE_TIME, true)
        ));
        QueryResult<IObjectData> record = serviceFacade.findBySearchQueryIgnoreAll(User.systemUser(tenantId), LoyaltyConstants.LoyaltyPointPool.API_NAME, query);
        if (record != null) {
            return Safes.first(record.getData());
        }
        return null;
    }
}
