package com.facishare.crm.task.sfa.services.channel.actions;

import com.facishare.crm.sfa.prm.api.enums.SignStatus;
import com.facishare.crm.sfa.prm.platform.infrastructure.statemachine.core.StateAction;
import com.facishare.crm.task.sfa.services.ChannelNotificationService;
import com.facishare.crm.task.sfa.services.MetadataServiceExt;
import com.facishare.crm.task.sfa.services.channel.core.ChannelAssertValidator;
import com.facishare.crm.task.sfa.services.channel.model.ChannelContext;
import com.facishare.crm.task.sfa.services.channel.enums.ChannelEvent;
import com.facishare.crm.task.sfa.services.channel.model.RegisterMessage;
import com.facishare.crm.task.sfa.services.channel.utils.StepRecorder;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.IObjectData;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * ============================================================
 *
 * @Description:
 * @CreatedBy: Sundy on 2025-05-20
 * ============================================================
 */
@Component
@Slf4j
public class RegisterRejetAction implements StateAction<SignStatus, ChannelEvent, ChannelContext> {
    @Resource
    private ChannelNotificationService channelNotificationService;
    @Resource
    private MetadataServiceExt metadataServiceExt;
    @Resource
    private ChannelAssertValidator channelAssertValidator;

    @Override
    public void execute(SignStatus sourceState, ChannelEvent event, ChannelContext context) {
        log.info("RegisterRejetAction#execute Register Rejet, sourceState: {}, event: {}, context:{}", sourceState, event, context);
        StepRecorder.record("RegisterRejetAction#execute");
        RegisterMessage registerMessage = (RegisterMessage) context.getPayload();
        User user = context.getUser();
        IObjectData objectData = channelAssertValidator.assertValidData(user, registerMessage.getObjectApiName(), registerMessage.getDataId());
        channelNotificationService.sendRejectRegisterChannelNotice(user, registerMessage.getObjectApiName(), objectData);
        StepRecorder.record("sendRejectNotice");
    }
}
