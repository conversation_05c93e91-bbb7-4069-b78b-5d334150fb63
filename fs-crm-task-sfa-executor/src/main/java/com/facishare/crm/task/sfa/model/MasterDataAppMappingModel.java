package com.facishare.crm.task.sfa.model;

/**
 * <AUTHOR>
 * @time 2023-12-20 11:06
 * @Description
 */
public interface MasterDataAppMappingModel {
    String MASTER_DATA_APP_MAPPING_OBJ = "MasterDataAppMappingObj";
    String ACCOUNT_MAIN_DATA_OBJ = "AccountMainDataObj";
    String ACCOUNT_OBJ = "AccountObj";
    String SOURCE_OBJECT_API_NAME = "source_object_api_name";
    String TARGET_OBJECT_API_NAME = "target_object_api_name";
    String SOURCE_DATA_ID = "source_data_id";
    String TARGET_DATA_ID = "target_data_id";
    String DOWN_STREAM_TENANT_ID = "down_stream_tenant_id";
    String ACCOUNT_MAIN_DATA_ID = "account_main_data_id";
    String CONNECT_CODE = "connect_coding";
}
