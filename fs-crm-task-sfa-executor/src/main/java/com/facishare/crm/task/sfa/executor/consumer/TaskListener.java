package com.facishare.crm.task.sfa.executor.consumer;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONException;
import com.facishare.crm.sfa.audit.log.context.SFALogContext;
import com.facishare.crm.task.sfa.common.SfaTaskRateLimiterService;
import com.facishare.crm.task.sfa.model.*;
import com.facishare.crm.task.sfa.model.qywx.InheritRelateChangeOwnerModel;
import com.facishare.crm.task.sfa.services.*;
import com.facishare.crm.task.sfa.services.automatch.AutoMatcher;
import com.facishare.crm.task.sfa.services.domain.DomainMessage;
import com.facishare.crm.task.sfa.services.domain.PeriodicProductServiceImpl;
import com.facishare.crm.task.sfa.services.job.PartnerLoyaltyCalculationJob;
import com.facishare.crm.task.sfa.services.job.ProductCategoryJobService;
import com.facishare.crm.task.sfa.services.qywx.inherit.InheritScheduledTaskService;
import com.facishare.crm.task.sfa.util.LeadsUtil;
import com.facishare.paas.appframework.common.mq.RocketMQMessageListener;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.metadata.util.SpringUtil;
import com.fxiaoke.release.FsGrayRelease;
import com.fxiaoke.release.FsGrayReleaseBiz;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.common.message.MessageExt;
import org.springframework.beans.factory.annotation.Autowired;

import javax.annotation.Resource;
import java.nio.charset.Charset;
import java.util.List;

import static com.facishare.crm.task.sfa.services.job.ChannelJob.*;
import static com.facishare.crm.task.sfa.services.job.PartnerLoyaltyCalculationJob.PARTNER_LOYALTY_CALCULATION_JOB;

/**
 * <AUTHOR>
 * 执行器接收MQ入口
 */
@Slf4j
public class TaskListener implements RocketMQMessageListener {

    @Resource
    private LeadsOverTimeService leadsOverTimeService;

    @Autowired
    private InheritScheduledTaskService inheritScheduledTaskService;

    @Autowired
    private PeriodicProductServiceImpl periodicProductServiceImpl;

    @Autowired
    private SalesOrderBizService salesOrderBizService;

    @Autowired
    private SfaTaskRateLimiterService sfaTaskRateLimiterService;

    @Autowired
    private InvoiceTransferService invoiceTransferService;

    @Autowired
    private CopyPriceBookProductService copyPriceBookProductService;

    @Autowired
    private SalesOrderProductTransferService salesOrderProductTransferService;

    @Autowired
    PaymentPlanOverTimeService paymentPlanOverTimeService;

    @Autowired
    ProductFiledInitService productFiledInitService;

    @Autowired
    AttributeService attributeService;

    @Autowired
    private ProductUpdateService productUpdateService;

    @Autowired
    ProductStatusChangeService productStatusChangeService;

    @Autowired
    ProjectOverTimeService projectOverTimeService;

    @Autowired
    ProjectStageOverTimeService projectStageOverTimeService;

    @Autowired
    private MarketingAttributionTaskExecutor marketingAttributionTaskExecutor;

    @Autowired
    private OpportunitySaleActionOverTimeService opportunitySaleActionOverTimeService;

    @Autowired
    private SaleActionOverTimeService saleActionOverTimeService;
    @Resource
    private ChannelConsumer channelConsumer;
    @Resource
    private AutoMatcher autoMatcher;
    @Resource
    private PartnerLoyaltyCalculationJob partnerLoyaltyCalculationJob;
    @Resource
    private ProductCategoryConsumer productCategoryConsumer;
    @Resource
    private PeriodicAccountsReceivableService periodicAccountsReceivableService;
    @Resource
    private SaleContractBizService saleContractBizService;

    private FsGrayReleaseBiz dataSfagray = FsGrayRelease.getInstance("sfa");


    @Override
    public void consumeMessage(List<MessageExt> list) {
        if (CollectionUtils.empty(list)) {
            return;
        }

        for (MessageExt message : list) {
            log.debug("message:{}", message);
            consumeMessage(message);
        }
    }

    private void consumeMessage(MessageExt message) {
        String body = new String(message.getBody(), Charset.forName("UTF-8"));
        log.debug("message body:{}", body);
        switch (message.getTags()) {
            case "leads_over_time":
                LeadsOverTimeMessage messageObj = JSON.parseObject(body, LeadsOverTimeMessage.class);
                //跳过企业
                if (LeadsUtil.skipOverTimeTenantId(messageObj.getTenantId())) {
                    return;
                }
                sfaTaskRateLimiterService.getLeadsOverTimeRateLimiter().acquire();
                leadsOverTimeService.execute(messageObj, message.getTags());
                break;
            case "leads_allocate_over_time":
                LeadsOverTimeMessage allocateOverTimeObj = JSON.parseObject(body, LeadsOverTimeMessage.class);
                sfaTaskRateLimiterService.getLeadsAllocateOverTimeRateLimiter().acquire();
                leadsOverTimeService.execute(allocateOverTimeObj, message.getTags());
                break;
            case "leads_allocate":
                try {
                    log.info("queueId:{},messageId:{},messageBody:{}", message.getQueueId(), message.getMsgId(), body);
                    LeadsAutoAllocateMessage msg = JSON.parseObject(body, LeadsAutoAllocateMessage.class);
                    //跳过企业
                    if (LeadsUtil.skipAllocateTenantId(msg.getTenantId())) {
                        return;
                    }
                    SFALogContext.putVariable("bizName", "leads_allocate");
                    ILeadsAutoAllocateService leadsAutoAllocateService = SpringUtil.getContext().getBean(LeadsAutoAllocateService.class);
                    if (msg.getBatch() == 1) {
                        LeadsAutoAllocateManyMessage b_msg = JSON.parseObject(body, LeadsAutoAllocateManyMessage.class);
                        leadsAutoAllocateService.excuteMany(b_msg);
                    } else {
                        leadsAutoAllocateService.execute(msg);
                    }
                    log.info("queueId:{},messageId:{} end", message.getQueueId(), message.getMsgId());
                } catch (JSONException ex) {
                    LeadsAutoAllocateManyMessage msg = JSON.parseObject(body, LeadsAutoAllocateManyMessage.class);
                    ILeadsAutoAllocateService leadsAutoAllocateService = SpringUtil.getContext().getBean(LeadsAutoAllocateService.class);
                    leadsAutoAllocateService.excuteMany(msg);
                    log.error(String.format("json convert error: %s", body));
                } finally {
                    SFALogContext.clearContext();
                }
                break;
            case "invoice_data_transfer_0":
            case "invoice_data_transfer_1":
                InvoiceTransferMessage invoiceTransferMessage = JSON.parseObject(body, InvoiceTransferMessage.class);
                invoiceTransferService.execute(invoiceTransferMessage);
                break;
            case "sales_order_product_data_transfer_0":
            case "sales_order_product_data_transfer_1":
                SalesOrderProductTransferMessage salesOrderProductTransferMessage = JSON.parseObject(body, SalesOrderProductTransferMessage.class);
                salesOrderProductTransferService.execute(salesOrderProductTransferMessage);
                break;
            case "payment_plan_overdue":
                PaymentPlanOverTimeMessage paymentPlanOverTimeMessage = JSON.parseObject(body, PaymentPlanOverTimeMessage.class);
                paymentPlanOverTimeService.execute(paymentPlanOverTimeMessage);
                break;
            case "init_product_is_package_field":
                ProductFieldInitMessage productFieldInitMessage = JSON.parseObject(body, ProductFieldInitMessage.class);
                productFiledInitService.execute(productFieldInitMessage);
                break;
            case "attribute_product":
                log.info("queueId:{},messageId:{},messageBody:{}", message.getQueueId(), message.getMsgId(), body);
                AttributeMessage attributeMessage = JSON.parseObject(body, AttributeMessage.class);
                attributeService.execute(attributeMessage);
                break;
            case "constraint_product_status_change":
                ProductStatusChangeMessage productStatusChangeMessage = JSON.parseObject(body, ProductStatusChangeMessage.class);
                productStatusChangeService.execute(productStatusChangeMessage);
                break;
            case "product_batch":
                ProductUpdateMessage productUpdateMessage = JSON.parseObject(body, ProductUpdateMessage.class);
                productUpdateService.execute(productUpdateMessage);
                break;
            case "project_overdue":
                ProjectOverTimeMessage projectOverTimeMessage = JSON.parseObject(body, ProjectOverTimeMessage.class);
                projectOverTimeService.execute(projectOverTimeMessage);
                break;
            case "project_stage_overdue":
                ProjectStageOverTimeMessage projectStageOverTimeMessage = JSON.parseObject(body, ProjectStageOverTimeMessage.class);
                projectStageOverTimeService.execute(projectStageOverTimeMessage);
                break;
            case "copy_price_book_product":
                CopyPriceBookProductMessage copyPriceBookProductMessage = JSON.parseObject(body, CopyPriceBookProductMessage.class);
                copyPriceBookProductService.execute(copyPriceBookProductMessage);
                break;
            case "marketing_attribution_recalculate":
                MarketingAttributionModel.RecalculateMarketingAttributionMessage reCalculateMarketingAttributionMessage = JSON.parseObject(body, MarketingAttributionModel.RecalculateMarketingAttributionMessage.class);
                marketingAttributionTaskExecutor.execute(reCalculateMarketingAttributionMessage);
                break;
            case "opportunity_saleaction_over_time":
                OpportunitySaleActionOverTimeMessage opportunitySaleActionOverTimeMessage = JSON.parseObject(body, OpportunitySaleActionOverTimeMessage.class);
                opportunitySaleActionOverTimeService.execute(opportunitySaleActionOverTimeMessage);
                break;
            case "saleaction_over_time":
                SaleActionOverTimeMessage saleActionOverTimeMessage = JSON.parseObject(body, SaleActionOverTimeMessage.class);
                saleActionOverTimeService.execute(saleActionOverTimeMessage);
                break;
            case JOB_BIZ_PARTNER_CHANNEL_APPROVAL:
                // 停用企业任务
                channelConsumer.deactivateJobHandle(body);
                break;
            case JOB_BIZ_PARTNER_CHANNEL_REMIND:
                // 签约到期前提醒任务
                channelConsumer.channelRemindJobHandle(body);
                break;
            case "auto_match_note":
                AutoMatchNoteMessage autoMatchNoteMessage = JSON.parseObject(body, AutoMatchNoteMessage.class);
                autoMatcher.match(autoMatchNoteMessage);
                break;
            case ProductCategoryJobService.JOB_BIZ_OBJECTIFY_PRODUCT_CATEGORY:
                // xxl job 产品分类对象化凌晨任务
                productCategoryConsumer.objectifyProductCategoryJobHandle(body);
                break;
            case JOB_PARTNER_CHANNEL_REMINDER_TYPE:
                // xxl job 企业周期性签约，自动提醒任务
                channelConsumer.channelRenewalReminderJobHandle(body);
                break;
            case JOB_PARTNER_CHANNEL_RENEWAL_EXPIRED:
                // xxl job 企业到期未续签 job
                channelConsumer.channelRenewalExpiredJobHandle(body);
                break;
            case "scrm_relate_obj_inherit":
                InheritRelateChangeOwnerModel relateChangeOwnerModel = JSON.parseObject(body, InheritRelateChangeOwnerModel.class);
                inheritScheduledTaskService.consumeInheritRelateChangeOwner(relateChangeOwnerModel);
                break;
            case "periodic_product_plugin_data":
                // 开启周期性产品刷数据
                DomainMessage domainMessage = JSON.parseObject(body, DomainMessage.class);
                periodicProductServiceImpl.handle(domainMessage);
                break;
            case "order_refresh_ship_to_party":
                // 刷订单送达方
                salesOrderBizService.refreshSalesOrderData(body,true);
                break;
            case "periodic_accounts_receivable":
                PeriodicAccountsReceivableMessage periodicAccountsReceivableMessage = JSON.parseObject(body, PeriodicAccountsReceivableMessage.class);
                periodicAccountsReceivableService.execute(periodicAccountsReceivableMessage);
                break;
            case PARTNER_LOYALTY_CALCULATION_JOB:
                // 伙伴忠诚度会员数据统计
                partnerLoyaltyCalculationJob.execute(body);
                break;
            case "contract_goal_snapshot_create":
                saleContractBizService.createContractGoalSnapshot(body);
                break;
        }
    }
}
