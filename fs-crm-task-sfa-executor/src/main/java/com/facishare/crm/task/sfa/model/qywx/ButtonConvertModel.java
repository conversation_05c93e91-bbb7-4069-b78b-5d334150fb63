package com.facishare.crm.task.sfa.model.qywx;

import com.alibaba.fastjson.annotation.JSONField;
import com.facishare.paas.appframework.core.predef.action.AbstractCustomButtonAction;
import com.facishare.paas.appframework.core.predef.action.BaseObjectSaveAction;
import com.facishare.paas.service.model.ObjectDataDocument;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;

public interface ButtonConvertModel {

	@Data
	@Builder
	@AllArgsConstructor
	@NoArgsConstructor
	class Arg {
		private String objectDataId;

		private ObjectDataDocument args;
		private boolean skipTriggerApprovalFlow = false;
		private boolean skipPreFunction = false;
		private boolean skipCheckButtonWheres;

		/**
		 * 转换按钮，指定工商查询接口的数据源
		 * <p>
		 * 邓白氏 DengBaiShi
		 * 天眼查 TianYanCha
		 */
		private String industryDatasource;
		/**
		 * 转换按钮、工商查询接口区分补充查询和高级查询
		 */
		private String industrySupplyTag;

		private AbstractCustomButtonAction.QueryParam queryParam;
	}

	@Data
	@Builder
	@NoArgsConstructor
	@AllArgsConstructor
	class Result {
		private ObjectDataDocument objectData;

		private Map<String, List<ObjectDataDocument>> details;
		private String targetDescribeApiName;

		private boolean hasReturnValue;

		private Object returnValue;

		private String returnType;

		private BaseObjectSaveAction.ValidationMessage validationMessage;
	}

	@Data
	class ResultWrapper {
		private Result result;
	}
}
