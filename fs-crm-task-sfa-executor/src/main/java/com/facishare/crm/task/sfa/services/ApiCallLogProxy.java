package com.facishare.crm.task.sfa.services;

import com.alibaba.fastjson.TypeReference;
import com.facishare.crm.sfa.prm.platform.model.RestResponse;
import com.facishare.crm.task.sfa.model.ApiCallLogRequest;
import com.facishare.crm.task.sfa.rest.ApiCallLogRestAPI;
import com.facishare.crm.task.sfa.util.HttpHeaderUtil;
import com.facishare.paas.appframework.core.model.User;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * ============================================================
 *
 * @Description:
 * @CreatedBy: Sundy on 2025-06-13
 * ============================================================
 */
@Service
public class ApiCallLogProxy {
    @Resource
    private ApiCallLogRestAPI apiCallLogRestAPI;

    public Boolean saveLog(User user, ApiCallLogRequest apiCallLogRequest) {
        RestResponse restResponse = apiCallLogRestAPI.saveLog(HttpHeaderUtil.getHeaders(user), apiCallLogRequest);
        if (restResponse == null || !restResponse.isSuccess()) {
            return false;
        }
        return restResponse.getResponse(() -> new TypeReference<Boolean>() {
        });
    }
}
