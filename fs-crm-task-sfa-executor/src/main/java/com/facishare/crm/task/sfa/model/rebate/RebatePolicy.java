package com.facishare.crm.task.sfa.model.rebate;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.facishare.crm.task.sfa.common.constants.RebatePolicyConstants;
import com.facishare.crm.task.sfa.services.rebate.util.ExceptionUtils;
import com.facishare.crm.task.sfa.services.rebate.util.MarketingInitUtil;
import com.facishare.crm.task.sfa.util.DateUtils;
import com.facishare.paas.metadata.api.IObjectData;
import com.google.common.collect.Lists;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.*;

/**
 * 返利产生政策
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class RebatePolicy {
    private String tenantId;
    private String id;
    private String name;
    private Long startDate;
    private Long endDate;
    private String accountType;
    private String accountRange;
    private String executeMode;
    private Long specifyCreateDate;
    private String calculateRange;
    private String rebateBasis;
    private int priority;
    private Long lastModifiedTime;
    private int cycleCount;
    private int executeCount;
    private String cycleInfo;
    private String rebateDimension;
    private IObjectData srcData;
    @Builder.Default
    private List<RebatePolicyRule> rules = Lists.newArrayList();
    /**
     * 条件字段
     */
    private Map<String, Set<String>> conditionFieldMap;
    /**
     * 是否有规则条件
     */
    private Boolean hasRuleCondition;

    /**
     * 规则条件中引用的聚合规则id
     */
    private Set<String> aggRuleIds;

    /**
     * 是否在有效期
     *
     * @return
     */
    public boolean inValidityPeriod() {
        return MarketingInitUtil.inValidityPeriod(startDate, endDate);
    }

    /**
     * 是否在指定日期
     *
     * @return
     */
    public boolean isSpecifyCreateDate() {
        return RebatePolicyConstants.ExecuteMode.SPECIFY.getValue().equals(executeMode) &&
                DateUtils.isSameDate(new Date(), new Date(specifyCreateDate));
    }

    /**
     * 是否周期性执行日期执行
     *
     * @return
     */
    public boolean isCycleExecDate() {
        //  周期性产生判断。
        if (!RebatePolicyConstants.ExecuteMode.CYCLE.getValue().equals(executeMode)) return false;

        JSONObject rebateUsedDateJsonObject = ExceptionUtils.trySupplier(() -> JSON.parseObject(cycleInfo));
        String type = rebateUsedDateJsonObject.getString("type");
        if (RebatePolicyConstants.CycleType.DAY.getValue().equals(type)) {
            return true;
        }

        int month = rebateUsedDateJsonObject.getIntValue("month");
        int day = rebateUsedDateJsonObject.getIntValue("day");
        if (cycleCount != 0 && executeCount >= cycleCount) {
            return false;
        }

        Date execDate = new Date();
        Calendar.getInstance();
        if (RebatePolicyConstants.CycleType.DAY.getValue().equals(type)) {
            return true;
        } else if (RebatePolicyConstants.CycleType.WEEK.getValue().equals(type)) {
            execDate = DateUtils.getWeekOfDates(day);
        } else if (RebatePolicyConstants.CycleType.MONTH.getValue().equals(type)) {
            execDate = DateUtils.getMonthOfDates(day);
        } else if (RebatePolicyConstants.CycleType.QUARTER.getValue().equals(type)) {
            execDate = DateUtils.getQuarterOfDates(month, day);
        } else if (RebatePolicyConstants.CycleType.YEAR.getValue().equals(type)) {
            execDate = DateUtils.getYearOfDates(month, day);
        }

        return DateUtils.isSameDate(new Date(), execDate);

    }
}
