package com.facishare.crm.task.sfa.model;

import com.facishare.paas.metadata.api.IObjectData;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.*;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @time 2023-12-01 15:12
 * @Description
 */
public interface PrmManagementModel {
    @Data
    class NoticeConfigArg {
        private NoticeConfig activateConfig;
        private NoticeConfig deactivateConfig;
    }

    @Data
    class NoticeConfigResult {
        private Boolean valid;
        private NoticeConfig activateConfig;
        private NoticeConfig deactivateConfig;
    }

    @Data
    class NoticeConfigData {
        private IObjectData activateConfig;
        private IObjectData deactivateConfig;
    }

    @Builder
    @Getter
    @Setter
    class SmsResult {
        private String activateSmsId;
        private String deActivateSmsId;
    }

    @Builder
    @Getter
    @Setter
    @AllArgsConstructor
    @NoArgsConstructor
    class NoticeConfig {
        @Builder.Default
        private String configId = "";
        // 启用配置还是停用配置
        @Builder.Default
        private String configType = "";

        @Builder.Default
        private String smsId = "";

        @Builder.Default
        private NoticeRange noticeRange = NoticeRange.builder().build();

        @Builder.Default
        private Boolean enable = Boolean.FALSE;
        // 固定 sms
        @Builder.Default
        private List<String> noticeTypes = Lists.newArrayList("sms");
        @Builder.Default
        private ShortMessage shortMessage = ShortMessage.builder().build();
        private String owner;
    }

    @Builder
    @Getter
    @Setter
    @AllArgsConstructor
    @NoArgsConstructor
    class NoticeRange {
        @Builder.Default
        List<String> outerTenants = Lists.newArrayList();
        @Builder.Default
        List<String> outerGroup = Lists.newArrayList();
        @Builder.Default
        Boolean includeAllTenant = false;
    }

    @Builder
    @Getter
    @Setter
    @AllArgsConstructor
    @NoArgsConstructor
    class ShortMessage {
        @Builder.Default
        private String smsId = "";
        @Builder.Default
        private String templateId = "";
        @Builder.Default
        private String smsType = "";
        @Builder.Default
        private String content = "";
        @Builder.Default
        private List<PartnerManagement.ContentParam> smsContentParam = Lists.newArrayList();

    }

    @Builder
    @Getter
    @Setter
    @AllArgsConstructor
    @NoArgsConstructor
    class NoticeRangeDiff {
        @Builder.Default
        private RangeDiff active = RangeDiff.builder().build();
        @Builder.Default
        private RangeDiff deactivate = RangeDiff.builder().build();
        @Builder.Default
        private boolean changed = false;
    }
    @Builder
    @Getter
    @Setter
    @AllArgsConstructor
    @NoArgsConstructor
    class RangeDiff {
        @Builder.Default
        private List<String> addRange = Lists.newArrayList();
        @Builder.Default
        private List<String> deleteRange = Lists.newArrayList();
    }

    @Builder
    @Getter
    @Setter
    @AllArgsConstructor
    @NoArgsConstructor
    class OuterUserReference {
        private String outerTenant;
        /**
         * 外部人员 mapping 应用权限引用个数
         */
        @Builder.Default
        Map<String, Integer> outerUserReference = Maps.newHashMap();
    }

    @Builder
    @Getter
    @Setter
    @AllArgsConstructor
    @NoArgsConstructor
    class OuterUser {
        @Builder.Default
        private String outerTenantId = "";
        @Builder.Default
        private String outerUserId = "";
        @Builder.Default
        private String phoneNumber = "";
    }

}
