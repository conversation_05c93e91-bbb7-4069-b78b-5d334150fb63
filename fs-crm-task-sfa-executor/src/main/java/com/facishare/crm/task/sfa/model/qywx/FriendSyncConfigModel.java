package com.facishare.crm.task.sfa.model.qywx;

import com.alibaba.fastjson.JSON;
import java.util.List;
import lombok.Builder;
import lombok.Data;

import java.util.HashMap;
import java.util.Map;

import static com.facishare.crm.task.sfa.model.qywx.FriendSyncConfigConstant.ACCOUNT_CHECK_DUPLICATE;
import static com.facishare.crm.task.sfa.model.qywx.FriendSyncConfigConstant.ACCOUNT_DUPLICATE_CONFIG;
import static com.facishare.crm.task.sfa.model.qywx.FriendSyncConfigConstant.ACCOUNT_DUPLICATE_HANDLE_MODE;
import static com.facishare.crm.task.sfa.model.qywx.FriendSyncConfigConstant.ACCOUNT_SYNC_CONDITION;
import static com.facishare.crm.task.sfa.model.qywx.FriendSyncConfigConstant.ACCOUNT_SYNC_TIMING;
import static com.facishare.crm.task.sfa.model.qywx.FriendSyncConfigConstant.CONTACT_CHECK_DUPLICATE;
import static com.facishare.crm.task.sfa.model.qywx.FriendSyncConfigConstant.CONTACT_DUPLICATE_CONFIG;
import static com.facishare.crm.task.sfa.model.qywx.FriendSyncConfigConstant.CONTACT_DUPLICATE_HANDLE_MODE;
import static com.facishare.crm.task.sfa.model.qywx.FriendSyncConfigConstant.CONTACT_SYNC_CONDITION;
import static com.facishare.crm.task.sfa.model.qywx.FriendSyncConfigConstant.CONTACT_SYNC_TIMING;
import static com.facishare.crm.task.sfa.model.qywx.FriendSyncConfigConstant.LEADS_CHECK_DUPLICATE;
import static com.facishare.crm.task.sfa.model.qywx.FriendSyncConfigConstant.LEADS_DUPLICATE_CONFIG;
import static com.facishare.crm.task.sfa.model.qywx.FriendSyncConfigConstant.LEADS_DUPLICATE_HANDLE_MODE;
import static com.facishare.crm.task.sfa.model.qywx.FriendSyncConfigConstant.LEADS_SYNC_CONDITION;
import static com.facishare.crm.task.sfa.model.qywx.FriendSyncConfigConstant.LEADS_SYNC_TIMING;
import static com.facishare.crm.task.sfa.model.qywx.FriendSyncConfigConstant.SYNC_TO_ACCOUNT;
import static com.facishare.crm.task.sfa.model.qywx.FriendSyncConfigConstant.SYNC_TO_CONTACT;
import static com.facishare.crm.task.sfa.model.qywx.FriendSyncConfigConstant.SYNC_TO_LEADS;


public interface FriendSyncConfigModel {

	@Data
	class SaveArg {
		private FriendSyncConfigInfo configInfo;
	}

	@Builder
	class SaveResult {

	}



	class QueryArg {

	}


	@Data
	class QueryResultWrapper {
		private QueryResult result;
	}

	@Data
	class QueryResult {
		private FriendSyncConfigInfo configInfo;
	}

	@Data
	class SearchResultWrapper {
		private SearchResult result;
	}
	@Data
	class SearchResult {
		private boolean syncLeads;
		private List<FriendSyncRuleInfo> leads;
		private boolean syncAccount;
		private List<FriendSyncRuleInfo> account;
		private boolean syncContact;
		private List<FriendSyncRuleInfo> contact;
	}

	@Data
	class FriendSyncRuleInfo {
		/**
		 * 同步时机
		 * 直接同步：DIRECT
		 * 满足条件：CONDITION
		 * APL名：APL
		 */
		private String syncTiming;
		/**
		 * 同步条件
		 */
		private String syncCondition;
		/**
		 * 按APL同步
		 */
		private String syncAPLName;
		/**
		 * 同步至目标对象
		 * LeadsObj：销售线索（默认），LeadsPoolObj：线索池，
		 * AccountObj：客户（默认），HighSeasObj：公海池，
		 * ContactObj：联系人
		 */
		private String syncTargetObjName;
		/**
		 * 同步的业务类型
		 */
		private String syncRecordType;
		/**
		 * 同步的关联池
		 */
		private String syncPoolId;

		private String userId;
	}

	@Data
	class FriendSyncConfigInfo {
		/**
		 * 是否开启自动同步至线索
		 */
		private boolean syncToLeads;
		/**
		 * 线索自动同步时机
		 * 直接同步：DIRECT
		 * 满足条件：CONDITION
		 */
		private SyncTiming leadsSyncTiming;
		/**
		 * 线索自动同步条件
		 */
		private String leadsSyncCondition;
		private boolean syncToAccount;
		private SyncTiming accountSyncTiming;
		private String accountSyncCondition;
		private boolean syncToContact;
		private SyncTiming contactSyncTiming;
		private String contactSyncCondition;
		/**
		 * 是否开启线索查重
		 */
		private boolean leadsCheckDuplicate;
		/**
		 * 线索查重配置
		 */
		private DuplicateSetting leadsDuplicateConfig;
		private boolean accountCheckDuplicate;
		private DuplicateSetting accountDuplicateConfig;
		private boolean contactCheckDuplicate;
		private DuplicateSetting contactDuplicateConfig;

		private DuplicateHandleMode leadsDuplicateHandleMode;
		private DuplicateHandleMode accountDuplicateHandleMode;
		private DuplicateHandleMode contactDuplicateHandleMode;
	}


	enum SyncTiming {
		DIRECT,
		CONDITION,
		APL
	}

	enum DuplicateHandleMode {
		CONTINUE_TO_CREATE,
		BLOCK,
		ASSOCIATION
	}

	@Data
	class DuplicateSetting {
		private boolean byPhone; //通过手机号查重
		private boolean byTel; //通过电话查重
		private boolean byOwner; // 通过负责人查重
		private boolean byMembership; //通过团队成员查重
		private boolean byOrganization; // 通过归属组织查重
	}


	static Map<String, Object> configInfoToMap(FriendSyncConfigInfo configInfo) {
		Map<String, Object> map = new HashMap<>();
		map.put(SYNC_TO_LEADS, configInfo.isSyncToLeads());
		map.put(LEADS_SYNC_TIMING, configInfo.getLeadsSyncTiming() != null ? configInfo.getLeadsSyncTiming().name() : null);
		map.put(LEADS_SYNC_CONDITION, configInfo.getLeadsSyncCondition());
		map.put(SYNC_TO_ACCOUNT, configInfo.isSyncToAccount());
		map.put(ACCOUNT_SYNC_TIMING, configInfo.getAccountSyncTiming() != null ? configInfo.getAccountSyncTiming().name() : null);
		map.put(ACCOUNT_SYNC_CONDITION, configInfo.getAccountSyncCondition());
		map.put(SYNC_TO_CONTACT, configInfo.isSyncToContact());
		map.put(CONTACT_SYNC_TIMING, configInfo.getContactSyncTiming() != null ? configInfo.getContactSyncTiming().name() : null);
		map.put(CONTACT_SYNC_CONDITION, configInfo.getContactSyncCondition());
		map.put(LEADS_CHECK_DUPLICATE, configInfo.isLeadsCheckDuplicate());
		map.put(LEADS_DUPLICATE_CONFIG, configInfo.getLeadsDuplicateConfig() != null ? JSON.toJSONString(configInfo.getLeadsDuplicateConfig()) : null);
		map.put(ACCOUNT_CHECK_DUPLICATE, configInfo.isAccountCheckDuplicate());
		map.put(ACCOUNT_DUPLICATE_CONFIG, configInfo.getAccountDuplicateConfig() != null ? JSON.toJSONString(configInfo.getAccountDuplicateConfig()) : null);
		map.put(CONTACT_CHECK_DUPLICATE, configInfo.isContactCheckDuplicate());
		map.put(CONTACT_DUPLICATE_CONFIG, configInfo.getContactDuplicateConfig() != null ? JSON.toJSONString(configInfo.getContactDuplicateConfig()) : null);
		map.put(LEADS_DUPLICATE_HANDLE_MODE, configInfo.getLeadsDuplicateHandleMode() != null ? configInfo.getLeadsDuplicateHandleMode().name() : null);
		map.put(ACCOUNT_DUPLICATE_HANDLE_MODE, configInfo.getAccountDuplicateHandleMode() != null ? configInfo.getAccountDuplicateHandleMode().name() : null);
		map.put(CONTACT_DUPLICATE_HANDLE_MODE, configInfo.getContactDuplicateHandleMode() != null ? configInfo.getContactDuplicateHandleMode().name() : null);
		return map;
	}


	static FriendSyncConfigInfo mapToConfigInfo(Map map) {
		FriendSyncConfigInfo configInfo = new FriendSyncConfigInfo();
		configInfo.setSyncToLeads((Boolean) map.get(SYNC_TO_LEADS));
		configInfo.setLeadsSyncTiming(map.get(LEADS_SYNC_TIMING) != null ? SyncTiming.valueOf((String) map.get(LEADS_SYNC_TIMING)) : null);
		configInfo.setLeadsSyncCondition((String) map.get(LEADS_SYNC_CONDITION));
		configInfo.setSyncToAccount((Boolean) map.get(SYNC_TO_ACCOUNT));
		configInfo.setAccountSyncTiming(map.get(ACCOUNT_SYNC_TIMING) != null ? SyncTiming.valueOf((String) map.get(ACCOUNT_SYNC_TIMING)) : null);
		configInfo.setAccountSyncCondition((String) map.get(ACCOUNT_SYNC_CONDITION));
		configInfo.setSyncToContact((Boolean) map.get(SYNC_TO_CONTACT));
		configInfo.setContactSyncTiming(map.get(CONTACT_SYNC_TIMING) != null ? SyncTiming.valueOf((String) map.get(CONTACT_SYNC_TIMING)) : null);
		configInfo.setContactSyncCondition((String) map.get(CONTACT_SYNC_CONDITION));
		configInfo.setLeadsCheckDuplicate((Boolean) map.get(LEADS_CHECK_DUPLICATE));
		configInfo.setLeadsDuplicateConfig(map.get(LEADS_DUPLICATE_CONFIG) != null ? JSON.parseObject((String) map.get(LEADS_DUPLICATE_CONFIG), DuplicateSetting.class) : null);
		configInfo.setAccountCheckDuplicate((Boolean) map.get(ACCOUNT_CHECK_DUPLICATE));
		configInfo.setAccountDuplicateConfig(map.get(ACCOUNT_DUPLICATE_CONFIG) != null ? JSON.parseObject((String) map.get(ACCOUNT_DUPLICATE_CONFIG), DuplicateSetting.class) : null);
		configInfo.setContactCheckDuplicate((Boolean) map.get(CONTACT_CHECK_DUPLICATE));
		configInfo.setContactDuplicateConfig(map.get(CONTACT_DUPLICATE_CONFIG) != null ? JSON.parseObject((String) map.get(CONTACT_DUPLICATE_CONFIG), DuplicateSetting.class) : null);
		configInfo.setLeadsDuplicateHandleMode(map.get(LEADS_DUPLICATE_HANDLE_MODE) != null ? DuplicateHandleMode.valueOf((String) map.get(LEADS_DUPLICATE_HANDLE_MODE)) : null);
		configInfo.setAccountDuplicateHandleMode(map.get(ACCOUNT_DUPLICATE_HANDLE_MODE) != null ? DuplicateHandleMode.valueOf((String) map.get(ACCOUNT_DUPLICATE_HANDLE_MODE)) : null);
		configInfo.setContactDuplicateHandleMode(map.get(CONTACT_DUPLICATE_HANDLE_MODE) != null ? DuplicateHandleMode.valueOf((String) map.get(CONTACT_DUPLICATE_HANDLE_MODE)) : null);
		return configInfo;
	}



}
