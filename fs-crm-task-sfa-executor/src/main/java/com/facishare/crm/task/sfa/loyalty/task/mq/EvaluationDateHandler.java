package com.facishare.crm.task.sfa.loyalty.task.mq;

import com.alibaba.fastjson.JSON;
import com.facishare.crm.sfa.lto.loyalty.constants.LoyaltyConstants;
import com.facishare.crm.sfa.lto.loyalty.utils.LoyaltyThreshold;
import com.facishare.crm.task.sfa.loyalty.model.TaskLoyalty;
import com.facishare.crm.task.sfa.loyalty.task.LoyaltyEvaluationDateTaskService;
import com.facishare.crm.task.sfa.util.SearchUtil;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.QueryResult;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.fxiaoke.rocketmq.producer.AutoConfMQProducer;
import com.fxiaoke.rocketmq.producer.DefaultTopicMessage;
import com.github.trace.TraceContext;
import com.google.common.collect.Lists;
import org.apache.rocketmq.common.message.Message;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

@Service
public class EvaluationDateHandler extends LoyaltyMqHandler {

    @Resource
    LoyaltyEvaluationDateTaskService loyaltyEvaluationDateTaskService;
    @Resource
    ServiceFacade serviceFacade;
    @Resource(name = "loyaltyMq")
    AutoConfMQProducer mqProducer;

    @Override
    public Tag getTag() {
        return Tag.tenantEvaluationDate;
    }

    @Override
    public void handler(String body) {
        TaskLoyalty.EvaluationDateTaskInfo evaluationDateTaskInfo = JSON.parseObject(body, TaskLoyalty.EvaluationDateTaskInfo.class);
        String tenantId = evaluationDateTaskInfo.getTenantId();
        if (!enable(tenantId)) {
            return;
        }
        String traceId = TraceContext.get().getTraceId();
        if (StringUtils.isEmpty(evaluationDateTaskInfo.getTraceId())) {
            evaluationDateTaskInfo.setTraceId(traceId);
        } else {
            TraceContext.get().setTraceId(evaluationDateTaskInfo.getTraceId());
        }
        log.info("会员计算评定日定时任务-开始.param:{}", JSON.toJSONString(evaluationDateTaskInfo));
        //没有计划id 则执行全部计划
        if (StringUtils.isEmpty(evaluationDateTaskInfo.getProgramId())) {
            List<Message> messageList = new ArrayList<>();
            List<IObjectData> programList = findProgramList(tenantId);
            for (IObjectData program : programList) {
                TaskLoyalty.EvaluationDateTaskInfo taskInfo = new TaskLoyalty.EvaluationDateTaskInfo();
                taskInfo.setTraceId(evaluationDateTaskInfo.getTraceId());
                taskInfo.setTenantId(tenantId);
                taskInfo.setProgramId(program.getId());
                messageList.add(new Message(mqProducer.getDefaultTopic(), LoyaltyMqHandler.Tag.tenantEvaluationDate.name(), JSON.toJSONBytes(taskInfo)));
            }
            log.info("会员计算评定日定时任务-开始.tenantId:[{}],programCount:[{}],param:{}", tenantId, programList.size(), JSON.toJSONString(evaluationDateTaskInfo));
            if (!CollectionUtils.isEmpty(programList)) {
                mqProducer.send(messageList);
            }
        } else {
            String programId = evaluationDateTaskInfo.getProgramId();
            List<IFilter> filters = Lists.newArrayList();
            if (!StringUtils.isEmpty(evaluationDateTaskInfo.getOffset())) {
                SearchUtil.fillFilterGT(filters, IObjectData.ID, evaluationDateTaskInfo.getOffset());
            }
            List<IObjectData> dataList = loyaltyEvaluationDateTaskService.evaluationDateTask(tenantId, programId, filters, false);
            if (CollectionUtils.isEmpty(dataList)) {
                log.info("会员计算评定日定时任务-结束.tenantId:[{}],programId:[{}]", tenantId, programId);
            } else {
                IObjectData lastData = dataList.get(dataList.size() - 1);
                evaluationDateTaskInfo.setOffset(lastData.getId());
                mqProducer.send(new DefaultTopicMessage(LoyaltyMqHandler.Tag.tenantEvaluationDate.name(), JSON.toJSONBytes(evaluationDateTaskInfo)));
            }
        }
    }

    public List<IObjectData> findProgramList(String tenantId) {
        SearchTemplateQuery searchTemplateQuery = new SearchTemplateQuery();
        List<IFilter> filters = Lists.newArrayList();
        SearchUtil.fillFilterEq(filters, IObjectData.IS_DELETED, 0);
        SearchUtil.fillFilterEq(filters, LoyaltyConstants.LoyaltyProgram.ENABLE_STATUS, "enable");
        searchTemplateQuery.setFilters(filters);
        searchTemplateQuery.setLimit(LoyaltyThreshold.getCommonMaxSize());
        QueryResult<IObjectData> queryResult = serviceFacade.findBySearchQuery(User.systemUser(tenantId), LoyaltyConstants.LoyaltyProgram.API_NAME, searchTemplateQuery);
        if (queryResult == null || CollectionUtils.isEmpty(queryResult.getData())) {
            return Lists.newArrayList();
        }
        return queryResult.getData();
    }
}
