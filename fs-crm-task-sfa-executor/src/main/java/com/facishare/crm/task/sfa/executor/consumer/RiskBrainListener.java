package com.facishare.crm.task.sfa.executor.consumer;

import cn.com.antcloud.api.riskplus.v1_0.response.QueryGeneralResponse;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.facishare.crm.sfa.lto.riskbrain.service.RiskBrainCommonService;
import com.facishare.crm.task.sfa.mq.integral.TenantUtils;
import com.facishare.crm.task.sfa.services.riskbrain.RiskBrainAsyncService;
import com.facishare.paas.appframework.common.util.ParallelUtils;
import com.github.trace.TraceContext;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.rocketmq.client.consumer.listener.ConsumeConcurrentlyStatus;
import org.apache.rocketmq.client.consumer.listener.MessageListenerConcurrently;
import org.apache.rocketmq.common.message.MessageExt;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.nio.charset.StandardCharsets;
import java.util.UUID;

@Slf4j
@Component
public class RiskBrainListener extends AbstractMqListener {

    @Autowired
    RiskBrainAsyncService riskBrainAsyncService;
    @Autowired
    RiskBrainCommonService riskBrainCommonService;
    @Autowired
    TenantUtils tenantUtils;

    @Override
    public String configName() {
        return "sfa-recalculate-consumer";
    }

    @Override
    public String sectionNames() {
        return "risk-brain";
    }

    @Override
    public MessageListenerConcurrently handle() {
        return (messagetList, context) -> {
            if (!CollectionUtils.isEmpty(messagetList)) {
                for (MessageExt msg : messagetList) {
                    ParallelUtils.ParallelTask parallelTask = ParallelUtils.createBackgroundTask();
                    parallelTask.submit(() -> {
                        try {
                            String traceId = UUID.randomUUID().toString().replace("-", "");
                            TraceContext.get().setTraceId(traceId);
                            String tenantId = new String(msg.getBody(), StandardCharsets.UTF_8);
                            log.info("风险画像定时任务：{}", tenantId);
                            if (tenantUtils.notTheCurrentEnv(tenantId)) {
                                return;
                            }
                            if (expired(tenantId)) {
                                return;
                            }
                            if ("risk-brain-change-info-remind".equals(msg.getTags())) {
                                riskBrainAsyncService.createTaskForChangeInfoRemind(tenantId);
                                riskBrainAsyncService.remindHandle(tenantId);
                            } else if ("risk-brain-account-info-sync".equals(msg.getTags())) {
                                riskBrainAsyncService.createTaskForInfoSync(tenantId);
                                riskBrainAsyncService.syncRiskInfo(tenantId);
                            }
                        } catch (Exception e) {
                            log.error("蚂蚁风险定时任务执行异常", e);
                        }
                    });
                    parallelTask.run();
                }
            }
            return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
        };
    }

    /**
     * 通过判断当前是否存在有效订单，来判断是否过期
     */
    public boolean expired(String tenantId) {
        try {
            JSONObject request = new JSONObject();
            request.put("clientName", tenantId);
            QueryGeneralResponse response = riskBrainCommonService.queryGeneral("irap.account.order.query", request);
            if (!response.isSuccess()) {
                return true;
            }
            JSONObject responseData = JSON.parseObject(response.getData());
            JSONObject queryResult = responseData.getJSONObject("queryResult");
            String orderId = queryResult.getString("orderId");
            return StringUtils.isEmpty(orderId);
        } catch (Exception e) {
            log.error("蚂蚁风险判断过期异常", e);
            return true;
        }
    }
}
