package com.facishare.crm.task.sfa.services.channel.actions;

import com.facishare.crm.sfa.prm.api.channel.AgreementStatusRecordService;
import com.facishare.crm.sfa.prm.api.channel.ChannelDataChangeService;
import com.facishare.crm.sfa.prm.api.enums.ScheduleType;
import com.facishare.crm.sfa.prm.api.enums.SignStatus;
import com.facishare.crm.sfa.prm.core.service.TimeComputeService;
import com.facishare.crm.sfa.prm.platform.enums.TimeUnit;
import com.facishare.crm.sfa.prm.platform.infrastructure.statemachine.core.StateAction;
import com.facishare.crm.sfa.prm.platform.utils.DataUtils;
import com.facishare.crm.task.sfa.enums.BizScopeEnums;
import com.facishare.crm.task.sfa.enums.ChannelStage;
import com.facishare.crm.task.sfa.enums.ReminderTriggerEnums;
import com.facishare.crm.task.sfa.model.ChannelModel;
import com.facishare.crm.task.sfa.services.*;
import com.facishare.crm.task.sfa.services.channel.core.ChannelAssertValidator;
import com.facishare.crm.task.sfa.services.channel.enums.ChannelEvent;
import com.facishare.crm.task.sfa.services.channel.exception.ItemNullException;
import com.facishare.crm.task.sfa.services.channel.model.ChannelContext;
import com.facishare.crm.task.sfa.services.channel.model.RenewMessage;
import com.facishare.crm.task.sfa.services.channel.utils.StepRecorder;
import com.facishare.crm.task.sfa.services.job.ChannelJob;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.IObjectData;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

import static com.facishare.crm.sfa.prm.core.constants.ChannelConstants.SIGNING_STATUS;
import static com.facishare.crm.task.sfa.common.util.I18NKeyUtil.SFA_CHANNEL_RENEWAL_SUCCESSFUL_TITLE;

/**
 * ============================================================
 *
 * @Description:
 * @CreatedBy: Sundy on 2025-05-23
 * ============================================================
 */
@Component
@Slf4j
public class RenewPassAction implements StateAction<SignStatus, ChannelEvent, ChannelContext> {
    @Resource
    private MetadataServiceExt metadataServiceExt;
    @Resource
    private AgreementStatusRecordService agreementStatusRecordService;
    @Resource
    private ChannelRestService channelRestService;
    @Resource
    private ChannelDataChangeService channelDataChangeService;
    @Resource
    private ChannelAgreementDetailService channelAgreementDetailService;
    @Resource
    private ChannelJob channelJob;
    @Resource
    private ChannelSignTaskService channelSignTaskService;
    @Resource
    private ChannelSignService channelSignService;
    @Resource
    private TimeComputeService timeComputeService;
    @Resource
    private ChannelAssertValidator channelAssertValidator;
    @Resource
    private ActivationTaskService activationTaskService;

    @Override
    public void execute(SignStatus sourceState, ChannelEvent event, ChannelContext context) {
        log.info("RenewPassAction#execute Renew Pass, sourceState: {}, event: {}, context:{}", sourceState, event, context);
        StepRecorder.record("RenewPassAction#execute");
        RenewMessage renewMessage = (RenewMessage) context.getPayload();
        User user = context.getUser();
        // 删除企业未续约处理任务
        log.info("renewalSignSuccessful,deleteRenewalExpiredJob");
        channelJob.deleteRenewalExpiredJob(user, renewMessage.getObjectDataId());
        StepRecorder.record("deleteRenewalExpiredJob");
        // 删除所有提醒
        log.info("renewalSignSuccessful,clearAlterReminderTrigger");
        channelRestService.clearAlterReminderTrigger(user, String.valueOf(renewMessage.getOutTenantId()));
        StepRecorder.record("clearAlterReminderTrigger");
        // 将签约状态改为续约
        IObjectData objectData = channelAssertValidator.assertValidData(user, renewMessage.getObjectApiName(), renewMessage.getObjectDataId());
        String value = DataUtils.getValue(objectData, SIGNING_STATUS, String.class, null);
        log.info("renewalSignSuccessful,changeSignStatusWithRecord objectData singingStatus:{}", value);
        agreementStatusRecordService.changeSignStatusWithRecord(user, I18N.text(SFA_CHANNEL_RENEWAL_SUCCESSFUL_TITLE), renewMessage.getObjectApiName(), objectData, SignStatus.RENEWAL);
        StepRecorder.record("changeSignStatusWithRecord");
        IObjectData partnerAgreementDetailData = channelAssertValidator.assertValidData(user, "PartnerAgreementDetailObj", renewMessage.getPartnerAgreementDetailId());
        StepRecorder.record("assertValidData#partnerAgreementDetailData");
        Long endTime = renewalSuccessfulHandler_950(user, objectData, renewMessage, SignStatus.RENEWAL);
        StepRecorder.record("renewalSuccessfulHandler_950");
        log.info("renewalSignSuccessful renewExpiredTime ,endTime:{}", endTime);
        channelDataChangeService.renewExpiredTime(user, renewMessage.getObjectApiName(), objectData, endTime);
        StepRecorder.record("renewExpiredTime");
        //查出所有 AgreementStatus.ACTIVE 协议，将其变更为 AgreementStatus.COMPLETED
        log.info("renewalSignSuccessful changeActive2Completed renewMessage:{}", renewMessage);
        channelAgreementDetailService.changeActive2Completed(user, renewMessage.getObjectApiName(), renewMessage.getObjectDataId(), partnerAgreementDetailData.getId());
        StepRecorder.record("changeActive2Completed");
    }

    private Long renewalSuccessfulHandler_950(User user, IObjectData objectData, RenewMessage renewMessage, SignStatus signStatus) {
        log.info("renewalSuccessfulHandler, begin");
        ChannelModel.AgreementDetailSignDTO agreementDetailSign = ChannelModel.AgreementDetailSignDTO.builder()
                .isRenewalEvent(true)
                .objectApiName(renewMessage.getObjectApiName())
                .objectData(objectData)
                .outTenantId(String.valueOf(renewMessage.getOutTenantId()))
                .signSchemeId(renewMessage.getSignSchemeId())
                .build();
        log.info("renewalSuccessfulHandler, changeChannelAgreementDetailWhenRenewalSuccessful agreementDetailSign:{}", agreementDetailSign);
        ChannelModel.ChangeAgreementDetailResult changeAgreementDetailResult = channelAgreementDetailService.changeChannelAgreementDetailWhenRenewalSuccessful(user, agreementDetailSign);
        log.info("renewalSuccessfulHandler renewExpiredTime changeAgreementDetailResult:{}", changeAgreementDetailResult);
        if (changeAgreementDetailResult == null) {
            throw new ItemNullException("changeAgreementDetailResult is null");
        }
        channelDataChangeService.renewExpiredTime(user, agreementDetailSign.getObjectApiName(), objectData, changeAgreementDetailResult.getEndTime());
        if (changeAgreementDetailResult.getScheduleType() == ScheduleType.ONE_TIME) {
            log.warn("renewalSuccessfulHandler getScheduleType is ONE_TIME");
            return null;
        }
        // 根据配置创建周期性签约提醒任务
        // 查询签约方案，窗口数据。
        IObjectData signSchemeData = metadataServiceExt.findObjectByIdIgnoreAll(user, renewMessage.getSignSchemeId(), "SignSchemeObj");
        if (signSchemeData == null) {
            throw new ItemNullException("signSchemeObj not found");
        }
        Integer renewalWindowEnd = DataUtils.getValue(signSchemeData, "renewal_window_end", Integer.class, 0);
        String renewalWindowUnit = DataUtils.getValue(signSchemeData, "renewal_window_unit", String.class, TimeUnit.DAY.getUnit());
        long enterpriseStopTime = timeComputeService.calculateTimestampByOffset(changeAgreementDetailResult.getEndTime(), renewalWindowEnd, TimeUnit.find(renewalWindowUnit));
        changeAgreementDetailResult.setEnterpriseStopTime(enterpriseStopTime);
        // 根据配置创建周期性签约提醒任务
        if (changeAgreementDetailResult.getReminderTrigger() == ReminderTriggerEnums.AUTO) {
            log.warn("renewalSuccessfulHandler createRenewalReminder is AUTO");
            channelSignService.createRenewalReminder(user, agreementDetailSign, changeAgreementDetailResult);
        }
        // 创建企业到期未续签停用企业任务
        channelSignTaskService.createRenewalExpiredNotRenewedJob(user, renewMessage.getObjectApiName(),
                objectData.getId(),
                changeAgreementDetailResult.getAgreementDetailId(),
                changeAgreementDetailResult.getEnterpriseStopTime(),
                agreementDetailSign.getOutTenantId());
        log.info("renewalSuccessfulHandler createRenewalExpiredNotRenewedJob end");
        activationTaskService.createActivationTask(user, ChannelStage.RENEWAL,
                BizScopeEnums.RENEW,
                renewMessage.getObjectApiName(),
                renewMessage.getObjectDataId(),
                renewMessage.getSignSchemeId(),
                agreementDetailSign.getOutTenantId());
        StepRecorder.record("createActivationTask#RENEWAL");
        return changeAgreementDetailResult.getEndTime();
    }
}
