package com.facishare.crm.task.sfa.executor.consumer;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.facishare.crm.sfa.audit.log.context.SFALogContext;
import com.facishare.crm.sfa.lto.operations.OperationsConstant;
import com.facishare.crm.sfa.lto.operations.OperationsStrategyProducer;
import com.facishare.crm.task.sfa.services.operations.OperationsInstanceCreateMessage;
import com.facishare.crm.task.sfa.services.operations.OperationsRetryException;
import com.facishare.crm.task.sfa.services.operations.OperationsService;
import com.facishare.paas.metadata.api.DBRecord;
import com.facishare.paas.metadata.api.Tenantable;
import com.fxiaoke.rocketmq.consumer.AutoConfMQPushConsumer;
import com.fxiaoke.rocketmq.util.MessageHelper;
import com.github.trace.TraceContext;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.client.consumer.listener.ConsumeConcurrentlyStatus;
import org.apache.rocketmq.client.consumer.listener.MessageListenerConcurrently;
import org.apache.rocketmq.common.message.MessageExt;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.context.event.ContextRefreshedEvent;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;

import java.nio.charset.StandardCharsets;

@Slf4j
@Component
public class OperationsListener implements InitializingBean, AutoCloseable {

    private final OperationsService service;

    private AutoConfMQPushConsumer consumer;

    public OperationsListener(OperationsService service) {
        this.service = service;
    }

    private void consumeMessage(MessageExt msg) throws OperationsRetryException {
        if (OperationsStrategyProducer.TAGS.equals(msg.getTags())) {
            String bodyStr = new String(msg.getBody(), StandardCharsets.UTF_8);
            JSONObject body = JSON.parseObject(bodyStr);
            log.info("receive msg [{}]: {}", msg.getMsgId(), body);
            OperationsInstanceCreateMessage message = new OperationsInstanceCreateMessage();
            message.setTenantId(body.getString(Tenantable.TENANT_ID));
            message.setStrategyId(body.getString(OperationsConstant.OPERATIONS_STRATEGY_ID));
            message.setCreateTime(body.getLong(DBRecord.CREATE_TIME));
            message.setMessageId(msg.getMsgId());
            message.setMessage(bodyStr);
            service.createInstance(message);
            SFALogContext.clearContext();
        }
    }

    @Override
    public void afterPropertiesSet() {
        consumer = new AutoConfMQPushConsumer("sfa-recalculate-consumer", "sfa-operations", (MessageListenerConcurrently) (list, context) -> {
            if (!list.isEmpty()) {
                for (MessageExt msg : list) {
                    MessageHelper.fillContextFromMessage(TraceContext.get(), msg);
                    try {
                        consumeMessage(msg);
                    } catch (OperationsRetryException e) {
                        return ConsumeConcurrentlyStatus.RECONSUME_LATER;
                    } catch (Exception e) {
                        log.error("consume message error: ", e);
                    }
                }
            }
            return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
        });
    }

    @EventListener(ContextRefreshedEvent.class)
    public void refresh(ContextRefreshedEvent event) {
        if (consumer != null && event.getApplicationContext().getParent() == null) {
            consumer.start();
        }
    }

    @Override
    public void close() throws Exception {
        if (consumer != null) {
            consumer.close();
        }
    }
}
