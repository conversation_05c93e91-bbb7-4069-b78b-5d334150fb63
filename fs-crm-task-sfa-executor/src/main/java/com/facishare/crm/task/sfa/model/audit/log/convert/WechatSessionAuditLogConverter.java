package com.facishare.crm.task.sfa.model.audit.log.convert;

import com.facishare.crm.sfa.audit.log.EntityConverter;
import com.facishare.crm.sfa.audit.log.model.AuditArg;
import com.facishare.crm.task.sfa.model.qywx.ConversionChangeArg;

public class WechatSessionAuditLogConverter implements EntityConverter<ConversionChangeArg.ChangeModel> {
	@Override
	public AuditArg convert(ConversionChangeArg.ChangeModel changeModel) {
		String extra;
		if (changeModel.getRoomId() != null) {
			extra = "group";
		} else {
			extra = "person";
		}

		return AuditArg.builder()
				.bizName("wechat_session_change")
				.ea(changeModel.getEa())
				.ei(changeModel.getTenantId())
				.extra(extra)
				.build();
	}
}
