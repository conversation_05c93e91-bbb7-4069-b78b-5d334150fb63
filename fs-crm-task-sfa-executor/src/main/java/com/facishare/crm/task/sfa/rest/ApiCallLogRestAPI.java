package com.facishare.crm.task.sfa.rest;

import com.facishare.crm.sfa.prm.platform.model.RestResponse;
import com.facishare.crm.task.sfa.model.ApiCallLogRequest;
import com.facishare.rest.core.annotation.Body;
import com.facishare.rest.core.annotation.HeaderMap;
import com.facishare.rest.core.annotation.POST;
import com.facishare.rest.core.annotation.RestResource;

import java.util.Map;

/**
 * ============================================================
 *
 * @Description:
 * @CreatedBy: Sundy on 2025-06-13
 * ============================================================
 */
@RestResource(value = "FS_CRM_SFA_INNER", desc = "ThirdPartyCallLog CRM Rest API", contentType = "application/json")
public interface ApiCallLogRestAPI {
    @POST(value = "/api-log/service/save")
    RestResponse saveLog(@HeaderMap Map<String, String> headers, @Body ApiCallLogRequest apiCallLogRequest);
}
