package com.facishare.crm.task.sfa.jxs;


import com.alibaba.fastjson.JSON;
import com.facishare.paas.auth.mq.userRole.RoleUserModifyEvent;
import com.fxiaoke.rocketmq.consumer.AutoConfMQPushConsumer;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.client.consumer.listener.ConsumeConcurrentlyStatus;
import org.apache.rocketmq.client.consumer.listener.MessageListenerConcurrently;
import org.apache.rocketmq.common.message.MessageExt;
import org.springframework.context.ApplicationListener;
import org.springframework.context.event.ContextRefreshedEvent;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;

//consumergroup:sfa_ep_user_permission_changed
@Slf4j
@Component
public class MyPermissionChangedConsumer implements ApplicationListener<ContextRefreshedEvent> {
    private AutoConfMQPushConsumer consumer;

    @PostConstruct
    public void init() {
        consumer = new AutoConfMQPushConsumer("fs-plat-service-mq", "RolesChange", (MessageListenerConcurrently) (msgs, context) -> {
            try {
                if (!msgs.isEmpty()) {
                    for (MessageExt msg : msgs) {
                        log.info("msg={}", msg);
                        String body = new String(msg.getBody());
                        RoleUserModifyEvent event = JSON.parseObject(body, RoleUserModifyEvent.class);
                    }
                }
            } catch (Exception e) {
                log.error("consumer failed", e);
                return ConsumeConcurrentlyStatus.RECONSUME_LATER;
            }

            return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
        });
    }

    @Override
    public void onApplicationEvent(ContextRefreshedEvent event) {
        if (null == event.getApplicationContext().getParent()) {
            if (null != consumer) {
                consumer.start();
            }
        }
    }
}
