package com.facishare.crm.task.sfa.loyalty.service;

import com.alibaba.fastjson.JSON;
import com.facishare.crm.sfa.lto.loyalty.constants.LoyaltyConstants;
import com.facishare.crm.sfa.lto.loyalty.model.Loyalty;
import com.facishare.crm.sfa.lto.loyalty.utils.LoyaltyI18nKey;
import com.facishare.crm.sfa.lto.loyalty.utils.NumberUtils;
import com.facishare.crm.task.sfa.common.util.CRMRecordUtil;
import com.facishare.crm.task.sfa.procurement.service.SFARedisService;
import com.facishare.crm.task.sfa.rest.SmsProxy;
import com.facishare.crm.task.sfa.util.SearchUtil;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.service.CRMNotificationService;
import com.facishare.paas.appframework.common.service.model.NewCrmNotification;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.Count;
import com.facishare.paas.metadata.api.search.AggFunctionArg;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.api.search.IGroupByParameter;
import com.facishare.paas.metadata.impl.search.GroupByParameter;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.facishare.paas.metadata.util.GetI18nKeyUtil;
import com.github.autoconf.ConfigFactory;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

@Slf4j
@Service
public class LoyaltyOrgService {

    @Resource
    private ServiceFacade serviceFacade;
    @Resource
    private CRMNotificationService crmNotificationService;
    @Resource
    private SmsProxy smsProxy;
    @Resource
    private SFARedisService redisService;

    private static Long seconds = 0L;

    static {
        ConfigFactory.getInstance().getConfig("fs-crm-sales-config", config -> seconds = config.getLong("loyalty_points_waring_interval", 60 * 60L));
    }

    public void syncUpdateOrg(Loyalty.OrgTask orgTask) {
        String tenantId = orgTask.getTenantId();
        String poolId = orgTask.getPoolId();
        if (StringUtils.isEmpty(poolId)) {
            return;
        }
        IObjectData poolInfo = serviceFacade.findObjectDataIgnoreAll(User.systemUser(tenantId), poolId, LoyaltyConstants.LoyaltyPointPool.API_NAME);
        String orgId = poolInfo.get("org_id", String.class);
        IObjectData orgInfo = serviceFacade.findObjectDataIgnoreAll(User.systemUser(tenantId), orgId, LoyaltyConstants.LoyaltyPointOrg.API_NAME);
        fillPoints(tenantId, orgInfo);
        serviceFacade.updateObjectData(User.systemUser(tenantId), orgInfo);
        long poolRemainingPoints = NumberUtils.getLong(poolInfo, LoyaltyConstants.LoyaltyPointPool.REMAINING_QUANTITY);
        Long poolWaringThresholdPoints = poolInfo.get(LoyaltyConstants.LoyaltyPointPool.REMAINING_POINTS_WARNING_VALUE, Long.class);
        if (poolWaringThresholdPoints != null && poolRemainingPoints <= poolWaringThresholdPoints) {
            warning(orgInfo, poolInfo, "" + poolRemainingPoints, "" + poolWaringThresholdPoints);
        }
        long orgRemainingPoints = NumberUtils.getLong(orgInfo, LoyaltyConstants.LoyaltyPointOrg.REMAINING_POINTS);
        Long orgWaringThresholdPoints = orgInfo.get(LoyaltyConstants.LoyaltyPointOrg.REMAINING_ALARM_POINTS, Long.class);
        if (orgWaringThresholdPoints != null && orgRemainingPoints <= orgWaringThresholdPoints) {
            warning(orgInfo, orgInfo, "" + orgRemainingPoints, "" + orgWaringThresholdPoints);
        }
    }

    private void warning(IObjectData orgInfo, IObjectData data, String remainingPoints, String waringThresholdPoints) {
        String tenantId = orgInfo.getTenantId();
        String warningKey = "loyalty_warning_" + data.getDescribeApiName() + "_" + tenantId + "_" + data.getId();
        if (!redisService.setnx(warningKey, seconds, new Date().toString())) {
            return;
        }
        if ("yes".equals(orgInfo.get(LoyaltyConstants.LoyaltyPointOrg.IS_BUSINESS_PARTNER, String.class))) {
            String message = I18N.text(LoyaltyI18nKey.POINTS_WAINING_INFO, data.getName(), remainingPoints, waringThresholdPoints);
            String name = orgInfo.get(LoyaltyConstants.LoyaltyPointOrg.EXTERNAL_ORGANIZATION_LEADER, String.class);
            String tel = orgInfo.get(LoyaltyConstants.LoyaltyPointOrg.EXTERNAL_CONTACT_NUMBER, String.class);
            SmsProxy.SmsInfo smsInfo = new SmsProxy.SmsInfo();
            smsInfo.setChannelType(12);
            smsInfo.setType(1);
            smsInfo.setTemplateContent(message);
            smsInfo.setPhoneDataList(Lists.newArrayList(new SmsProxy.SmsInfo.PhoneData(tel)));
            smsInfo.setTenantId(tenantId);
            smsInfo.setReal(false);
            smsInfo.setUserId("-5000");
            SmsProxy.Result result = smsProxy.sendSms(smsInfo);
            log.info("发送短信结果：{}", result);
        } else {
            String employedIdsJson = orgInfo.get(LoyaltyConstants.LoyaltyPointOrg.COST_ORGANIZATION_LEADER, String.class);
            CRMRecordUtil.sendNewCRMRecord(
                    crmNotificationService,
                    User.systemUser(tenantId),
                    NewCrmNotification.CUSTOM_REMIND_RECORD_TYPE,
                    JSON.parseArray(employedIdsJson).toJavaList(Integer.class),
                    User.SUPPER_ADMIN_USER_ID,
                    "{0}积分预警",
                    "{0}剩余积分[{1}]低于预警规则[{2}]",
                    LoyaltyI18nKey.POINTS_WAINING_TITLE,
                    Lists.newArrayList(CRMRecordUtil.getI18ParameterKey(GetI18nKeyUtil.getDescribeDisplayNameKey(data.getDescribeApiName()))),
                    LoyaltyI18nKey.POINTS_WAINING_INFO,
                    Lists.newArrayList(data.getName(), remainingPoints, waringThresholdPoints),
                    CRMRecordUtil.getUrlParameter(data.getDescribeApiName(), data.getId())
            );
        }
    }

    private void fillPoints(String tenantId, IObjectData orgInfo) {
        SearchTemplateQuery query = new SearchTemplateQuery();
        List<IFilter> filters = Lists.newArrayList();
        SearchUtil.fillFilterEq(filters, LoyaltyConstants.LoyaltyPointPool.ORG_ID, orgInfo.getId());
        query.setFilters(filters);
        IGroupByParameter groupByParameter = new GroupByParameter();
        groupByParameter.setAggFunctions(Lists.newArrayList(
                AggFunctionArg.builder()
                        .aggFunction(Count.TYPE_SUM)
                        .aggField(LoyaltyConstants.LoyaltyPointPool.ORIGINAL_QUANTITY)
                        .build(),
                AggFunctionArg.builder()
                        .aggFunction(Count.TYPE_SUM)
                        .aggField(LoyaltyConstants.LoyaltyPointPool.REMAINING_QUANTITY)
                        .build()
        ));
        groupByParameter.setGroupBy(Lists.newArrayList(LoyaltyConstants.LoyaltyPointPool.ORG_ID));
        query.setGroupByParameter(groupByParameter);
        List<IObjectData> res = serviceFacade.aggregateFindBySearchQuery(User.systemUser(tenantId), query, LoyaltyConstants.LoyaltyPointPool.API_NAME);
        for (IObjectData data : res) {
            orgInfo.set(LoyaltyConstants.LoyaltyPointOrg.TOTAL_POINTS, NumberUtils.getLong(data, "sum_original_quantity"));
            orgInfo.set(LoyaltyConstants.LoyaltyPointOrg.REMAINING_POINTS, NumberUtils.getLong(data, "sum_remaining_quantity"));
        }
    }
}
