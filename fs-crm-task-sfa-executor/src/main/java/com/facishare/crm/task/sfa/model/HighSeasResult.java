package com.facishare.crm.task.sfa.model;

import lombok.Data;

import java.util.List;

public interface HighSeasResult {
    @Data
    class Result {
        boolean success;
        String message;
        int errorCode;
        boolean value;
    }

    @Data
    class GetHSUserRoleTypeResult {
        boolean success;
        String message;
        int errorCode;
        List<Integer> value;
    }
}