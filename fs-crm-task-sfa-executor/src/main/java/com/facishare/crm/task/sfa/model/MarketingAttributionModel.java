package com.facishare.crm.task.sfa.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

public interface MarketingAttributionModel {
    @Data
    @Builder
    class CalculateMarketingAttributionArg{
        private List<String> objectIds;
        private String objectApiName;
    }

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    class RecalculateMarketingAttributionMessage {
        private String tenantId;
        private List<String> objectIds;
        private String operatorId;
        private Long operateTime;
    }
}