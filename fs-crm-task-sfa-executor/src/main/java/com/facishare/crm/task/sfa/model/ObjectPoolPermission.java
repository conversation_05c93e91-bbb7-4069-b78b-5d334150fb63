package com.facishare.crm.task.sfa.model;

import com.alibaba.druid.util.StringUtils;

public interface ObjectPoolPermission {
    enum  ObjectPoolPermissions {
        NO_PERMISSION("0","无"),
        POOL_ADMIN("1", "池管理员"),
        POOL_MEMBER("2", "池成员"),
        POOL_ALL("3", "池管理员和成员");

        private String value;
        private String label;

        ObjectPoolPermissions(String value, String label) {
            this.value = value;
            this.label = label;
        }

        public String getValue() {
            return value;
        }

        public String getLabel() {
            return label;
        }


        public static String getLabelByValue(String value) {
            for (ObjectPoolPermissions permission : ObjectPoolPermissions.values()) {
                if (StringUtils.equals(value, permission.value)) {
                    return permission.getLabel();
                }
            }
            return "";
        }

        public static ObjectPoolPermissions getEnumByValue(String value) {
            if (value == null) {return NO_PERMISSION;}
            for (ObjectPoolPermissions permission : ObjectPoolPermissions.values()) {
                if (StringUtils.equals(value, permission.value)) {
                    return permission;
                }
            }
            return NO_PERMISSION;
        }

        public Boolean isPoolAdmin(){
            if(this.getValue().equals(ObjectPoolPermissions.POOL_ALL.getValue())
                    || this.getValue().equals(ObjectPoolPermissions.POOL_ADMIN.getValue())){
                return  true;
            }
            return  false;
        }

        public Boolean isPoolMember(){
            if(this.getValue().equals(ObjectPoolPermissions.POOL_ALL.getValue())
                    || this.getValue().equals(ObjectPoolPermissions.POOL_MEMBER.getValue())){
                return  true;
            }
            return  false;
        }
    }

    enum ObjectPoolMemberType {
        EMPLOYEE("1","人员"),
        CIRCLE("2","部门"),
        OUTER_ENTERPRISE("3", "外部企业"),
        OUTER_EMPLOYEE("4", "外部人员");
        private String value;
        private String label;

        ObjectPoolMemberType(String value, String label) {
            this.value = value;
            this.label = label;
        }
        public String getValue() {
            return value;
        }
    }

    enum ObjectPoolLimitType {
        PERSONAL("personal","个人级"),
        ENTERPRISE("enterprise","企业级");
        private String value;
        private String label;

        ObjectPoolLimitType(String value, String label) {
            this.value = value;
            this.label = label;
        }
        public String getValue() {
            return value;
        }
    }
}
