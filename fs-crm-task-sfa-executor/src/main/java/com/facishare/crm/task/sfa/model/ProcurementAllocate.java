package com.facishare.crm.task.sfa.model;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @Description
 * <AUTHOR>
 * @Date 2023/3/02 00:32
 */


public interface ProcurementAllocate {


    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    class BulkArg{
        @JSONField(name = "data_list")
        @JsonProperty(value = "data_list")
        List<Arg> dataList;
    }


    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    class Arg {

        @JSONField(name = "bid_type")
        @JsonProperty(value = "bid_type")
        Integer bidType;


        @JSONField(name = "id")
        @JsonProperty(value = "id")
        String id;

        @JSONField(name = "client_info")
        @JsonProperty(value = "client_info")
        String clientInfo;

        @JSONField(name = "batch")
        @JsonProperty(value = "batch")
        Integer batch;
    }


    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    class AllocateResult {
        @JSONField(name = "errCode")
        private int errorCode;
        @JSONField(name = "errMessage")
        private String errorMessage;
        @JSONField(name = "result")
        private Result result;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @Builder
    class Result {
        @JSONField(name = "id")
        private String message;
        @JSONField(name = "cost")
        private int cost;
        @JSONField(name = "success")
        private boolean success;
    }


}
