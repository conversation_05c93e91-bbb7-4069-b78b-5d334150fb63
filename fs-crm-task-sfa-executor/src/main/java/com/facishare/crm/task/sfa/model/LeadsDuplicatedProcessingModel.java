package com.facishare.crm.task.sfa.model;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Builder;
import lombok.Data;

import java.util.List;
import java.util.Map;

public interface LeadsDuplicatedProcessingModel {
    @Data
    @Builder
    class Arg {
        @JsonProperty("refresh_version")
        @JSONField(name = "refresh_version")
        int refreshVersion;
        @JsonProperty("object_data_id_list")
        @JSONField(name = "object_data_id_list")
        List<String> objectDataIdList;
        String source;
    }

    @Data
    class Result {
        int errCode;
        String errMessage;
    }
}