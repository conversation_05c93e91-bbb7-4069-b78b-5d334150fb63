package com.facishare.crm.task.sfa.services.channel.actions;

import com.facishare.crm.sfa.prm.api.channel.AgreementStatusRecordService;
import com.facishare.crm.sfa.prm.api.channel.ChannelDataChangeService;
import com.facishare.crm.sfa.prm.api.enums.ScheduleType;
import com.facishare.crm.sfa.prm.api.enums.SignStatus;
import com.facishare.crm.sfa.prm.core.service.TimeComputeService;
import com.facishare.crm.sfa.prm.platform.enums.TimeUnit;
import com.facishare.crm.sfa.prm.platform.infrastructure.statemachine.core.StateAction;
import com.facishare.crm.sfa.prm.platform.utils.DataUtils;
import com.facishare.crm.task.sfa.enums.BizScopeEnums;
import com.facishare.crm.task.sfa.enums.ChannelStage;
import com.facishare.crm.task.sfa.enums.ReminderTriggerEnums;
import com.facishare.crm.task.sfa.model.ChannelModel;
import com.facishare.crm.task.sfa.model.OutRoleDTO;
import com.facishare.crm.task.sfa.services.*;
import com.facishare.crm.task.sfa.services.channel.core.ChannelAssertValidator;
import com.facishare.crm.task.sfa.services.channel.exception.ChannelFlowException;
import com.facishare.crm.task.sfa.services.channel.exception.ItemNullException;
import com.facishare.crm.task.sfa.services.channel.model.ChannelContext;
import com.facishare.crm.task.sfa.services.channel.enums.ChannelEvent;
import com.facishare.crm.task.sfa.services.channel.model.SignMessage;
import com.facishare.crm.task.sfa.services.channel.utils.StepRecorder;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.IObjectData;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

import static com.facishare.crm.task.sfa.common.util.I18NKeyUtil.SFA_CHANNEL_RENEWAL_SIGN_SUCCESS;

/**
 * ============================================================
 *
 * @Description:
 * @CreatedBy: Sundy on 2025-05-20
 * ============================================================
 */
@Component
@Slf4j
public class SignPassAction implements StateAction<SignStatus, ChannelEvent, ChannelContext> {
    @Resource
    private MetadataServiceExt metadataServiceExt;
    @Resource
    private ChannelRestService channelRestService;
    @Resource
    private ChannelSignTaskService channelSignTaskService;
    @Resource
    private AgreementStatusRecordService agreementStatusRecordService;
    @Resource
    private ChannelAgreementDetailService channelAgreementDetailService;
    @Resource
    private ChannelNotificationService channelNotificationService;
    @Resource
    private EnterpriseLinkRoleService enterpriseLinkRoleService;
    @Resource
    private ChannelDataChangeService channelDataChangeService;
    @Resource
    private ChannelSignService channelSignService;
    @Resource
    private TimeComputeService timeComputeService;
    @Resource
    private ChannelAssertValidator channelAssertValidator;
    @Resource
    private ActivationTaskService activationTaskService;

    @Override
    public void execute(SignStatus sourceState, ChannelEvent event, ChannelContext context) {
        log.info("SignPassAction#execute Sign Pass, sourceState: {}, event: {}, context:{}", sourceState, event, context);
        StepRecorder.record("SignPassAction#execute");
        User user = context.getUser();
        SignMessage signMessage = (SignMessage) context.getPayload();
        IObjectData objectData = channelAssertValidator.assertValidData(user, signMessage.getObjectApiName(), signMessage.getDataId());
        ChannelModel.AggregatorMatchDTO aggregatorResult = assertValidSignScheme(user, signMessage);
        StepRecorder.record("matchSignScheme");
        OutRoleDTO outRoleDTO = OutRoleDTO
                .builder()
                .objectApiName(signMessage.getObjectApiName())
                .dataId(signMessage.getDataId())
                .app(signMessage.getApp())
                .activateRoles(aggregatorResult.getActivateRoles())
                .build();
        String outTenantId = null;
        try {
            log.info("ChannelSignService#sign updateOutUserRole begin outRoleDTO: {}", outRoleDTO);
            outTenantId = enterpriseLinkRoleService.updateOutUserRole(user, signMessage.getOperator(), outRoleDTO);
            log.info("ChannelSignService#sign updateOutUserRole end");
            StepRecorder.record("updateOutUserRole");
        } catch (Exception e) {
            log.warn("签约审核：更新角色失败, tenant:{}, outRoleDTO:{}", user.getTenantId(), outRoleDTO, e);
            throw new ChannelFlowException("updateOutUserRole failed");
        }
        // 已签约，ExpiredTime 置为 endTime。
        ChannelModel.AgreementDetailSignDTO agreementDetailSignDTO = ChannelModel.AgreementDetailSignDTO
                .builder()
                .objectApiName(signMessage.getObjectApiName())
                .objectData(objectData)
                .outTenantId(outTenantId)
                .signSchemeId(signMessage.getSignSchemeId())
                .isRenewalEvent(false)
                .build();
        log.info("ChannelSignService#sign changeChannelAgreementDetailWhenSign agreementDetailSignDTO: {}", agreementDetailSignDTO);
        ChannelModel.ChangeAgreementDetailResult changeAgreementDetailResult = channelAgreementDetailService.changeChannelAgreementDetailWhenSign(user, agreementDetailSignDTO);
        StepRecorder.record("changeChannelAgreementDetailWhenSign");
        if (changeAgreementDetailResult == null) {
            throw new ItemNullException("objectData is null");
        }
        agreementStatusRecordService.changeSignStatusWithRecord(user, I18N.text(SFA_CHANNEL_RENEWAL_SIGN_SUCCESS), signMessage.getObjectApiName(), objectData, SignStatus.SIGNED, SignStatus.PENDING_SIGNATURE);
        log.info("ChannelSignService#sign renewExpiredTime: {}", changeAgreementDetailResult);
        StepRecorder.record("changeSignStatusWithRecord");
        channelDataChangeService.renewExpiredTime(user, signMessage.getObjectApiName(), objectData, changeAgreementDetailResult.getEndTime());
        StepRecorder.record("renewExpiredTime");
        // 开始根据结束时间，创建提醒任务。
        if (changeAgreementDetailResult.getScheduleType() == ScheduleType.ONE_TIME) {
            log.info("ChannelSignService#sign ONE_TIME renewExpiredTime");
            channelNotificationService.sendChannelNotice(user, objectData, true, aggregatorResult.getApprovalNoticeAggregators());
            StepRecorder.record("ONE_TIME#sendChannelNotice");
            channelSignTaskService.createRenewalExpiredNotRenewedJob(user, signMessage.getObjectApiName(), objectData.getId(), changeAgreementDetailResult.getAgreementDetailId(), changeAgreementDetailResult.getEndTime(), outTenantId);
            StepRecorder.record("createRenewalExpiredNotRenewedJob");
            activationTaskService.createActivationTask(user,
                    ChannelStage.SIGNED,
                    BizScopeEnums.SIGN,
                    signMessage.getObjectApiName(),
                    signMessage.getDataId(),
                    agreementDetailSignDTO.getSignSchemeId(),
                    outTenantId);
            StepRecorder.record("createActivationTask#SIGNED");
            return;
        }
        // 根据配置创建周期性签约提醒任务
        // 查询签约方案，窗口数据。
        IObjectData signSchemeData = metadataServiceExt.findObjectByIdIgnoreAll(user, agreementDetailSignDTO.getSignSchemeId(), "SignSchemeObj");
        if (signSchemeData == null) {
            throw new ItemNullException("signSchemeObj not found");
        }
        Integer renewalWindowEnd = DataUtils.getValue(signSchemeData, "renewal_window_end", Integer.class, 0);
        String renewalWindowUnit = DataUtils.getValue(signSchemeData, "renewal_window_unit", String.class, TimeUnit.DAY.getUnit());
        long enterpriseStopTime = timeComputeService.calculateTimestampByOffset(changeAgreementDetailResult.getEndTime(), renewalWindowEnd, TimeUnit.find(renewalWindowUnit));
        changeAgreementDetailResult.setEnterpriseStopTime(enterpriseStopTime);
        if (changeAgreementDetailResult.getReminderTrigger() == ReminderTriggerEnums.AUTO) {
            log.info("ChannelSignService#sign AUTO renewExpiredTime createRenewalReminder");
            channelSignService.createRenewalReminder(user, agreementDetailSignDTO, changeAgreementDetailResult);
            StepRecorder.record("AUTO#createRenewalReminder");
        }
        // 创建企业到期未续签停用企业任务
        log.info("ChannelSignService#sign createRenewalExpiredNotRenewedJob renewExpiredTime");
        channelSignTaskService.createRenewalExpiredNotRenewedJob(user, signMessage.getObjectApiName(), objectData.getId(), changeAgreementDetailResult.getAgreementDetailId(), changeAgreementDetailResult.getEnterpriseStopTime(), outTenantId);
        StepRecorder.record("createRenewalExpiredNotRenewedJob");
        channelNotificationService.sendChannelNotice(user, objectData, true, aggregatorResult.getApprovalNoticeAggregators());
        StepRecorder.record("sendChannelNotice");
        activationTaskService.createActivationTask(user,
                ChannelStage.SIGNED,
                BizScopeEnums.SIGN,
                signMessage.getObjectApiName(),
                signMessage.getDataId(),
                signSchemeData.getId(),
                outTenantId);
        StepRecorder.record("createActivationTask#SIGNED");
    }

    private ChannelModel.AggregatorMatchDTO assertValidSignScheme(User user, SignMessage signMessage) {
        ChannelModel.AggregatorMatchDTO aggregatorResult = channelRestService.matchSignScheme(user, signMessage.getObjectApiName(), signMessage.getDataId());
        if (aggregatorResult == null) {
            log.warn("SignRejetAction#execute aggregator result not found, dataId:{}, objectApiName:{}", signMessage.getDataId(), signMessage.getObjectApiName());
            throw new ItemNullException("aggregator result not found");
        }
        return aggregatorResult;
    }
}
