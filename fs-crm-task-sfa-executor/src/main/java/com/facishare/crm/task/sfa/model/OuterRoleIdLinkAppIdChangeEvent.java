package com.facishare.crm.task.sfa.model;

import com.facishare.common.fsi.ProtoBase;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * <AUTHOR>
 * @time 2023-12-06 10:28
 * @Description
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class OuterRoleIdLinkAppIdChangeEvent extends ProtoBase {
    public static final String TAG = "7001";
    private String tenantId;
    /**
     * 互联角色  游客角色=er_visitor
     */
    private String outerRoleId;
    /**
     * 互联角色与那些互联应用取消关联
     */
    private List<String> deleteLinkAppIds;
    /**
     * 互联角色与哪些应用建立关联
     */
    private List<String> addLinkAppIds;
}
