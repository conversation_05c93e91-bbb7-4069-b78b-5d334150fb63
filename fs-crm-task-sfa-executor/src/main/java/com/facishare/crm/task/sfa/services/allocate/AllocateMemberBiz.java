package com.facishare.crm.task.sfa.services.allocate;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.facishare.crm.task.sfa.util.ConvertUtils;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.DescribeLogicServiceImpl;
import com.facishare.paas.appframework.metadata.MetaDataFindService;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.QueryResult;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IFieldType;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.api.search.Wheres;
import com.facishare.paas.metadata.exception.MetadataServiceException;
import com.facishare.paas.metadata.impl.search.Filter;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.OrderBy;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.facishare.paas.metadata.service.impl.ObjectDataServiceImpl;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.facishare.crm.task.sfa.util.ConvertUtils.convertMapToObjectData;

/**
 * @Description 线索分配查询成员相关的数据
 * <AUTHOR>
 * @Date 2021/1/23 15:53
 */

@Slf4j
@Component
public class AllocateMemberBiz {
    @Autowired
    protected ObjectDataServiceImpl objectDataService;
    @Autowired
    private MetaDataFindService metaDataFindService;
    @Autowired
    private DescribeLogicServiceImpl describeLogicService;

    /**
     * 获取所有规则配置的分配成员
     *
     * @param ruleId
     * @param tenantId
     * @return
     */
    public List<IObjectData> getAllRuleMembers(String ruleId, String tenantId, String apiName) {
        StringBuilder ruleMemberSql;
        ruleMemberSql = new StringBuilder();
        ruleMemberSql.append(String.format("select * from biz_pool_allocate_rule_member where tenant_id = '%s' and object_api_name='%s' and is_deleted = 0 and allocate_rule_id ='%s' ", tenantId, apiName, ruleId));
        ruleMemberSql.append("order by create_time ");
        List<Map> ruleMembers = new ArrayList<>();
        try {
            ruleMembers = objectDataService.findBySql(tenantId, ruleMemberSql.toString());
        } catch (MetadataServiceException e) {
            log.error("getAllRuleMembers findBySql:{}", ruleMemberSql, e);
        }
        return ConvertUtils.convertMapToObjectData(ruleMembers);
    }

    /**
     * 获取所有的分配历史
     *
     * @param ruleId
     * @param tenantId
     * @return
     */
    public List<IObjectData> getAllRuleMemberRecords(String ruleId, String tenantId, String apiName) {
        List<Map> member_record = null;
        StringBuilder ruleMemberRecordSql = new StringBuilder();
        ruleMemberRecordSql.append(String.format("select * from biz_pool_allocate_rule_member_record where tenant_id = '%s' and data_object_describe_api_name = '%s' and allocate_rule_id  ='%s' ", tenantId, apiName, ruleId));
        ruleMemberRecordSql.append("order by last_allocate_time");

        try {
            member_record = objectDataService.findBySql(tenantId, ruleMemberRecordSql.toString());
        } catch (MetadataServiceException e) {
            log.error("getAllRuleMemberRecords error", e);
        }
        return convertMapToObjectData(member_record);
    }

    /**
     * 获取所有根据条件设置的人员
     *
     * @param user
     * @param memberWheres
     * @return
     */
    public List<String> getCustomMemberIds(User user, String memberWheres) {
        List<Wheres> wheresList = Lists.newArrayList();
        convert2WheresList(memberWheres, wheresList);
        if (CollectionUtils.empty(wheresList)){
            return new ArrayList<>();
        }

        List<String> dataIds = Lists.newArrayList();
        int offset = 0;
        int pageSize = 100;

        String objectApiName = "PersonnelObj";
        IObjectDescribe objectDescribe = describeLogicService.findObject(user.getTenantId(), objectApiName);

        List<Wheres> newWheres = convertFilter(wheresList, objectDescribe);

        // newWheres为空 或者  filters 为空不再去查询
        if (CollectionUtils.empty(newWheres) ||
                (CollectionUtils.notEmpty(newWheres) && newWheres.size() == 1) && CollectionUtils.empty(newWheres.get(0).getFilters())){
            return Lists.newArrayList();
        }

        int maxCount = 1000;
        while (maxCount >= 0) {
            SearchTemplateQuery searchTemplateQuery = new SearchTemplateQuery();
            searchTemplateQuery.setWheres(newWheres);
            searchTemplateQuery.setLimit(pageSize);
            searchTemplateQuery.setOffset(offset);
            searchTemplateQuery.setPermissionType(0);
            searchTemplateQuery.setOrders(Lists.newArrayList(new OrderBy("id", true)));
            --maxCount;
            QueryResult<IObjectData> queryResult = queryBySearchTemplate(user, objectApiName, searchTemplateQuery);
            if(queryResult.getData() == null) {
                break;
            }
            if(CollectionUtils.empty(queryResult.getData())) {
                break;
            }
            if("PersonnelObj".equals(objectApiName)) {
                dataIds.addAll(queryResult.getData().stream().map(x -> x.get("user_id").toString()).collect(Collectors.toSet()));
            }
            offset += pageSize;
            if(queryResult.getTotalNumber() <= offset) {
                break;
            }
            searchTemplateQuery.setOffset(offset);
        }
        return  dataIds;
    }

    private void convert2WheresList(String wheresString, List<Wheres> wheresList) {
        if(StringUtils.isEmpty(wheresString)) {
            return;
        }
        List<JSONObject> wheresJSONObjectList = JSON.parseObject(wheresString, List.class);
        if(CollectionUtils.notEmpty(wheresJSONObjectList)){
            for(JSONObject jsonObject : wheresJSONObjectList){
                Wheres wheres = JSON.parseObject(jsonObject.toJSONString(), Wheres.class);
                if(CollectionUtils.notEmpty(wheres.getFilters())) {
                    for(IFilter filter : wheres.getFilters()) {
                        if(filter.getFieldValues() == null) {
                            filter.setFieldValues(Lists.newArrayList());
                        }
                    }
                }
                wheresList.add(wheres);
            }
        }
    }

    private List<Wheres>  convertFilter(List<Wheres> wheresList, IObjectDescribe objectDescribe) {
        if(CollectionUtils.empty(wheresList) || objectDescribe == null) {
            return wheresList;
        }
        List<Wheres> newWheres = new ArrayList<>();
        for (Wheres wheres : wheresList) {
            if(CollectionUtils.empty(wheres.getFilters())) {
                continue;
            }

            boolean isFieldDeleted = true;
            for (IFilter filter : wheres.getFilters()) {
                IFieldDescribe fieldDescribe = objectDescribe.getFieldDescribe(filter.getFieldName());
                if(fieldDescribe == null) {
                    continue;
                }
                isFieldDeleted = false;
                if(IFieldType.OBJECT_REFERENCE.equals(fieldDescribe.getType())) {
                    filter.setFieldName(String.format("%s.name", filter.getFieldName()));
                } else if(IFieldType.SELECT_MANY.equals(fieldDescribe.getType())) {
                    if(Operator.IN.equals(filter.getOperator())) {
                        filter.setOperator(Operator.HASANYOF);
                    } else if(Operator.NIN.equals(filter.getOperator())) {
                        filter.setOperator(Operator.NHASANYOF);
                    }
                }
            }

            if (!isFieldDeleted){
                List<IFilter> filters = wheres.getFilters();
                // 增加停用状态
                IFilter filter = new Filter();
                filter.setFieldName("status");
                filter.setOperator(Operator.EQ);
                filter.setFieldValues(Lists.newArrayList("0"));
                filters.add(filter);
                newWheres.add(wheres);
            }
        }
        return newWheres;
    }

    private QueryResult<IObjectData> queryBySearchTemplate(User user, String objectApiName, SearchTemplateQuery searchQuery) {
        QueryResult<IObjectData> result;
        try {
            result = metaDataFindService.findBySearchQuery(user, objectApiName, searchQuery);
        } catch (Exception e) {
            log.error("queryObjectData throw exception,tenantId:{}, objectApiName:{}",  user.getTenantId(), objectApiName,e);
            throw new RuntimeException();
        }
        return result;
    }
}
