package com.facishare.crm.task.sfa.services.channel.core;

import com.facishare.crm.task.sfa.services.MetadataServiceExt;
import com.facishare.crm.task.sfa.services.channel.exception.ItemNullException;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.IObjectData;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * ============================================================
 *
 * @Description:
 * @CreatedBy: Sundy on 2025-05-26
 * ============================================================
 */
@Service
@Slf4j
public class ChannelAssertValidator {
    @Resource
    private MetadataServiceExt metadataServiceExt;

    public IObjectData assertValidData(User user, String objectApiName, String dataId) {
        IObjectData objectData = metadataServiceExt.findObjectByIdIgnoreAll(user, dataId, objectApiName);
        if (objectData == null) {
            log.warn("ChannelAssertValidator#execute objectData is null");
            throw new ItemNullException("objectData is null");
        }
        return objectData;
    }
}
