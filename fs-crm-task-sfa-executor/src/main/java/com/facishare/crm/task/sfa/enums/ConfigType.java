package com.facishare.crm.task.sfa.enums;

import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;

import java.util.List;
import java.util.Map;

public enum ConfigType {
    None("-1", "查无此项目", "0"),
    IsAllowExportCustomerAndContact("1", "是否允许员工导出客户和联系人", "1"),
    IsAllowCreateCustomer("2", "是否禁止员工添加客户", "1"),
    IsFuzzyQuery("4", "是否开启模糊查询", "1"),
    IsOpenFiling("3", "是否启用客户报备", "0"),
    CheckDuplicate("5", "客户查重显示项", "1,2,6,5,3,4"),
    IsOpenCustomerCheckRepeat("6", "是否开启客户查重", "0"),
    IsAllowNoRightSeeCompleteCustomerName("7", "是否禁止无客户权限的员工在工作流中看到完整客户名称", "0"),
    ServiceEmployeeFindCategory("9", "服务人员查询类别 1、模糊查询；2、精确查询", "1"),
    LeaderViewScope("10", "(5.2新增) 上级可见数据范围=10;[ EValue：1-所有下级数据，2-直属下级数据]", "2"),
    IsAllowedToEditCustomerName("11", "(5.3新增) 允许负责人修改客户名称 [1-可以;0-不可以]", "0"),
    IsAllowedSetPersonalGoal("12", "(5.3新增)允许个人设置目标 [1-可以;0-不可以]", "0"),
    IsDiscountAutoCalculated("13", "(5.3新增) 折扣是否自动计算 [1-是;0-否]", "0"),
    CRMVersion("15", "(5.4新增) CRM版本 1、普通版本；2、快销版本 具体参照枚举 CRMVersionEnum", "1"),
    CustomerOrderRule("16", "(5.4新增) 订单规则 ，默认勾选3。其中12互斥,1 订单金额=产品合计*折扣，产品是必填项,2 订单金额、产品合计、折扣互相独立，产品是选填项,3 订单金额、产品合计、折扣互相独立，产品是必填项", "0,0,1"),
    DuplicateSearchBasicSetting("17", "(5.5 新增) 查重基本规则", "[{\"ObjectType\":2,\"AddDulicate\":true,\"ToolDulicate\":true},{\"ObjectType\":3,\"AddDulicate\":true,\"ToolDulicate\":true},{\"ObjectType\":1,\"AddDulicate\":false,\"ToolDulicate\":true},{\"ObjectType\":8,\"AddDulicate\":false,\"ToolDulicate\":false}]"),
    OpportunityDuplicateSearch("20", "(5.5 新增) 商机判重 1-判重  0-不判重", "0"),
    TradeOrderWorkflowType("21", "(5.5 新增) 审批流订单类型 1-自定义审批流  0-固定审批流", "0"),
    ReturnOrderWorkflowType("22", "(5.5 新增) 审批流退货单类型 1-自定义审批流  0-固定审批流", "0"),
    IsDingHuoTongEnabled("23", "(5.5 新增) 是否启用订货通(0-不启用 1-启用)", "0"),
    IsFastSellingEnabled("24", " (5.5新增）是否启用快销版功能（0-不起用 1-启用）", "0"),
    CustomerFollowDealSetting("25", "(5.6新增）客户跟进成交动作配置项", ""),
    UniquenessRulesSetting("26", "(6.0新增）唯一性规则", ""),
    UnionDSSetting("27", "(6.0新增）联合查重配置", ""),
    IsPriceBookEnabled("28", "(6.1新增）是否启用价目表（0-不起用 1-启用）", "0"),
    IsCustomerAccountEnabled("29", "(6.1Added) 客户账户是否启用（0-不启用,1-启用)", "0"),
    IsNewCustomerAccountEnabled("is_customer_account_enable", "客户账户2.0是否启用（1-不启用,2-启用)", "1"),
    IsPaymentEnterAccountEnable("is_payment_enter_account_enable", "回款是否入账", "0"),
    IsPaymentPayEnable("is_payment_pay_enable", "回款支付", "0"),
    IsInventoryEnabled("30", "(6.2Added)库存是否启用", "0"),
    IsPromotionEnabled("31", "(6.2Added)促销是否启用", "0"),
    IsDeliveryNoteEnabled("33", "(6.2Added)发货单是否启用", "0"),
    IsShowRankingList("34", "(6.2Added)是否显示排行榜", "0"),
    IsDeviceManagementEnabled("35", "(6.3Added)是否开启设备管理", "0"),
    DuplicateSearchLastModifyBy("36", "(6.3Added)查重最后修改人", ""),
    IsOpenPartner("37", "(6.3Added)是否开启合作伙伴", "0"),
    IsSalesRecordTypeNotNull("38", "(6.3.1Added)销售记录类型是否必填", "0"),
    IsServiceRecordTypeNotNull("39", "(6.3.1Added)服务记录类型是否必填", "0"),
    IsEnableTransExistCustomer("40", "(6.3.1Added)是否启用线索转换时关联已有客户", "1"),
    LeadsTransferSetting("41", "(6.3.2Added)线索转换设置 客户--1必选  联系人--1必选，0可选 商机--1必选，0可选 商机2.0--1必选，0可选，与商机最多二选一", "1,0,0,0"),
    IsOpenNewOpportunity("42", "(6.3.4Added)开启商机2.0", "0"),
    IsOpenNewOpportunityByNewKey("config_newopportunity_open", "(6.3.4Added)开启商机2.0", "0"),
    IsTradeProductRepeatable("43", "(6.3.4Added)订单产品是否可重复  1-可重复，0-不可重复 开通订货通需要更改为可重复", "1"),
    IsIndustryPriceBookEnabled("44", "(6.3.4Added)行业价目表", "0"),
    IsSendFeedContactWithCustomer("45", "联系人的销售记录同关联到客户下必填可配置", "0"),
    CanEditOrderWhenPromotionEnabled("46", "(6.5Added)当促销开启时,是否可编辑订单", "0"),
    IsBatchNumberOrSerialNumberEnabled("47", "(6.5Added)是否开启批次、序列号管理 必须先开启库存，不可关闭", "0"),
    IsAllowedToEditPartnerName("48", "允许负责人修改合作伙伴名称 [1-可以;0-不可config_change_close_date_if_win以]【默认可以】", "1"),
    IsOpenSalesOrder("49", "销售订单是否开启", "0"),
    IsPromotionEnabledWhenEditSalesOrder("50", "编辑订单仍然适配促销", "0"),
    ConfigChangeCloseDateIfWin("config_change_close_date_if_win", "赢单是否更改结单日期", "0"),
    TransferToAccountChangeStageSetting("51", "线索转换触发线索阶段变更设置", "1,SQL"),
    TransferToOppChangeStageSetting("52", "线索转换触发线索阶段变更设置", "1,SQL"),
    UPSTREAM_PROMOTION_OPERATION("upstream_promotion_operation", "功能操作", "{\"create\":true, \"update\":true}"),
    UPSTREAM_PROMOTION_ORDER_RECORD_TYPE("upstream_promotion_order_record_type", "订单业务类型", "all"),
    IS_SHOW_DUPLICATE_SEARCH("IsShowDuplicateSearch", "是否开启查重", "1"),
    NEW_INVOICE("new_invoice", "new_invoice", "0"),
    INVOICE_IS_ALLOWED_OVERFLOW("invoice_is_allowed_overflow", "invoice_is_allowed_overflow", "0"),
    MODULE_CPQ("cpq", "cpq", "0"),
    MODULE_CPQ_TIEREDPRICE("cpq_tieredprice", "cpq_tieredprice", "0"),
    MODULE_SPU("config_spu_or_sku_selector", "config_spu_or_sku_selector", "0"),
    MODULE_PRM("config_partner_open", "config_partner_open", "0"),
    MODULE_PRICE_BOOK("pricebook", "pricebook", "0"),
    MODULE_CHANGE_TO_NEW_OPPORTUNITY("change_to_new_opportunity", "change_to_new_opportunity", "0"),
    MODULE_MULTIPLE_UNIT("multiple_unit", "multiple_unit", "0"),
    DEFAULT_MODULE("default_module", "default_module", "0"),
    MODULE_SAVE_NEW_OPPORTUNITY_USER_FILTER("new_opportuntiy_user_filter", "new_opportuntiy_user_filter", "0"),
    SPU("spu", "商品设置", "0"),
    CPQ("cpq", "配置产品组合", "0"),
    BOM_INSTANCE("bom_instance", "产品选配实例", "0"),
    INVOICE_MODE("invoice_mode", "开票模式", "normal"),
    ACCOUNT_TREE_FIELDS("account_tree_fields", "客户层级关系展示字段", "name"),
    NEW_OPPORTUNTIY_LEADS_SETTING("new_opportuntiy_leads_setting", "商机归因设置0:不自动关联；1：自动关联第一个；2：自动关联最近一个", "0"),
    IS_KX_PECULIARITY_ENABLED("is_kx_peculiarity_enabled", "是否是快销企业", "0"),
    AVAILABLE_RANGE("available_range", "是否开启可售范围", "0"),
    ENFORCE_PRIORITY("enforce_priority", "是否强制执行价目表优先级最优价格", "0"),
    CONTACT_RELATIONSHIP_TYPE("contact_relationship_type", "联系人关系类型", "[{\"value\":\"1\",\"label\":\"上级\"}]"),
    IS_OPEN_AVAILABLE_RANGE_PRIORITY("is_open_available_range_priority", "是否开启可售范围优先级", "0"),
    CLONE_HISTORY_ORDER_PRODUCT("clone_history_order_product", "复制历史订单产品", "0"),
    BOM_PRINT_TEMPLATE_INDENT("bom_print_template_indent", "XX明细打印Bom支持缩进", "0"),
    BOM_PRINT_TEMPLATE_HAS_SUB_NODE("bom_print_template_has_sub_node", "XX明细打印Bom是否包含子节点", "0"),
    PROMOTION_MOBILE_H5("promotion_mobile_h5", "促销是否移动端跳转订单h5", "0"),
    IS_OPEN_ATTRIBUTE("is_open_attribute", "是否开启属性属性值", "0"),
    IS_OPEN_NONSTANDARD_ATTRIBUTE("is_open_nonstandard_attribute", "是否开启非标属性", "0"),
    CHANGE_BUSINESS_TYPE("change_business_type", "是否工商回填", "0"),
    PROMOTION_STATUS("promotion_status", "是否开启促销", "0"),
    ACCOUNT_LEADS_TO_ENTERPRISE("account_leads_to_enterprise", "线索/客户同步企业库规则", "{\"use_default_rule\":\"1\",\"func_api_name\":\"\",\"func_name\":\"\",\"binding_object_api_name\":\"NONE\"}"),
    ACCOUNT_MAIN_DATA_ENABLE_COMMON_PRIVILEGE("account_main_data_enable_common_privilege", "新建客户选主数据对象使用系统通用权限过滤可选数据范围", "0"),
    LEADS_TRANSFER_RIGHT_SETTING("leads_transfer_right_setting", "线索是否允许转换为无查看权限客户", "1"),
    IS_ENABLE_GEO_MANUAL_MAINTENANCE("is_enable_geo_manual_maintenance", "是否开启地址GEO手动维护", "0"),
    MULTIPLE_UNIT("multiple_unit", "是否开启多单位", "0"),
    AVAILABLE_RANGE_DUPLICATED_CHECK("available_range_duplicated_check", "available_range_duplicated_check", "0"),
    /*产品关键字检索模式
    空-单关键词搜索
    and-多关键词且模式
    or-多关键词或模式*/
    PRODUCT_KEYWORD_SEARCH_MODE("product_keyword_search_mode", "产品关键字检索模式", ""),
    GET_PRICE_WHEN_CONVERT("get_price_when_convert", "转换时是否重新取价", "0"),
    PRICE_POLICY("price_policy", "价格政策", "0"),
    PRICE_POLICY_SALES_ORDER_OBJ("price_policy_SalesOrderObj", "销售订单使用价格政策", "0"),
    PRICE_POLICY_QUOTE_OBJ("price_policy_QuoteObj", "报价单使用价格政策", "0"),
    /**
     * 1、展示
     * 0、不展示（default）
     */
    SHOW_PRICE_POLICY_NAME("show_price_policy_name", "移动端订单选产品，促销产品是否显示价格政策名称", "0"),
    GET_PRICE_WHEN_COPY("get_price_when_copy", "复制订单时是否重新取价", "1"),
    GET_PRICE_WHEN_COPY_QUOTE("get_price_when_copy_quote", "复制报价单时是否重新取价", "1"),
    GET_PRICE_WHEN_COPY_CONTRACT("get_price_when_copy_contract", "复制销售合同时是否重新取价", "1"),
    GET_PRICE_WHEN_COPY_NEWOPPORTUNITY("get_price_when_copy_newopportunity", "复制商机2.0时是否重新取价", "1"),
    IS_TEST_CALCULATE("is_test_calculate", "是否开启报价单报价试算", "0"),
    MAP_MODE_SETTING_TYPE("map_mode_setting_type", "客户地图颜色配置", ""),
    ENFORCE_PRICE_POLICY_PRIORITY("enforce_price_policy_priority", "是否强制执行价格政策优先级", "0"),
    WHETHER_FILTER_ORDER_SELECT_PRODUCT("whether_filter_order_select_product", "订单选择产品，产品选择页面，增加产品是否过滤配置", "0"),
    TENANT_WHETHER_FILTER_ORDER_SELECT_PRODUCT("tenant_whether_filter_order_select_product", "订单选择产品，产品选择页面，增加产品是否过滤配置, 租户级", "0"),
    DELIVERY_NOTE_STATUS("delivery_note_status", "开启发货单", "0"),
    ORDER_TO_QUOTE_DEFAULT_VALUE_COVER("order_to_quote_default_value_cover", "报价单转订单，收货人、收货人电话、收货人地址、仓库、默认值不覆盖映射值-灰度功能", "0"),
    CHANGE_BUSINESS_QUERY("change_business_query", "工商查询是否包含个体户（0-不勾选 1-勾选）", "0"),
    ALLOW_AFTER_ACTION_CHANGE("allow_after_action_change", "允许流程后动作更新成交状态和最后一次成交时间字段（0 不启用  1启用）", "0"),
    ALL_PRODUCT_DISPLAY_PROMOTION("all_product_display_promotion", "促销本品为所有产品时是否要显示促", "0"),

    /**
     * 0 未开启 1 开启中 2 已经开启3 开启失败
     */
    ACCOUNTS_RECEIVABLE_STATUS_SFA("accounts_receivable_status_sfa", "应收管理是否开启", "0"),
    /**
     * amount 发票金额  count 开票数量 none 隐藏
     */
    INVOICE_SHOW_QUICK_OP_RULE("invoice_show_quick_op_rule", "开票明细快捷操作显示规则", "amount"),
    /**
     * 0 不显示  1 显示
     */
    INVOICE_SHOW_SUM_DATA("invoce_show_sum_data", "开票界面是否显示汇总信息", "1"),
    //公海、线索池回收规则最大规则数量
    POOL_RECYCLING_RULE_MAX_NUMBER("pool_recycling_rule_max_number", "公海、线索池回收规则最大规则数量", "30"),
    /**
     * 0 默认不开启此功能
     */
    ORDER_ENLARGE_EDIT_PRIVILEGE("order_enlarge_edit_privilege", "订单编辑权限扩大，不限于订单管理员和财务", "0"),
    PROMOTION_ORDER_RECORD_TYPE("promotion_order_record_type", "促销根据适配订单业务类型", ""),
    IGNORE_PRICE_BOOK_VALID_PERIOD("ignore_price_book_valid_period", "是否自定义匹配价目表有效期", "0"),
    MATCH_PRICE_BOOK_VALID_FIELD("match_price_book_valid_field", "匹配价目表有效期的字段", ""),
    /**
     * 0、子件产品跟随母件选择价目表
     * 1、子件产品按照客户适配的可售范围中价目表优先级带出
     */
    BOM_ADAPTATION_PRICE_LIST_RULES("bom_adaptation_price_list_rules", "子件适配价目表带出规则", "0"),
    /**
     * 业务函数存储
     */
    BIZ_FUNCTION("biz_function", "业务函数存储", ""),

    /**
     * 默认原来逻辑：产品组合.价格(元)（原始）+sum(选中子产品.调整价格（最新）*数量)-sum(默认子产品.调整价格（之前）*数量（默认）)
     */
    BOM_PRICE_CALCULATION_CONFIGURATION("bom_price_calculation_configuration", "产品包价格计算公式", "0"),
    MODULE_SALE_CONTRACT("sale_contract", "销售合同", "0"),
    PARTNER_TREE_FIELDS("partner_tree_fields", "合作伙伴层级字段", "name"),
    NEW_OPPORTUNITY_TREE_FIELDS("new_opportunity_tree_fields", "商机2.0伙伴层级字段", "name"),
    /**
     * 项目或阶段是否开启系统自动计算完成度
     */
    CONFIG_PROJ_STAGE_AUTO_CALCULATE_COMPLETE("config_proj_stage_auto_calculate_complete", "项目或阶段是否开启系统自动计算完成度", "0"),

    /**
     * 赠品费用分摊依据
     * price_book_price 价目表价格
     * product_price 产品档案价格
     */
    GIFT_AMORTIZE_BASIS("gift_amortize_basis", "赠品费用分摊依据", "price_book_price"),

    GIFT_ATTEND_AMORTIZE("gift_attend_amortize", "赠品参与分摊", "0"),

    REBATE("rebate", "返利", "0"),

    COUPON("coupon", "优惠券", "0"),

    /**
     * 产品分类，展示类型，tree or list
     * 1. tree
     * 2. list
     */
    CATEGORY_MODEL_TYPE("category_model_type", "产品分类展示形式", "2"),

    HOSPITAL_STATUS("hospital_status", "医疗数据开关", "0"),

    /**
     * 联系人负责人规则设置
     * 0 false; 1 true
     */
    CONTACT_OWNER_RULE_SETTING("contact_owner_rule_setting", "联系人负责人规则设置", "1"),

    /**
     * capsule、胶囊模式
     * tiled、平铺模式
     * last_level_tiled、末级平铺模式
     */
    MULTI_SPEC_DISPLAY_STYLE("multi_spec_display_style", "多规格商品选择产品样式", "capsule"),

    VIRTUAL_EXTENSION("virtual_extension", "商品/产品虚拟字段", "0"),

    AVAILABLE_RANGE_FILTER("available_range_filter", "可售范围支持过滤", "{\"status\":\"0\",\"filter_field\":\"\",\"filter_function\":\"\"}"),

    AVAILABLE_RANGE_FILTER_CONDITION("available_range_filter_condition", "可售范围过滤条件", "{\"filter_field\":\"\",\"filter_value\":\"\"}"),

    INPUT_CUSTOM_FIELDS("input_custom_fields", "移动端加车支持输入字段", "0"),

    RECENT_ORDER("recent_order", "最近订购", "0"),

    /**
     * 是否展示-注销异常企业配置项
     * 0不展示
     * 1展示
     */
    BUSINESS_ALLOWCONFIG_NO_ABNORMAL("business_allowconfig_no_abnormal", "是否展示-注销异常企业配置项", "0"),

    /**
     * 注销异常企业配置项
     * 0不展示
     * 1展示
     */
    BUSINESS_NO_ABNORMAL("business_no_abnormal", "注销异常企业配置项", "0"),
    /**
     * 1：关闭
     * 0：没关闭
     * 新企业,，默认都是关闭
     */
    CLOSE_OLD_CATEGORY("close_old_category", "关闭了老的产品分类", "0"),
    CATEGORY_TOTAL_LIMIT("category_total_limit", "分类总数限制", "5000"),
    MULTI_UNIT_PRICE_BOOK("multi_unit_price_book", "多单位价目表", "0"),

    PROCUREMENT_TRANSFER_SETTING("procurement_transfer_setting", "招投标转换设置", "AccountObj"),


    DHT_SALES_ORDER_RECORD_TYPE("dht_sales_order_record_type", "订货通订单业务类型", ""),
    SHOPPING_MALL_MODE("shopping_mall_mode", "订货通商城模式开关", "1"),
    INIT_MASTER_DATA_APP("init_master_data_app", "开启客户主数据按钮", "0"),

    //新建编辑客户当客户名称与系统中已有客户名称重复时进行提示
    CHECK_ACCOUNT_NAME_DUPLICATED ("check_account_name_duplicated", "新建编辑客户当客户名称与系统中已有客户名称重复时进行提示", "1"),

    /**
     * 价格政策开启后，可以开启手工改价，修改价目表价格
     * 0、不支持修改 默认值
     * 1、支持修改
     */
    ALLOW_EDIT_PRICE_BOOK_PRICE("allow_edit_price_book_price","是否允许编辑价目表价格","0"),

	MUST_TRANSFER_BY_TYPE("must_transfer_by_type","根据业务类型设置必转",""),

	QYWX_CHANGE_OWNER_SYNC_OBJ("qywx_change_owner_sync_obj", "企业微信同步负责人配置",""),

    CONTACT_MEMBER_RELATIONSHI_FUNC_SETTING("contact_member_relationship_func_setting","联系人与成员关系图谱功能是否开启设置","0"),

    CONTACT_MEMBER_RELATIONSHI_FUNC_ADDRESS_BOOK_SETTING("contact_member_relationship_func_addressBook_setting","联系人与成员关系图谱中获取通讯录功能是否开启设置","0"),

    REBATE_POLICY_SOURCE("rebate_policy_source", "返利产生来源", "[{\"api_name\":\"SalesOrderObj\",\"api_name__r\":\"销售订单\",\"account_filed\":\"account_id\",\"detail_api_name\":\"SalesOrderProductObj\",\"detail_api_name__r\":\"订单产品\",\"used\":true,\"type\":\"system\"}]"),

    SEND_USER("send_user","发送前端日志用户设置","{\"allUser\":false,\"user\":[],\"filter\":[]}"),
    /**
     * 0-不创建分摊数据（默认值）
     * 1-创建分摊数据（灰度此功能的企业，为此状态）
     * 整单额外调整，是否允许创建分摊明细数据
     */
    DYNAMIC_ALLOW_AMORTIZE("dynamic_allow_amortize", "整单额外调整，是否允许创建分摊明细数据", "0"),


    BOM_DUPLICATE_CHECK("bom_duplicate_check", "BOM查重校验", "0"),

    IS_OPEN_AUTO_MATCH("is_open_auto_match", "是否开启自动核销", "0"),
    AUTO_MATCH_RULE("auto_match_rule", "自动核销匹配规则", "[]"),
    MULTI_CURRENCY_CONFIG("multi_currency_config", "多币种配置", "0"),
    LOYALTY_PLUGIN_SWITCH_SALES_ORDER("sfa_loyalty_plugin_switch_apply_SalesOrderObj", "会员插件/销售订单", "false"),
    ;



    private final String key;
    private final String defaultValue;
    private final String label;


    public static final Map<String, ConfigType> CONFIG_TYPE_MAP;

    static {
        Map<String, ConfigType> map = Maps.newHashMap();
        for (ConfigType configType : ConfigType.values()) {
            map.put(configType.getKey(), configType);
        }
        CONFIG_TYPE_MAP = ImmutableMap.copyOf(map);
    }

    ConfigType(String key, String label, String defaultValue) {
        this.key = key;
        this.defaultValue = defaultValue;
        this.label = label;
    }

    public String getKey() {
        return this.key;
    }

    public String getLabel() {
        return this.label;
    }

    public String getDefaultValue() {
        return this.defaultValue;
    }

    public String getValue(String value) {
        if (this.key.equals("41")) {
            if (value.split(",").length == 3) {
                value += ",0";
            }
            return value;
        } else {
            return value;
        }
    }

    public static ConfigType getConfigType(String key) {
        ConfigType configType = ConfigType.CONFIG_TYPE_MAP.get(key);
        return null == configType ? ConfigType.None : configType;
    }

    public static List<String> getAllKeys() {
        List<String> rst = Lists.newArrayList();
        ConfigType[] values = ConfigType.values();
        if (values != null && values.length > 0) {
            for (ConfigType value : values) {
                rst.add(value.getKey());
            }
        }
        return rst;
    }
}
