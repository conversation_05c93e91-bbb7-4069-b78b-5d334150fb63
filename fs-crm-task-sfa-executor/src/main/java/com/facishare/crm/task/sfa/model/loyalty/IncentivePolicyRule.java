package com.facishare.crm.task.sfa.model.loyalty;

import com.facishare.crm.task.sfa.model.RuleWhere;
import com.facishare.crm.task.sfa.services.loyalty.util.IncentivePolicyRuleConstants;
import com.facishare.paas.metadata.api.IObjectData;
import com.google.common.collect.Lists;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;

import java.util.List;

/**
 * 忠诚度规则
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class IncentivePolicyRule {
    private String id;
    private String name;
    private String activeStatus;
    private String policyId;
    private Integer groupNo;
    private Integer orderField;
    private String conditionContent;
    private String functionInfo;
    private String action;
    private IObjectData srcData;
    /**
     * 是否有规则引擎数据，规则为空或者规则只有政策高级属性和会员政策高级属性的都没有
     */
    private boolean hasRuleCondition;
    /**
     * 是否有政策高级属性和会员政策高级属性条件
     */
    private boolean hasPolicyCondition;

    @Builder.Default
    private List<IncentivePolicyAction> actions = Lists.newArrayList();
    @Builder.Default
    List<RuleWhere.FiltersBean> ruleFunctionDataList = Lists.newArrayList();

    @Builder.Default
    List<RuleWhere.FiltersBean> attributeDataList = Lists.newArrayList();

    public boolean hasCondition(String condition) {
        return StringUtils.isNotEmpty(condition) && !IncentivePolicyRuleConstants.EMPTY_ARRAY_SIGN.equals(condition);
    }

}
