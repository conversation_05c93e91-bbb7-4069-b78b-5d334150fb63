package com.facishare.crm.task.sfa.model;

import lombok.Data;

/**
 * <AUTHOR> lik
 * @date : 2023/5/26 18:21
 */

public interface RiskBrainAsyncModel {

    @Data
    class AccountCreateApplyResult {
        String queryName;
        AccountCreateApplyQueryResult queryResult;
    }
    @Data
    class AccountCreateApplyQueryResult{
        String orderId;
        String accountName;
        String encryptedPassword;
    }
}
