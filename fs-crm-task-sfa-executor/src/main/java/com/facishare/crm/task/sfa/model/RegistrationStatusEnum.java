package com.facishare.crm.task.sfa.model;

/**
 * 登记状态 class
 *
 * <AUTHOR>
 * @date 2019/3/29
 */


import java.util.Objects;
import java.util.Optional;

public enum RegistrationStatusEnum {
    //在营
    in_the_camp("01", "在营"),
    //停业
    closed("02", "停业"),
    //注销
    the_cancellation("03", "注销"),
    //吊销
    revoked("04", "吊销"),
    //其他
    other("other", "其他");

    private final String status;
    private final String label;

    RegistrationStatusEnum(String status, String label) {
        this.status = status;
        this.label = label;
    }

    public static Optional<RegistrationStatusEnum> get(String status) {
        for (RegistrationStatusEnum statusEnum : RegistrationStatusEnum.values()) {
            if (Objects.equals(statusEnum.status, status)) {
                return Optional.of(statusEnum);
            }
        }
        return Optional.empty();
    }

    public String getStatus() {
        return status;
    }

    public String getLabel() {
        return label;
    }
}
