package com.facishare.crm.task.sfa.model;

import io.protostuff.ProtobufIOUtil;
import io.protostuff.Schema;
import io.protostuff.runtime.RuntimeSchema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PolicyLimitObjTypeModel {

    private List<String> configApiNames;

    private String tenantId;

    public void fromProto(byte[] bytes) {
        Schema schema = RuntimeSchema.getSchema(getClass());
        ProtobufIOUtil.mergeFrom(bytes, this, schema);
    }
}
