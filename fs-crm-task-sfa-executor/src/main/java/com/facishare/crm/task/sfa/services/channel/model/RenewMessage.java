package com.facishare.crm.task.sfa.services.channel.model;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * ============================================================
 *
 * @Description:
 * @CreatedBy: Sundy on 2025-05-23
 * ============================================================
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class RenewMessage extends BaseMessage {
    private Long outTenantId;
    private String objectDataId;
    private String objectApiName;
    private String partnerAgreementDetailId;
    private String signSchemeId;
    private Boolean pass;
}
