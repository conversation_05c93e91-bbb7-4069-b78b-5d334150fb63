package com.facishare.crm.task.sfa.model.rebate;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;

@Data
public class RebatePolicyRuleFunctionData implements Serializable {
    @J<PERSON>NField(name = "name_space")
    @JsonProperty("name_space")
    private String nameSpace;
    @JSONField(name = "name")
    @JsonProperty("name")
    private String name;
    @JSONField(name = "api_name")
    @JsonProperty("api_name")
    private String apiName;
    @JSONField(name = "bind_object_api_name")
    @JsonProperty("bind_object_api_name")
    private String bindObjectApiName;
}
