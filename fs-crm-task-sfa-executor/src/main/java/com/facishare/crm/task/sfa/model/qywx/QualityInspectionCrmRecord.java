package com.facishare.crm.task.sfa.model.qywx;

import lombok.Data;

import java.util.List;

@Data
public class QualityInspectionCrmRecord {
    /**
     * 质检结果id
     */
    private String qualityInspectionId;
    /**
     * 质检结果类型
     */
    private String qualityInspectionType;
    private List<Integer> receiverIds;

    private Boolean group;
    private String employeeName;
    private String friendName;
    private String groupName;
    private String ruleName;
    private String sessionType;
    private String messageTime;
}
