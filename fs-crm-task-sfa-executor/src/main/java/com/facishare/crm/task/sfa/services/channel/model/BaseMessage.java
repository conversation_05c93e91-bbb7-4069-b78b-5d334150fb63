package com.facishare.crm.task.sfa.services.channel.model;

import com.facishare.crm.sfa.prm.api.enums.SignStatus;
import com.facishare.paas.appframework.core.model.User;
import lombok.Data;

/**
 * ============================================================
 *
 * @Description:
 * @CreatedBy: Sundy on 2025-05-20
 * ============================================================
 */
@Data
public class BaseMessage {
    private String tenantId;
    private String currentStatus;
    private String language;
    private String operator;

    public User createSystemUser() {
        return User.systemUser(this.getTenantId());
    }

    public User createUser() {
        return new User(this.getTenantId(), this.getOperator());
    }

    public SignStatus fetchCurrentStatus() {
        return SignStatus.from(getCurrentStatus());
    }
}
