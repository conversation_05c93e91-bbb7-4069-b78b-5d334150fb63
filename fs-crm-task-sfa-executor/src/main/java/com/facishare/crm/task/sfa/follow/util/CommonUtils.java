package com.facishare.crm.task.sfa.follow.util;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.facishare.paas.appframework.core.model.RequestContextManager;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.ActionContextExt;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.action.ActionContext;
import com.facishare.paas.metadata.api.action.IActionContext;

import java.util.List;

import static com.facishare.crm.openapi.Utils.*;
import static com.facishare.crm.openapi.Utils.OPPORTUNITY_API_NAME;

/**
 * @Description
 * <AUTHOR>
 * @Date 2019/9/18 10:59
 */

public class CommonUtils {


    public static User buildUser(String tenantId) {
        return buildUser(tenantId, "-10000", "", "");
    }

    public static User buildUser(String tenantId,String userId) {
        return buildUser(tenantId, userId, "", "");
    }
    private static User buildUser(String tenantId, String userId, String outUserId, String outTenantId) {
        return new User(tenantId, userId, outUserId, outTenantId);
    }


    public static IActionContext buildContext(User user) {
        return buildContext(user, false);
    }


    private static IActionContext buildContext(User user, boolean allowUpdateInvalid) {

        return ActionContextExt.of(user, RequestContextManager.getContext())
                .allowUpdateInvalid(allowUpdateInvalid)
                .setIsSpecifyTime(Boolean.TRUE)
                .getContext();
    }

    public static ActionContext buildContextAllInvalid(String tenantId){
        return buildContextAllInvalid(buildUser(tenantId));
    }

    public static ActionContext buildContextAllInvalid(User user){
        ActionContext actionContext = new ActionContext();
        actionContext.setEnterpriseId(user.getTenantId());
        actionContext.setUserId(user.getUserId());
        actionContext.setDbType("pg");
        actionContext.setPrivilegeCheck(false);
        actionContext.put("batch", true);
        actionContext.put("skip_version_change",true);
        return actionContext;
    }

	public static ActionContext buildActionContext(String tenantId){
		ActionContext actionContext = new ActionContext();
		actionContext.setEnterpriseId(tenantId);
		actionContext.setUserId(User.SUPPER_ADMIN_USER_ID);
		actionContext.setPrivilegeCheck(false);
		actionContext.setDbType("pg");
		actionContext.put("batch", true);
		actionContext.put("skip_version_change",true);
		return actionContext;
	}


    public static String getOwner(IObjectData objectData) {
        Object value = objectData.get("owner");
        if (null == value) {
            return null;
        } else {
            String str;
            if (value instanceof String) {
                return  (String)value;
            } else {
                str = JSON.toJSONString(value);
                return JSONObject.parseObject(str, List.class).get(0).toString();
            }
        }
    }

    public static boolean hasDealApiName(String apiName){
        if (SALES_ORDER_API_NAME.equalsIgnoreCase(apiName) ||
                CONTRACT_API_NAME.equalsIgnoreCase(apiName) ||
                NEW_OPPORTUNITY_API_NAME.equalsIgnoreCase(apiName) ||
                OPPORTUNITY_API_NAME.equalsIgnoreCase(apiName)){
            return true;
        }
        return false;
    }
}
