package com.facishare.crm.task.sfa.executor.consumer;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONException;
import com.facishare.crm.sfa.audit.log.context.SFALogContext;
import com.facishare.crm.task.sfa.common.AllocateGuavaCacheUtils;
import com.facishare.crm.task.sfa.common.SfaTaskRateLimiterService;
import com.facishare.crm.task.sfa.model.LeadsAutoAllocateManyMessage;
import com.facishare.crm.task.sfa.model.LeadsAutoAllocateMessage;
import com.facishare.crm.task.sfa.services.ILeadsAutoAllocateService;
import com.facishare.crm.task.sfa.services.LeadsAutoAllocateService;
import com.facishare.crm.task.sfa.util.LeadsUtil;
import com.facishare.paas.appframework.common.mq.RocketMQMessageListener;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.metadata.util.SpringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.rocketmq.common.message.MessageExt;
import org.springframework.beans.factory.annotation.Autowired;

import java.nio.charset.Charset;
import java.util.List;

@Slf4j
public class AllocateListener implements RocketMQMessageListener {

    @Autowired
    private AllocateGuavaCacheUtils cacheUtils;

    @Autowired
    protected SfaTaskRateLimiterService sfaTaskRateLimiterService;


    @Override
    public void consumeMessage(List<MessageExt> list) {
        if (CollectionUtils.empty(list)) {
            return;
        }

        for (MessageExt message : list) {
            log.debug("message:{}", message);
            if (message.getReconsumeTimes() > 1) {
                log.warn("message:{} reconsumeTimes:{}", message.getMsgId(), message.getReconsumeTimes());
                continue;
            }
            consumeMessage(message);
        }
    }

    private void consumeMessage(MessageExt message) {
        String body = new String(message.getBody(), Charset.forName("UTF-8"));
        log.debug("message body:{}", body);
        switch (message.getTags()) {
            case "leads_allocate":
                try {
                    LeadsAutoAllocateMessage msg = JSON.parseObject(body, LeadsAutoAllocateMessage.class);
                    ILeadsAutoAllocateService leadsAutoAllocateService;
                    if (Boolean.TRUE.equals(LeadsUtil.skipAllocateTenantId(msg.getTenantId()))) {
                        return;
                    }
                    if (StringUtils.isBlank(msg.getTenantId())) {
                        log.warn("message body is null or objectId is null or tenantId is null");
                        return;
                    }
                    sfaTaskRateLimiterService.getLeadsAllocateRateLimiter().acquire();
                    SFALogContext.putVariable("bizName", "crm_allocate_task_sfa_group");
                    leadsAutoAllocateService = SpringUtil.getContext().getBean(LeadsAutoAllocateService.class);
                    if (msg.getBatch() == 1) {
                        LeadsAutoAllocateManyMessage b_msg = JSON.parseObject(body, LeadsAutoAllocateManyMessage.class);
                        log.info("queueId:{},messageId:{},reconsumeTimes:{},messageBody:{}", message.getQueueId(), message.getMsgId(), message.getReconsumeTimes(), b_msg);
                        leadsAutoAllocateService.excuteMany(b_msg);
                    } else {
                        log.info("queueId:{},messageId:{},reconsumeTimes:{},messageBody:{}", message.getQueueId(), message.getMsgId(), message.getReconsumeTimes(), msg);
                        if (!cacheUtils.hitCache(msg.getTenantId() + "_" + msg.getObjectId())) {
                            leadsAutoAllocateService.execute(msg);
                        } else {
                            log.warn("duplicated allocated messageId:{},{}", message.getMsgId(), msg.getObjectId());
                        }
                    }
                    log.info("queueId:{},messageId:{} end", message.getQueueId(), message.getMsgId());
                } catch (JSONException ex) {
                    LeadsAutoAllocateManyMessage msg = JSON.parseObject(body, LeadsAutoAllocateManyMessage.class);
                    log.info("queueId:{},messageId:{},reconsumeTimes:{},messageBody:{}", message.getQueueId(), message.getMsgId(),message.getReconsumeTimes(), msg);
                    ILeadsAutoAllocateService leadsAutoAllocateService = SpringUtil.getContext().getBean(LeadsAutoAllocateService.class);
                    leadsAutoAllocateService.excuteMany(msg);
                } finally {
                    SFALogContext.clearContext();
                }
                break;
        }

    }
}
