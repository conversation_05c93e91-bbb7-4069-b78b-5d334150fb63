package com.facishare.crm.task.sfa.model;

import com.facishare.crm.sfa.prm.api.enums.SignStatus;
import com.facishare.crm.task.sfa.util.ObjectDataUtils;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.MetaDataServiceImpl;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.util.SpringUtil;
import com.google.common.collect.Maps;

import javax.validation.constraints.NotNull;
import java.util.Map;

/**
 * Created by Sundy on 2024/9/19 16:24
 */
public class PartnerDataExt {
    private final MetaDataServiceImpl metaDataService = SpringUtil.getContext().getBean(MetaDataServiceImpl.class, "metaDataService");


    public static final String PARTNER_OBJ = "PartnerObj";
    public static final String SIGNING_STATUS = "signing_status";
    public static final String EXPIRED_TIME = "expired_time";

    private IObjectData objectData;

    private PartnerDataExt() {
        // ignore constructor
    }

    public static @NotNull PartnerDataExt of(@NotNull IObjectData partnerData) {
        PartnerDataExt partnerDataExt = new PartnerDataExt();
        partnerDataExt.objectData = partnerData;
        return partnerDataExt;
    }

    public IObjectData raw() {
        return objectData;
    }

    public Long getExpiredTime() {
        return getExpiredTime(null);
    }

    public IObjectData renewExpiredTime(User user, Long expiredTime) {
        Map<String, Object> filedMapping = Maps.newHashMap();
        filedMapping.put(EXPIRED_TIME, expiredTime);
        return metaDataService.updateWithMap(user, objectData, filedMapping);
    }

    public IObjectData changeSigneStatus(User user, SignStatus signingStatus) {
        Map<String, Object> filedMapping = Maps.newHashMap();
        filedMapping.put(SIGNING_STATUS, signingStatus.getStatus());
        return metaDataService.updateWithMap(user, objectData, filedMapping);
    }

    public Long getExpiredTime(Long defaultValue) {
        return ObjectDataUtils.getValue(this.objectData, EXPIRED_TIME, Long.class, defaultValue);
    }

    public SignStatus getSigningStatus() {
        String value = ObjectDataUtils.getValue(this.objectData, SIGNING_STATUS, String.class, null);
        return SignStatus.find(value, null);
    }
    public SignStatus getSigningStatus(SignStatus defaultStatus) {
        String value = ObjectDataUtils.getValue(this.objectData, SIGNING_STATUS, String.class, null);
        return SignStatus.find(value, defaultStatus);
    }
}
