package com.facishare.crm.task.sfa.services.channel.utils;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * ============================================================
 *
 * @Description:
 * @CreatedBy: Sundy on 2025-05-23
 * ============================================================
 */
public class StepRecorder {
    private static final ThreadLocal<List<Step>> STEP_HOLDER = ThreadLocal.withInitial(ArrayList::new);

    /**
     * 记录步骤并计算耗时
     */
    public static void record(String name) {
        long now = System.currentTimeMillis();
        List<Step> steps = STEP_HOLDER.get();

        long durationSinceLast = steps.isEmpty() ? 0 : now - steps.get(steps.size() - 1).timestamp;
        steps.add(new Step(name, now, durationSinceLast));
    }

    /**
     * 获取格式化后的字符串，例如：[加载配置 +0ms] -> [执行操作 +23ms]
     */
    public static String formatSteps() {
        List<Step> steps = STEP_HOLDER.get();
        if (steps.isEmpty()) return "[]";

        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < steps.size(); i++) {
            Step s = steps.get(i);
            sb.append("[").append(s.name).append(" +").append(s.sinceLastMs).append("ms]");
            if (i != steps.size() - 1) {
                sb.append(" -> ");
            }
        }
        return sb.toString();
    }

    public static String formatAndClearSteps() {
        try {
            return formatSteps();
        } finally {
            clear(); // 保证一定清理 ThreadLocal，防止泄漏
        }
    }

    /**
     * 清除步骤数据
     */
    public static void clear() {
        STEP_HOLDER.remove();
    }

    /**
     * 内部类表示一个步骤
     */
    private static class Step {
        String name;
        long timestamp;
        long sinceLastMs;

        Step(String name, long timestamp, long sinceLastMs) {
            this.name = name;
            this.timestamp = timestamp;
            this.sinceLastMs = sinceLastMs;
        }
    }
}
