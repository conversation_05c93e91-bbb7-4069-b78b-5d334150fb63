package com.facishare.crm.task.sfa.enums;

import cn.hutool.core.exceptions.ValidateException;
import com.facishare.paas.I18N;
import lombok.Getter;

import static com.facishare.crm.task.sfa.util.constant.I18nKeyConstants.PRM_PARAM_PROTOCOL_ERROR;

/**
 * <AUTHOR>
 * @time 2024-07-03 10:40
 * @Description
 */
@Getter
public enum ChannelStage {
    REGISTERED("registered"),
    SIGNED("signed"),
    RENEWAL("renewed");

    private final String stage;

    ChannelStage(String stage) {
        this.stage = stage;
    }

    public static ChannelStage fromString(String progress) {
        for (ChannelStage e : values()) {
            if (e.stage.equals(progress)) {
                return e;
            }
        }
        throw new ValidateException(I18N.text(PRM_PARAM_PROTOCOL_ERROR, "askProgress"));
    }
}
