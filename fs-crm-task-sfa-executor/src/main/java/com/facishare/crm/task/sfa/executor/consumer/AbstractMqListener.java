package com.facishare.crm.task.sfa.executor.consumer;

import com.fxiaoke.rocketmq.consumer.AutoConfMQPushConsumer;
import org.apache.rocketmq.client.consumer.listener.MessageListenerConcurrently;
import org.springframework.context.ApplicationListener;
import org.springframework.context.event.ContextRefreshedEvent;

import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;

public abstract class AbstractMqListener implements ApplicationListener<ContextRefreshedEvent> {

    private AutoConfMQPushConsumer consumer;


    public abstract String configName();

    public abstract String sectionNames();

    public abstract MessageListenerConcurrently handle();

    @PostConstruct
    public void init() {
        consumer = new AutoConfMQPushConsumer(configName(), sectionNames(), handle());
    }

    @PreDestroy
    public void close() {
        consumer.close();
    }

    @Override
    public void onApplicationEvent(ContextRefreshedEvent event) {
        if (consumer != null && event.getApplicationContext().getParent() == null) {
            consumer.start();
        }
    }
}
