package com.facishare.crm.task.sfa.executor.consumer;

import com.alibaba.fastjson.JSON;
import org.apache.rocketmq.common.message.MessageExt;
import com.facishare.crm.task.sfa.common.SfaTaskRateLimiterService;
import com.facishare.crm.task.sfa.model.DDSBody;
import com.facishare.crm.task.sfa.services.MasterDataBizService;
import com.facishare.paas.appframework.common.mq.RocketMQMessageListener;
import com.facishare.paas.metadata.exception.MetadataServiceException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.nio.charset.StandardCharsets;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/5/27 15:57
 */
@Service
@Slf4j
public class MasterDataAppListener implements RocketMQMessageListener {
    @Autowired
    private MasterDataBizService masterDataBizService;
    @Autowired
    private SfaTaskRateLimiterService sfaTaskRateLimiterService;

    @Override
    public void consumeMessage(List<MessageExt> messageExtList) {
        if (CollectionUtils.isEmpty(messageExtList)) {
            return;
        }
        for (MessageExt msg : messageExtList) {
            String body = new String(msg.getBody(), StandardCharsets.UTF_8);
            DDSBody ddsBody = JSON.parseObject(body, DDSBody.class);
            try {
                log.info("MasterDataListener log trace start body:{}", body);
                consumeMessage(ddsBody);
            } catch (Exception e) {
                log.error("MasterDataListener log trace error, body:{}", body, e);
            }

        }
    }

    private void consumeMessage(DDSBody ddsBody) throws MetadataServiceException {
        sfaTaskRateLimiterService.getMasterDataAppLimiter().acquire();
        masterDataBizService.backFillMasterDataField(ddsBody);
    }
}
