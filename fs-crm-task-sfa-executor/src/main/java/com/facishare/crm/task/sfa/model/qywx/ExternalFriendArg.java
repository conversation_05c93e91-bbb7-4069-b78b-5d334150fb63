package com.facishare.crm.task.sfa.model.qywx;

import com.facishare.paas.metadata.api.IObjectData;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Objects;

@Data
@NoArgsConstructor
public class ExternalFriendArg {
    /**
     * 企微员工id
     * qywx_user_id
     */
    private String userId;
    private String enterpriseWechatUserId;

    public ExternalFriendArg(String userId, String enterpriseWechatUserId) {
        this.userId = userId;
        this.enterpriseWechatUserId = enterpriseWechatUserId;
    }

    public String getUnique() {
        return userId + "/" + enterpriseWechatUserId;
    }

    public static String getUnique(IObjectData WechatFriendsRecord) {
        return WechatFriendsRecord.get("qywx_user_id", String.class) + "/" + WechatFriendsRecord.get("enterprise_wechat_user_id", String.class);
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        ExternalFriendArg that = (ExternalFriendArg) o;
        return Objects.equals(getUnique(), that.getUnique());
    }

    @Override
    public int hashCode() {
        return Objects.hash(getUnique());
    }
}
