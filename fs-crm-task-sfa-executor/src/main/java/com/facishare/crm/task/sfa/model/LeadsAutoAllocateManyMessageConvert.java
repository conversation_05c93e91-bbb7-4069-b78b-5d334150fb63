package com.facishare.crm.task.sfa.model;

import com.facishare.crm.sfa.audit.log.EntityConverter;
import com.facishare.crm.sfa.audit.log.model.AuditArg;

/**
 * <AUTHOR> gongchunru
 * @date : 2024/1/23 10:21
 * @description:
 */
public class LeadsAutoAllocateManyMessageConvert implements EntityConverter<LeadsAutoAllocateManyMessage> {

    @Override
    public AuditArg convert(LeadsAutoAllocateManyMessage leadsAutoAllocateManyMessage) {
        return AuditArg.builder()
                .ei(leadsAutoAllocateManyMessage.getTenantId())
                .objectApiName("LeadsObj")
                .build();
    }
}
