package com.facishare.crm.task.sfa.model;

import lombok.*;

/**
 * Demo class
 *
 * <AUTHOR>
 * @date 2019/10/25
 */

public interface TodoCheckEventModel {
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @ToString
    @Builder
    class TodoCheckEvent {
        public Integer tenantId;
        public Integer employeeId;
        public String bizType;
        public Integer notDealCount;
        public Long syncTimeStamp;
        public CrossUserInfo crossUserInfo;
        public String appId;
    }
    @Data
    class CrossUserInfo {
        public Integer upEnterpriseId;
        public Integer upEmployeeId;
        public Long outUserId;
        public Long outTenantId;
    }
}
