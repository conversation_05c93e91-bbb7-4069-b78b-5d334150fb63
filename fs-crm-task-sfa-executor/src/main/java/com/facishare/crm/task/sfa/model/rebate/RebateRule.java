package com.facishare.crm.task.sfa.model.rebate;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

public interface RebateRule {
    @Data
    class UseRangeInfo {
        private String type;
        private String value;
    }
    @Data
    class CycleInfoData implements Serializable {
        @JSONField(name = "max_amount")
        @JsonProperty("max_amount")
        private BigDecimal maxAmount;
        @JSONField(name = "cycle_data")
        @JsonProperty("cycle_data")
        private List<CycleDataBean> cycleData;

        @Data
        public static class CycleDataBean implements Serializable {
            @JSONField(name = "left")
            @JsonProperty("left")
            private LeftBean left;
            @JSONField(name = "field_value")
            @JsonProperty("field_value")
            private BigDecimal fieldValue;
            @JSONField(name = "used_amount")
            @JsonProperty("used_amount")
            private BigDecimal usedAmount;
            @JSONField(name = "execute_type")
            @JsonProperty("execute_type")
            private String executeType;

            @Data
            public static class LeftBean implements Serializable {
                @JSONField(name = "field_name")
                @JsonProperty("field_name")
                private String fieldName;
                @JSONField(name = "field_name__s")
                @JsonProperty("field_name__s")
                private String fieldNameS;
                @JSONField(name = "object_api_name")
                @JsonProperty("object_api_name")
                private String objectApiName;
                @JSONField(name = "object_api_name__s")
                @JsonProperty("object_api_name__s")
                private String objectApiNameS;
                @JSONField(name = "field_name_type")
                @JsonProperty("field_name_type")
                private String fieldNameType;
                @JSONField(name = "default_value")
                @JsonProperty("default_value")
                private String defaultValue;
                @JSONField(name = "default_value__s")
                @JsonProperty("default_value__s")
                private String defaultValueName;
                @JSONField(name = "return_type")
                @JsonProperty("return_type")
                private String returnType;
                @JSONField(name = "decimal_places")
                @JsonProperty("decimal_places")
                private Integer decimalPlaces;
                @JSONField(name = "default_to_zero")
                @JsonProperty("default_to_zero")
                private Boolean defaultToZero;
            }
        }
    }

    @Data
    class ExpressionData implements Serializable {
        @JSONField(name = "execute_type")
        @JsonProperty("execute_type")
        private String executeType;
        @JSONField(name = "left")
        @JsonProperty("left")
        private CycleInfoData.CycleDataBean.LeftBean left;
        @JSONField(name = "operator")
        @JsonProperty("operator")
        private String operator;
        @JSONField(name = "operator__s")
        @JsonProperty("operator__s")
        private String operatorS;
        @JSONField(name = "right")
        @JsonProperty("right")
        private String right;
    }

    @Builder
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public class Rule {
        private String id;
        private String name;
        @JSONField(name = "last_modified_time")
        @JsonProperty("last_modified_time")
        private Long lastModifiedTime;
        @JSONField(name = "rule_content")
        @JsonProperty("rule_content")
        private String ruleContent;
        @JSONField(name = "rule_type")
        @JsonProperty("rule_type")
        private String ruleType;
    }

    enum RuleOperator {
        EQUAL,
        ADD,
        SUBTRACT,
        MULTIPLY,
        DIVIDE
    }

    enum FieldNameType {
        FIELD,
        AGGREGATE
    }
}
