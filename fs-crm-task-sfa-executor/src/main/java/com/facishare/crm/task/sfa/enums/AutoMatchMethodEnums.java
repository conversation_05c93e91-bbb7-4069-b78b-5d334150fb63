package com.facishare.crm.task.sfa.enums;

import cn.hutool.core.exceptions.ValidateException;
import com.facishare.paas.I18N;
import lombok.Getter;

import static com.facishare.crm.task.sfa.util.constant.I18nKeyConstants.PRM_PARAM_PROTOCOL_ERROR;

/**
 *
 */
@Getter
public class AutoMatchMethodEnums {
    //根据客户关联信息进行匹配
    public static final String MATCH_RELATED_ACCOUNT_METHOD = "match_related_account_method";
    //根据订单关联信息进行匹配
    public static final String MATCH_RELATED_ORDER_METHOD = "match_related_order_method";
    //根据金额进行匹配
    public static final String MATCH_AMOUNT_METHOD = "match_amount_method";
    //根据币种进行匹配
    public static final String MATCH_CURRENCY_METHOD = "match_currency_method";
    //根据日期进行匹配
    public static final String MATCH_DATE_METHOD = "match_date_method";
}
