package com.facishare.crm.task.sfa.model.proxy.bi;

import com.alibaba.fastjson.JSONObject;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

public interface StatViewDataQuery extends BiCrmRestQuery {
    @Data
    @EqualsAndHashCode(callSuper = true)
    class Arg extends Page {
        private String viewId;
        /**
         * 场景id
         */
        private String defaultFilterOptionID;
        /**
         * 是否显示明细
         */
        private Integer showMode;
    }

    @Data
    class Result {
        private Integer code;
        private String message;
        private ResultData data;
    }

    @Data
    class ResultData {
        /**
         * 分页信息
         */
        private JSONObject page;
        /**
         * 表头结构信息
         */
        private List<JSONObject> displayFields;
        /**
         * 结果集
         */
        private List<List<JSONObject>> dataSet;
    }
}