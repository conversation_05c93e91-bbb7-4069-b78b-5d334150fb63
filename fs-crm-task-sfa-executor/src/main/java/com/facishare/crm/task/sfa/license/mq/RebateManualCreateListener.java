package com.facishare.crm.task.sfa.license.mq;

import com.alibaba.fastjson.JSON;
import com.facishare.crm.task.sfa.services.loyalty.LoyaltyManualService;
import org.apache.rocketmq.common.message.MessageExt;
import com.facishare.crm.task.sfa.model.RebateManualCreateMessage;
import com.facishare.crm.task.sfa.services.rebate.RebateManualCreateServiceImpl;
import com.facishare.paas.appframework.common.mq.RocketMQMessageListener;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import lombok.extern.slf4j.Slf4j;

import javax.annotation.Resource;
import java.nio.charset.StandardCharsets;
import java.util.List;

/**
 * 返利产生，手动产生执行器接收MQ入口
 */
@Slf4j
public class RebateManualCreateListener implements RocketMQMessageListener {
    public static final String MANUAL_CREATE_REBATE_TAG = "MANUAL_CREATE";
    public static final String REBATE_EACH_CREATE_TAG = "REBATE_EACH_CREATE";
    public static final String EVENT_ROLLBACK = "EVENT_ROLLBACK";
    public static final String EVENT_RETRY = "EVENT_RETRY";
    /**
     * 审批通过
     */
    public static final String RESULT_APPROVAL_PASS = "RESULT_APPROVAL_PASS";
    /**
     * 审批未通过
     */
    public static final String RESULT_APPROVAL_NOT_PASS = "RESULT_APPROVAL_NOT_PASS";
    /**
     * 预计返利
     */
    public static final String EXPECTED_REBATE__CREATE_TAG = "EXPECTED_REBATE_CREATE";

    @Resource
    private RebateManualCreateServiceImpl rebateCreateService;

    @Resource
    private LoyaltyManualService loyaltyService;

    @Override
    public void consumeMessage(List<MessageExt> list) {
        if (CollectionUtils.empty(list)) {
            return;
        }

        for (MessageExt message : list) {
            log.debug("message:{}", message);
            consumeMessage(message);
        }
    }

    private void consumeMessage(MessageExt message) {
        log.debug("consumeMessage: arg:{}", message);
        String tags = message.getTags();
        String messageBody = new String(message.getBody(), StandardCharsets.UTF_8);
        RebateManualCreateMessage createMessage = JSON.parseObject(messageBody, RebateManualCreateMessage.class);
        switch (tags) {
            case MANUAL_CREATE_REBATE_TAG:
            case REBATE_EACH_CREATE_TAG:
                rebateCreateService.create(createMessage);
                break;
            case EXPECTED_REBATE__CREATE_TAG:
                rebateCreateService.expectedRebate(createMessage);
                break;
            case EVENT_ROLLBACK:
                log.info("EVENT_ROLLBACK event:{}", message);
                loyaltyService.back(createMessage);
                break;
            case EVENT_RETRY:
                log.info("EVENT_RETRY event:{}", message);
                loyaltyService.retry(createMessage);
                break;
            case RESULT_APPROVAL_PASS:
                log.info("RESULT_APPROVAL_PASS event:{}", message);
                loyaltyService.calcResult(createMessage);
                break;
            case RESULT_APPROVAL_NOT_PASS:
                log.info("RESULT_APPROVAL_NOT_PASS event:{}", message);
                loyaltyService.calcResultNoPaas(createMessage);
                break;
            default:
                log.warn("unknown tags:{}", tags);
        }

    }
}
