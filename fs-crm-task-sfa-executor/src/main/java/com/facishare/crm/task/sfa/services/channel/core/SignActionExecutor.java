package com.facishare.crm.task.sfa.services.channel.core;

import com.facishare.crm.sfa.prm.platform.utils.ObjectUtils;
import com.facishare.crm.task.sfa.services.channel.ActionExecutor;
import com.facishare.crm.task.sfa.services.channel.ChannelMachine;
import com.facishare.crm.task.sfa.services.channel.enums.ChannelEvent;
import com.facishare.crm.task.sfa.services.channel.enums.RouterType;
import com.facishare.crm.task.sfa.services.channel.exception.ItemNullException;
import com.facishare.crm.task.sfa.services.channel.model.ChannelContext;
import com.facishare.crm.task.sfa.services.channel.model.SignMessage;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.core.model.User;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * ============================================================
 *
 * @Description:
 * @CreatedBy: Sundy on 2025-05-22
 * ============================================================
 */
@Slf4j
@Component
public class SignActionExecutor implements ActionExecutor {
    @Resource
    private ChannelMachine channelMachine;

    @Override
    public void execute(String body) {
        SignMessage signMessage = ObjectUtils.convertObject(body, SignMessage.class);
        if (signMessage == null) {
            throw new ItemNullException("signMessage is null");
        }
        User systemUser = signMessage.createSystemUser();
        I18N.setContext(systemUser.getTenantId(), signMessage.getLanguage());
        log.info("ChannelEventRouter#sign mq consume, message:{}", signMessage);
        log.info("ChannelEventRouter#sign begin");
        ChannelContext channelContext = ChannelContext.builder()
                .user(systemUser)
                .payload(signMessage)
                .build();
        if (signMessage.isPass()) {
            channelMachine.getInstance().fire(signMessage.fetchCurrentStatus(), ChannelEvent.SIGN_PASS, channelContext);
        } else {
            channelMachine.getInstance().fire(signMessage.fetchCurrentStatus(), ChannelEvent.SIGN_REJET, channelContext);
        }
        log.info("ChannelEventRouter#sign end");
    }

    @Override
    public RouterType getRouterType() {
        return RouterType.SIGN;
    }
}
