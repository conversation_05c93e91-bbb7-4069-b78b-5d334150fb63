package com.facishare.crm.task.sfa.model.qywx;

import com.facishare.paas.metadata.api.IObjectData;
import lombok.Data;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.Objects;

@Data
public class ChatSession {

    private Boolean group;
    private String chatId;
    /**
     * 员工id
     */
    private String ownerPlaintextId;
    /**
     * 可能是员工id也可能是客户
     */
    private String oppositeCipherId;
    /**
     * 还有企微id
     */
    private String externalFriendId;

    public ChatSession(QualityInspectionHitRecord.MsgDetail msgDetail) {
        group = !StringUtils.isEmpty(msgDetail.getChatId());
        chatId = msgDetail.getChatId();
        if (msgDetail.getSender() == null) {
            return;
        }
        Integer senderType = msgDetail.getSender().getType();
        String senderId = msgDetail.getSender().getId();
        String receiverFirstId = null;
        if (!CollectionUtils.isEmpty(msgDetail.getReceiverList())) {
            QualityInspectionHitRecord.ReceiverListItem receiverListItem = msgDetail.getReceiverList().get(0);
            receiverFirstId = receiverListItem.getId();
            Integer oppositeType = receiverListItem.getType();
            if (oppositeType != null && oppositeType == 2) {//客户
                externalFriendId = receiverFirstId;
            }
        }
        if (senderType == 1) {//员工
            ownerPlaintextId = senderId;
            oppositeCipherId = receiverFirstId;
        } else if (senderType == 2) {//客户
            oppositeCipherId = senderId;
            ownerPlaintextId = receiverFirstId;
            externalFriendId = senderId;
        }
    }

    public static String getSessionUnique(IObjectData session) {
        String wechatGroupId = session.get("wechat_group_id", String.class);
        if (!StringUtils.isEmpty(wechatGroupId)) {
            return session.get("opposite_plaintext_id", String.class);
        }
        return session.get("owner_cipher_id", String.class) + "/" + session.get("opposite_cipher_id", String.class);
    }

    public String getUnique() {
        if (Boolean.TRUE.equals(group)) {
            return chatId;
        }
        return ownerPlaintextId + "/" + oppositeCipherId;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        ChatSession that = (ChatSession) o;
        return Objects.equals(getUnique(), that.getUnique());
    }

    @Override
    public int hashCode() {
        return Objects.hash(getUnique());
    }
}
