package com.facishare.crm.task.sfa.enums;

/**
 * <AUTHOR>
 * @time 2024-01-18 15:38
 * @Description
 */
public enum WeChatMsgType {

    MSG_TEXT("text", "文本消息"),

    MSG_TEXT_CARD("textcard", "文本卡片消息"),
    MSG_NEWS_CARD("news", "图文消息（图片url）"),
    MINIPROGRAM_NOTICE("miniprogram_notice", "小程序通知消息")
    ;

    private String type; //消息类型
    private String description; //消息类型描述

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    WeChatMsgType(String type, String description){
        this.type = type;
        this.description = description;
    }
}
