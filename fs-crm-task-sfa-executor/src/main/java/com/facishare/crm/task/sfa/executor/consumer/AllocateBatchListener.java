package com.facishare.crm.task.sfa.executor.consumer;

import com.alibaba.fastjson.JSON;
import com.facishare.crm.sfa.audit.log.context.SFALogContext;
import com.facishare.crm.task.sfa.common.SfaTaskRateLimiterService;
import com.facishare.crm.task.sfa.model.LeadsAutoAllocateMessage;
import com.facishare.crm.task.sfa.services.ILeadsAutoAllocateService;
import com.facishare.crm.task.sfa.util.LeadsUtil;
import com.fxiaoke.rocketmq.consumer.AutoConfMQPushConsumer;
import com.fxiaoke.rocketmq.util.MessageHelper;
import com.github.trace.TraceContext;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.client.consumer.listener.ConsumeOrderlyStatus;
import org.apache.rocketmq.client.consumer.listener.MessageListenerOrderly;
import org.apache.rocketmq.common.message.MessageExt;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationListener;
import org.springframework.context.event.ContextRefreshedEvent;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;


/**
 * @Description
 * <AUTHOR>
 * @Date 2021/3/26 11:00
 */

@Slf4j
@Component
public class AllocateBatchListener implements ApplicationListener<ContextRefreshedEvent> {

    private AutoConfMQPushConsumer consumer;

    @Autowired
    private ILeadsAutoAllocateService leadsAutoAllocateService;

    @Autowired
    private SfaTaskRateLimiterService sfaTaskRateLimiterService;


    @PostConstruct
    public void init() {
        consumer = new AutoConfMQPushConsumer("sfa-recalculate-consumer", "sfa-leads-allocate-batch",(MessageListenerOrderly) (msgs, context) -> {
            if (!msgs.isEmpty()) {
                for(MessageExt msg: msgs) {
                    // 取出traceContext
                    MessageHelper.fillContextFromMessage(TraceContext.get(), msg);
                    consumeMessage(msg);
                }
            }
            return ConsumeOrderlyStatus.SUCCESS;
        });
    }


    @PreDestroy
    public void close() {
        consumer.close();
    }

    @Override
    public void onApplicationEvent(ContextRefreshedEvent event) {
        if (consumer != null && event.getApplicationContext().getParent() == null) {
            consumer.start();
        }
    }


    private void consumeMessage(MessageExt messageExt) {
        try {
            LeadsAutoAllocateMessage message = JSON.parseObject(messageExt.getBody(), LeadsAutoAllocateMessage.class);
            log.info("AllocateBatchListener LeadsAutoAllocateMessage, msgId:{},queue:{},reconsumeTimes:{},RecalculateMessage:{}", messageExt.getMsgId(),messageExt.getQueueId(),messageExt.getReconsumeTimes(),message);
            if (LeadsUtil.skipNewAllocateTenantId(message.getTenantId())){
                log.warn("AllocateBatchListener skipTenantId:{},objectId:{}",message.getTenantId(),message.getObjectId());
                return;
            }
            SFALogContext.putVariable("bizName","sfa-allocate-batch");
            sfaTaskRateLimiterService.getLeadsAllocateBatchRateLimiter().acquire();
            leadsAutoAllocateService.execute(message);
        } catch (Exception e) {
            log.error("AllocateBatchListener consumeMessage msgId:{}",messageExt.getMsgId(),e);
            throw new RuntimeException(e);
        } finally {
            SFALogContext.clearContext();
        }
    }
}
