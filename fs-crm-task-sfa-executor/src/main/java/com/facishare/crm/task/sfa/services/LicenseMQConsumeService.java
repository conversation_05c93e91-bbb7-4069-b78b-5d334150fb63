package com.facishare.crm.task.sfa.services;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.facishare.converter.EIEAConverter;
import com.facishare.crm.openapi.Utils;
import com.facishare.crm.task.sfa.common.SfaTaskRateLimiterService;
import com.facishare.crm.task.sfa.common.constants.CommonConstant;
import com.facishare.crm.task.sfa.common.gray.GrayUtils;
import com.facishare.crm.task.sfa.common.util.I18NKeyUtil;
import com.facishare.crm.task.sfa.common.util.Safes;
import com.facishare.crm.task.sfa.model.InsertOrUpdateResult;
import com.facishare.crm.task.sfa.model.LicenseModel;
import com.facishare.crm.task.sfa.mq.integral.TenantUtils;
import com.facishare.crm.task.sfa.rest.CRMMetaDataService;
import com.facishare.crm.task.sfa.rest.CRMMetaDataServiceProxy;
import com.facishare.crm.task.sfa.services.equityrelationship.EquityRelationshipInitService;
import com.facishare.crm.task.sfa.services.mcr.MCRInitService;
import com.facishare.crm.task.sfa.services.qywx.EnterpriseWechatEmployeeService;
import com.facishare.crm.task.sfa.services.riskbrain.RiskBrainAsyncService;
import com.facishare.crm.task.sfa.services.template.AtlasRelationTemplate;
import com.facishare.crm.task.sfa.util.*;
import com.facishare.enterprise.common.constant.FieldPermissionEnum;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.config.ConfigService;
import com.facishare.paas.appframework.config.ConfigValueType;
import com.facishare.paas.appframework.core.exception.PermissionError;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.*;
import com.facishare.paas.appframework.metadata.dto.auth.AddRoleRecordTypeModel;
import com.facishare.paas.appframework.metadata.dto.auth.BatchAddRoleRecordTypeModel;
import com.facishare.paas.appframework.metadata.multicurrency.RefreshInternational;
import com.facishare.paas.appframework.privilege.FunctionPrivilegeProxy;
import com.facishare.paas.appframework.privilege.FunctionPrivilegeServiceImpl;
import com.facishare.paas.appframework.privilege.dto.CreateFunctionPrivilege;
import com.facishare.paas.appframework.privilege.dto.FuncCodePrivilege;
import com.facishare.paas.appframework.privilege.util.PrivilegeConstants;
import com.facishare.paas.auth.model.AuthContext;
import com.facishare.paas.auth.model.params.request.AddRoleGroupArg;
import com.facishare.paas.auth.model.params.request.CreateRoleArg;
import com.facishare.paas.metadata.api.IObjectMappingRuleInfo;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.impl.describe.ObjectReferenceFieldDescribe;
import com.facishare.paas.metadata.service.impl.ObjectDataServiceImpl;
import com.fxiaoke.paas.auth.factory.FieldClient;
import com.fxiaoke.paas.auth.factory.RoleClient;
import com.github.jedis.support.JedisCmd;
import com.google.common.collect.ImmutableList;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

import static com.facishare.crm.task.sfa.common.constants.CommonConstant.*;
import static com.facishare.crm.task.sfa.rest.BiCrmRestProxy.getHeaders;
import static com.facishare.paas.appframework.privilege.FunctionPrivilegeProxy.HeaderUtil.buildHeaders;
import static com.facishare.paas.appframework.privilege.util.Headers.PAAS_PRIVILEGE_HEADDER;


@Service
@Slf4j
@Component
public class LicenseMQConsumeService {
    @Autowired
    CRMMetaDataService crmMetaDataService;
    @Autowired
    private FunctionPrivilegeServiceImpl functionPrivilegeService;
    @Autowired
    private FieldClient fieldClient;
    @Autowired
    private PrmService prmService;
    @Autowired
    private SfaTaskRateLimiterService sfaTaskRateLimiterService;
    @Autowired
    private DescribeLogicService describeLogicService;
    @Autowired
    private DescUtil descUtil;
    @Autowired
    private RecordTypeAuthProxy recordTypeAuthProxy;
    @Autowired
    private MasterDataBizService masterDataBizService;
    @Autowired()
    private JedisCmd SFAJedisCmd;
    @Autowired
    private ConfigService configService;
    @Autowired
    private RiskBrainAsyncService riskBrainAsyncService;
    @Autowired
    private EnterpriseWechatEmployeeService enterpriseWechatEmployeeService;
    @Autowired
    private EquityRelationshipInitService equityRelationshipInitService;
    @Autowired
    private MultiCurrencyLogicServiceImpl multiCurrencyLogicService;
    @Autowired
    private TenantUtils tenantUtils;
    @Autowired
    private LicenseHandlerFactory licenseHandlerFactory;
    @Autowired
    private ObjectMappingService objectMappingService;
    @Autowired
    private EIEAConverter eieaConverter;
    @Autowired
    private MCRInitService mcrInitService;
    @Autowired
    private CRMMetaDataServiceProxy crmMetaDataServiceProxy;
    @Autowired
    private SFALicenseService sfaLicenseService;
    @Autowired
    private AtlasRelationTemplate atlasRelationTemplate;
    @Resource
    private PartnerChannelService partnerChannelService;
    @Resource(name = "objectDataPgService")
    ObjectDataServiceImpl objectDataService;
    @Resource
    private RoleClient roleClient;
    @Resource
    FunctionPrivilegeProxy functionPrivilegeProxy;
    @Resource
    private PartnerChannelInitService partnerChannelInitService;

    public static final String KEY_SUB = "task-sfa-license-";
    private final static String ENTERPRISE_LIBRARY_APP = "enterprise_library_app";
    private final static String LEADS_DEDUPLICATION_APP = "leads_deduplication_app";
    private static final String ACCOUNTS_LEADS_LIMIT_APP = "accounts_leads_limit_app";
    private static final String MARKETING_INTEGRATION_APP = "marketing_integration_app";
    private static final String NEW_OPPORTUNITY_OBJ = "NewOpportunityObj";
    public static final String PROJECT_MANAGEMENT_APP = "project_management_app";
    private static final String INTERCONNECT_APP_BASIC_APP = "interconnect_app_basic_app";
    private static final String DNB_COMMERCIAL_INFOR_QUERY_APP = "dnb_commercial_infor_query_app";
    private static final String TIANYANCHA_API_INDUSTRIAL_QUERIES_APP = "tianyancha_api_industrial_queries_app";
    private static final String DEALER_EDITION = "dealer_edition";
    private static final String AGENT_EDITION = "agent_edition";
    private static final String BUSINESS_OPPORTUNITY_FORECAST_APP = "business_opportunity_forecast_app";
    private static final String BIDDING_INFO_MANAGE_APP = "bidding_info_manage_app";
    private static final String PRM_APP = "prm_app";
    //风险画像高级license
    private static final String CUSTOMER_RISK_MANAGEMENT_ADVANCE = "customer_risk_management_advance";
    //风险画像低级license
    private static final String CUSTOMER_RISK_MANAGEMENT_BASE = "customer_risk_management_base";
    //推拉单license
    private static final String PUSH_PULL_ORDER_APP = "push_pull_order_app";
    //客户树
    private static final String ACCOUNT_TREE_APP = "account_tree_app";
    //股权关系
    private static final String CUSTOMER_EQUITY_RELATIONSHIP_APP = "customer_equity_relationship_app";
    // 树形组件，AccountDepartmentObj
    private static final String TREE_COMPONENT_APP = "tree_component_app";
    // 树形组件，AccountDepartmentObj
    private static final String DBS_WITHOUT_LICENSE = "dnb_commercial_infor_query_app";
    private static final String OPPORTUNITY_FISHBONE_DIAGRAM_APP = "opportunity_fishbone_diagram_app";
    //客户树--客户
    private static final String SUB_ACCOUNT_TREE_APP = "sub_account_tree_app";
    //MCR
    private static final String KEY_ACCOUNTS_MANAGEMENT_INDUSTRY = "key_accounts_management_industry";
    //风险信息
    private static final String RISK_MANAGEMENT_STRENGTHEN_INDUSTRY = "risk_management_strengthen_industry";
    private static final String INDUSTRIAL_SEARCH_99_APP = "industrial_search_99_app";
    private static final String INDUSTRIAL_SEARCH_100PLUS_APP = "industrial_search_100plus_app";
    //rfm
    private static final String rfm = "rfm1111111111";
    // 运营工作台
    private static final String OPERATIONS_WORKBENCH_APP = "operations_workbench_app";

    private static final String USER_LICENSE_FXIAOKE_AI_PLATFORM_APP = "user_license_fxiaoke_ai_platform_app";
    private static final String FXIAOKE_AI_PLATFORM_APP = "fxiaoke_ai_platform_app";
    private static final String TENCENT_MEETING_INTEGRATION_APP = "tencent_conference_integration_app";

    public static final List<String> PARTNER_CHANNEL_LICENSE_LIST = ImmutableList.of("channel_partner_recruitment_app", "channel_partner_recruitment_industry");

    private static final String OUTLOOK_INTEGRATION_APP = "outlook_integration_app";
    private static final String MARKETING_OVERSEAS_PLUGIN_APP = "marketing_overseas_plugin_app";
    private static final String AI_INTERACTIVE_ASSISTANT_APP = "ai_interactive_assistant_app";
    private static final String AI_INTERACTIVE_ASSISTANT_LIMIT = "user_license_ai_interactive_assistant_user_limit";
    // 互联应用 AI License
    public static final String ER_AI_INTERACTIVE_ASSISTANT_LIMIT = "user_license_ai_interactive_assistant_externaluser_limit";

    public void execute(LicenseModel.LicenseMessage message) {
        log.info("LicenseMQConsumeService——message:{}", message);
        if (message == null
                || CollectionUtils.empty(message.getModuleCodes())) {
            return;
        }
        if (tenantUtils.isExclusiveCloudEnterprise(message.getTenantId())) {
            log.info("{} is exclusive cloud ei", message.getTenantId());
            return;
        }
        String key = KEY_SUB + message.getTenantId();
        Set<String> smembers = SFAJedisCmd.smembers(key);
        SFAJedisCmd.sadd(key, message.getModuleCodes().toArray(new String[message.getModuleCodes().size()]));
        SFAJedisCmd.expire(key, 5 * 60L);

        sfaTaskRateLimiterService.getLicenseMQConsumeInitRateLimiter().acquire();

        licenseHandlerFactory.handler(message);

        try {
            String tenantId = message.getTenantId();
            User user = User.systemUser(tenantId);
            try {
                if (message.getModuleCodes().contains(ENTERPRISE_LIBRARY_APP) || message.getModuleCodes().contains(ACCOUNT_TREE_APP) || message.getModuleCodes().contains(SUB_ACCOUNT_TREE_APP)) {
                    boolean needInit = true;
                    try {
                        IObjectDescribe enterpriseDescribe = AccountPathUtil.getObjectDescribe(user, Utils.ENTERPRISE_INFO_API_NAME);
                        if (enterpriseDescribe != null) {
                            needInit = false;
                        }
                    } catch (Exception e) {
                        needInit = true;
                    }
                    if (needInit) {
                        crmMetaDataService.initEnterpriseInfoObj(tenantId);
                    }
                }
            } catch (Exception e) {
                log.error("LicenseMQConsumeService ENTERPRISE_LIBRARY_APP arg:{}, error:", message, e);
            }

            try {
                if (message.getModuleCodes().contains(DNB_COMMERCIAL_INFOR_QUERY_APP)) {
                    CommercialInforQueryUtil.initCommercialInforQueryFunction(user, "DnbCommercialInforQuery",
                            "邓白氏工商查询");
                }
            } catch (Exception e) {
                log.error("LicenseMQConsumeService DNB_COMMERCIAL_INFOR_QUERY_APP arg:{}, error:", message, e);
            }

            try {
                if (message.getModuleCodes().contains(TIANYANCHA_API_INDUSTRIAL_QUERIES_APP)) {
                    CommercialInforQueryUtil.initCommercialInforQueryFunction(user, "EyeCommercialInforQuery",
                            "天眼查工商查询");
                }
            } catch (Exception e) {
                log.error("LicenseMQConsumeService TIANYANCHA_API_INDUSTRIAL_QUERIES_APP arg:{}, error:", message, e);
            }

            try {
                if (message.getModuleCodes().contains(LEADS_DEDUPLICATION_APP) && !smembers.contains(LEADS_DEDUPLICATION_APP)) {
                    crmMetaDataService.refreshLeadsDuplicateFields(tenantId);
                    initLeadsDuplicateFunction(user);
                }
            } catch (Exception e) {
                log.error("LicenseMQConsumeService LEADS_DEDUPLICATION_APP arg:{}, error:", message, e);
            }

            try {
                if (message.getModuleCodes().contains(ACCOUNTS_LEADS_LIMIT_APP)) {
                    crmMetaDataService.refreshAccountLimit2ObjectLimit(tenantId);
                }
            } catch (Exception e) {
                log.error("LicenseMQConsumeService ACCOUNTS_LEADS_LIMIT_APP arg:{}, error:", message, e);
            }

            try {
                if (message.getModuleCodes().contains(MARKETING_INTEGRATION_APP)) {
                    crmMetaDataService.refreshLeadsMarketingFields(tenantId);
                    crmMetaDataService.refreshMarketingEventFields(tenantId);
                }
            } catch (Exception e) {
                log.error("LicenseMQConsumeService MARKETING_INTEGRATION_APP arg:{}, error:", message, e);
            }

            try {
                if (message.getModuleCodes().contains(INTERCONNECT_APP_BASIC_APP)) {
                    brushEnterpriseRelation(tenantId);
                }
            } catch (Exception e) {
                log.error("LicenseMQConsumeService INTERCONNECT_APP_BASIC_APP arg:{}, error:", message, e);
            }

            try {
                if (DEALER_EDITION.equals(message.getLicenseVersion())) {
                    LicenseBusinessProcessUtil.initSupplierField(tenantId, Lists.newArrayList(PRODUCT_API_NAME),
                            INIT_QUOTE_FIELD_OBJECT_LIST);
                }
            } catch (Exception e) {
                log.error("LicenseMQConsumeService DEALER_EDITION arg:{}, error:", message, e);
            }

            try {
                if (AGENT_EDITION.equals(message.getLicenseVersion())) {
                    LicenseBusinessProcessUtil.initSupplierField(tenantId, INIT_REFERENCE_FIELD_OBJECT_LIST,
                            INIT_QUOTE_FIELD_OBJECT_LIST);
                }
            } catch (Exception e) {
                log.error("LicenseMQConsumeService AGENT_EDITION arg:{}, error:", message, e);
            }

            try {
                if (message.getModuleCodes().contains(BUSINESS_OPPORTUNITY_FORECAST_APP)) {
                    LicenseBusinessProcessUtil.openForecastRuleForDescribe(message.getTenantId());
                    crmMetaDataService.initForecast(tenantId);
                }
            } catch (Exception e) {
                log.error("LicenseMQConsumeService BUSINESS_OPPORTUNITY_FORECAST_APP arg:{}, error:", message, e);
            }
            try {
                if (EnterpriseWechatUtils.SCRM_LICENSE_CODE_SET.contains(message.getLicenseVersion()) || message.getModuleCodes().contains(MARKETING_OVERSEAS_PLUGIN_APP)) {
                    List<IObjectMappingRuleInfo> mappingRule = objectMappingService.findByApiName(
                            User.systemUser(tenantId), "rule_wechatfriend2leadsobj__c");
                    if (Safes.isEmpty(mappingRule)) {
                        String mappingName = "\"rule_wechatfriend2leadsobj__c\",\"rule_wechatfriend2contactobj__c\",\"rule_wechatfriend2accountobj__c\"";
                        crmMetaDataService.initMappingRule(tenantId, mappingName);
                    }
                    LicenseBusinessProcessUtil.addScrmSpecialField(tenantId);

                    /*crmMetaDataService.refreshQualityInspectionRuleButton(tenantId);
                    crmRecordRestService.assignRecord(tenantId);
                    crmRecordRestService.saveLayoutAssign(tenantId);

                    crmMetaDataService.refreshInheritButton(tenantId);*/
                }
            } catch (Exception e) {
                log.error("LicenseMQConsumeService SCRM_LICENSE arg:{}, error:", message, e);
            }

            try {
                //下单企微的互通接口或基础接口后同步企微员工数据至CRM中，异步线程处理
                if (EnterpriseWechatUtils.SCRM_BASE_LICENSE_CODE_SET.contains(message.getLicenseVersion())) {
                    CompletableFuture.runAsync(() -> enterpriseWechatEmployeeService.syncHistoryEmployee(tenantId));
                }
            } catch (Exception e) {
                log.error("LicenseMQConsumeService SCRM_INTERFACE_LICENSE arg:{}, error:", message, e);
            }

            try {
                if (message.getModuleCodes().contains(PRM_APP)) {
                    prmService.batchAddObject2Prm(user, Lists.newArrayList("AccountMainDataObj"));
                    prmService.initNavigationTemplate(tenantId);
                    mcrInitService.handleFieldOfMCRExpansionPlanObj(user, true);
                }
            } catch (Exception e) {
                log.error("LicenseMQConsumeService PRM_APP  initNavigationTemplate arg:{}, error", message, e);
            }

            try {
                if (message.getModuleCodes().contains(NEW_OPPORTUNITY_OBJ)) {
                    this.dealNewOpportunityFiledOfCOMPETITIVE_LINES_API_NAME(user);
                }
            } catch (Exception e) {
                log.error("LicenseMQConsumeService dealNewOpportunityFiledOfCOMPETITIVE_LINES_API_NAME arg:{}, error", message, e);
            }

            try {
                if (message.getModuleCodes().contains(PUSH_PULL_ORDER_APP)) {
                    crmMetaDataService.initConvertRule(tenantId);
                }
            } catch (Exception e) {
                log.error("LicenseMQConsumeService initConvertRule arg:{}, error", message, e);
            }

            try {
                if (message.getModuleCodes().contains(ACCOUNT_TREE_APP)) {

                    open_ACCOUNT_TREE_APP(user, false);
                }
            } catch (Exception e) {
                log.error("LicenseMQConsumeService initAccountTree arg:{}, error", message, e);
            }

            try {
                if (message.getModuleCodes().contains(CUSTOMER_EQUITY_RELATIONSHIP_APP)) {
                    open_CUSTOMER_EQUITY_RELATIONSHIP_APP(user, false);
                }
            } catch (Exception e) {
                log.error("LicenseMQConsumeService CUSTOMER_EQUITY_RELATIONSHIP_APP arg:{}, error", message, e);
            }
            try {
                if (message.getModuleCodes().contains(TREE_COMPONENT_APP)) {
                    log.info("Start to init TREE_COMPONENT_APP");
                    crmMetaDataService.initContactObjTreeComponentDesc(tenantId);
                    crmMetaDataService.initRecordTypeAndRole(tenantId, Lists.newArrayList("AccountDepartmentObj", "ContactRelationshipObj"));
                    atlasRelationTemplate.initRecordTypeAndLayoutOfRelationTemplateObj(tenantId, "AccountDepartmentObj");
                }
            } catch (Exception e) {
                log.error("LicenseMQConsumeService TREE_COMPONENT_APP arg:{}, error", message, e);
            }

            try {
                // 开通邓白氏
                if (message.getModuleCodes().contains(DBS_WITHOUT_LICENSE)) {
                    log.info("Start to init syncInsertFuncCommercialInforQuery");
                    crmMetaDataService.syncInsertFuncCommercialInforQuery(tenantId, "DnbCommercialInforQuery");
                }
            } catch (Exception e) {
                log.error("LicenseMQConsumeService DBS_WITHOUT_LICENSE arg:{}, error", message, e);
            }
            try {
                //商机
                if (message.getModuleCodes().contains(OPPORTUNITY_FISHBONE_DIAGRAM_APP)) {
                    log.info("Start to init OPPORTUNITY_FISHBONE_DIAGRAM_APP");
                    crmMetaDataService.initRecordTypeAndRole(tenantId, Lists.newArrayList("OpportunityDecisionChainObj", "OpportunityDecisionChainDetailObj"));
                    atlasRelationTemplate.initRecordTypeAndLayoutOfRelationTemplateObj(tenantId, "OpportunityDecisionChainObj");
                }
            } catch (Exception e) {
                log.error("LicenseMQConsumeService OPPORTUNITY_FISHBONE_DIAGRAM_APP arg:{}, error", message, e);
            }
            try {
                //客户客户树
                if (message.getModuleCodes().contains(SUB_ACCOUNT_TREE_APP)) {
                    log.info("Start to init SUB_ACCOUNT_TREE_APP");
                    crmMetaDataServiceProxy.init_sub_account_tree_app(tenantId, getHeaders(tenantId));
                    log.info("Start to init init_sub_account_tree_app");
                    crmMetaDataService.initRecordTypeAndRole(tenantId, Lists.newArrayList("SubAccountTreeRelationObj", "SubAccountTreeRelationLogObj"));
                    log.info("Start to init initRecordTypeAndRole");
                    crmMetaDataServiceProxy.initLayoutRule(tenantId, "SubAccountTreeRelationObjLayoutRuleType", getHeaders(tenantId));
//                    log.info("Start to init initLayoutRule");
//                    crmMetaDataService.addTreeRelationButton(tenantId);
                }
            } catch (Exception e) {
                log.error("LicenseMQConsumeService SUB_ACCOUNT_TREE_APP arg:{}, error", message, e);
            }
            try {
                //MCR
                if (message.getLicenseVersion().equals(KEY_ACCOUNTS_MANAGEMENT_INDUSTRY) || message.getModuleCodes().contains(KEY_ACCOUNTS_MANAGEMENT_INDUSTRY)) {
                    log.info("Start to init KEY_ACCOUNTS_MANAGEMENT_INDUSTRY tenantId：{}", tenantId);
                    mcrInitService.initLayout(tenantId);
                    mcrInitService.handleFieldOfMCRExpansionPlanObj(user, false);
                }
            } catch (Exception e) {
                log.error("LicenseMQConsumeService KEY_ACCOUNTS_MANAGEMENT_APP arg:{}, error", message, e);
            }
            try {
                //更新风险信息
                if (message.getLicenseVersion().equals(RISK_MANAGEMENT_STRENGTHEN_INDUSTRY) || message.getModuleCodes().contains(RISK_MANAGEMENT_STRENGTHEN_INDUSTRY)) {
                    log.warn("Start to init BusinessRiskInformationObj");
                    riskBrainAsyncService.handleBusinessRiskInformationObj(tenantId);
                    log.warn("handleBusinessRiskInformationObj is finish");
                    crmMetaDataService.initRecordTypeAndRole(tenantId, Lists.newArrayList("BusinessRiskInformationObj"));
                    log.warn("initRecordTypeAndRole is finish");
                }
            } catch (Exception e) {
                log.error("LicenseMQConsumeService BusinessRiskInformationObj arg:{}, error", message, e);
            }
//            try {
//                //rfm
//                if (message.getModuleCodes().contains(rfm)) {
//                    log.info("Start to init rfm");
//                    mcrInitService.initLayout(tenantId);
//                }
//            } catch (Exception e) {
//                log.error("LicenseMQConsumeService KEY_ACCOUNTS_MANAGEMENT_APP arg:{}, error", message, e);
//            }

            try {
                if (message.getLicenseVersion().equals(INDUSTRIAL_SEARCH_99_APP) || message.getModuleCodes().contains(INDUSTRIAL_SEARCH_100PLUS_APP)) {
                    crmMetaDataService.initRecordTypeAndRole(tenantId, Lists.newArrayList("BizQueryManageObj"));
                    log.warn("BizQueryManageObj initRecordTypeAndRole is finish");
                }
            } catch (Exception e) {
                log.error("BizQueryManageObj initRecordTypeAndRole arg:{}, error", message, e);
            }

            try {
                if (message.getModuleCodes().contains(OPERATIONS_WORKBENCH_APP)) {
                    log.info("Start to init operations_workbench_app: {}", tenantId);
                    addRoleRecordForEntityIds(user, Lists.newArrayList("OperationsStrategyObj", "OperationsActivityTemplateObj", "OperationsTaskTemplateObj", "OperationsActivityObj", "OperationsTaskObj"));
                }
            } catch (Exception e) {
                log.error("Init operations_workbench_app arg:{}, error", message, e);
            }

            try {
                if (message.getModuleCodes().contains(USER_LICENSE_FXIAOKE_AI_PLATFORM_APP) || message.getModuleCodes().contains(FXIAOKE_AI_PLATFORM_APP)) {
                    log.info("Start to init user_license_fxiaoke_ai_platform_limit: {}", tenantId);
                    addRoleAndFuncRecord(user, Lists.newArrayList(Utils.LEADS_API_NAME, Utils.ACCOUNT_API_NAME, Utils.NEW_OPPORTUNITY_API_NAME));
                }
            } catch (Exception e) {
                log.error("Init user_license_fxiaoke_ai_platform_limit arg:{}, error", message, e);
            }

            try {
                if (message.getModuleCodes().contains(AI_INTERACTIVE_ASSISTANT_APP) && message.getMqModuleParas().isEmpty()) {
                    log.info("Init AI interactive licenses start: {}", tenantId);
                    handleAIInteractiveAssistantApp(user);
                }
            } catch (Exception e) {
                log.error("Init AI interactive licenses arg:{}, error", message, e);
            }
            try {
                if (message.getModuleCodes().contains(AI_INTERACTIVE_ASSISTANT_APP) && !message.getMqModuleParas().isEmpty()) {
                    if (message.getMqModuleParas().stream().map(LicenseModel.MqModulePara::getParaKey).anyMatch(AI_INTERACTIVE_ASSISTANT_LIMIT::equals)) {
                        log.info("Init AI interactive limit start: {}", tenantId);
                        handleAIInteractiveAssistantLimit(user);
                    }
                }
            } catch (Exception e) {
                log.error("Init AI interactive limit arg:{}, error", message, e);
            }
            try {
                if (message.getModuleCodes().contains(AI_INTERACTIVE_ASSISTANT_APP) && org.apache.commons.collections.CollectionUtils.isNotEmpty(message.getMqModuleParas())) {
                    if (message.getMqModuleParas().stream().map(LicenseModel.MqModulePara::getParaKey).anyMatch(ER_AI_INTERACTIVE_ASSISTANT_LIMIT::equals)) {
                        log.info("Init ER AI interactive limit start: {}", tenantId);
                        prmService.addPrmAIRole(user);
                    }
                }
            } catch (Exception e) {
                log.error("Init prm role, error, tenantId:{}", user.getTenantId(), e);
            }

            try {
                if (message.getModuleCodes().contains(OUTLOOK_INTEGRATION_APP)) {
                    log.info("Start to handle outlook_integration_app: {}", tenantId);
                    handleOutlookIntegrationApp(tenantId, user);
                }
            } catch (Exception e) {
                log.error("Handle outlook_integration_app arg:{}, error", message, e);
            }

            try {
                if (message.getModuleCodes().contains("knowledgemanagement_app")) {
                    log.info("Start to handle knowledgemanagement_app: {}", tenantId);
                    crmMetaDataService.upgradeRequirementDescribe(tenantId);
                }
            } catch (Exception e) {
                log.error("Handle knowledgemanagement_app arg:{}, error", message, e);
            }
        } catch (Exception ex) {
            log.error("LicenseMQConsumeService arg:{}, error:", message, ex);
        }
    }

    private void addRoleAndFuncRecord(User user, List<String> objectApiNames) {
        addAISalesAssistantRole(user);


        Map<String, String> funcNames = new HashMap<>();
        funcNames.put("sfa_basic_leads_bant_insights", "BANT洞察");
        funcNames.put("sfa_basic_leads_feed_summary", "销售记录摘要");
        funcNames.put("sfa_basic_leads_marketing_summary", "营销互动摘要");
        funcNames.put("crm_assistant_component", "AI助手");
        funcNames.put("sfa_risk_insights", "风险洞察");
        funcNames.put("sfa_financial_report_insights", "财报洞察");
        funcNames.put("sfa_basic_knowledge_recommend", "AI知识推荐");
        funcNames.put("sfa_basic_deal_account_case", "成交客户案例");
        funcNames.put("sfa_win_first_values_clear", "C139模型");
        List<String> leadsFuncCodes = Lists.newArrayList("sfa_basic_leads_bant_insights", "sfa_basic_leads_feed_summary", "sfa_basic_leads_marketing_summary", "crm_assistant_component");
        List<String> accountFuncCodes = Lists.newArrayList("sfa_basic_leads_bant_insights", "sfa_risk_insights", "sfa_financial_report_insights", "sfa_basic_knowledge_recommend", "sfa_basic_deal_account_case", "crm_assistant_component");
        List<String> newOpportunityFuncCodes = Lists.newArrayList("sfa_win_first_values_clear", "crm_assistant_component");
        Map<String, List<String>> funcCodes = ImmutableMap.of(
                Utils.LEADS_API_NAME, leadsFuncCodes,
                Utils.ACCOUNT_API_NAME, accountFuncCodes,
                Utils.NEW_OPPORTUNITY_API_NAME, newOpportunityFuncCodes
        );
        Map<String, List<String>> funcAccessCodes = new HashMap<>();
        funcAccessCodes.put(Utils.LEADS_API_NAME, leadsFuncCodes.stream()
                .map(x -> String.format("LeadsObj||%s", x))
                .collect(Collectors.toList()));
        funcAccessCodes.get(Utils.LEADS_API_NAME).add(Utils.LEADS_API_NAME);

        funcAccessCodes.put(Utils.ACCOUNT_API_NAME, accountFuncCodes.stream()
                .map(x -> String.format("AccountObj||%s", x))
                .collect(Collectors.toList()));
        funcAccessCodes.get(Utils.ACCOUNT_API_NAME).add(Utils.ACCOUNT_API_NAME);

        funcAccessCodes.put(Utils.NEW_OPPORTUNITY_API_NAME, newOpportunityFuncCodes.stream()
                .map(x -> String.format("NewOpportunityObj||%s", x))
                .collect(Collectors.toList()));
        funcAccessCodes.get(Utils.NEW_OPPORTUNITY_API_NAME).add(Utils.NEW_OPPORTUNITY_API_NAME);

        batchCreateFunc(user, objectApiNames, funcCodes, funcNames);
        for (String objectApiName : objectApiNames) {
            functionPrivilegeService.updatePreDefinedFuncAccess(user, "CRM", "AISalesAssistantRole", funcAccessCodes.get(objectApiName), Lists.newArrayList());
        }
    }

    private void addAISalesAssistantRole(User user) {
        addAIRoleGroup(user);
        try {
            String querySql = String.format("SELECT * FROM fc_role WHERE tenant_id='%s' AND role_code ='AISalesAssistantRole' " + "AND app_id='CRM' and is_deleted = 'false' ", user.getTenantId());
            List<Map> queryResult = objectDataService.findBySql(user.getTenantId(), querySql);

            if (CollectionUtils.notEmpty(queryResult)) {
                log.warn("addRoleAndFuncRecord role exist,{}", user);
            } else {
                CreateRoleArg createRoleArg = new CreateRoleArg();
                createRoleArg.setDescription("AI 销售助手");
                createRoleArg.setRoleCode("AISalesAssistantRole");
                createRoleArg.setRoleName("AI 销售助手");
                createRoleArg.setRoleType(1);
                createRoleArg.setLicenseCode("user_license_fxiaoke_ai_platform_limit");
                createRoleArg.setAuthContext(AuthContext.builder().appId(CRM_APP_ID).tenantId(user.getTenantId()).userId(User.SUPPER_ADMIN_USER_ID).build());
                roleClient.createRole(createRoleArg);
            }
        } catch (Exception e) {
            log.error("addRoleAndFuncRecord error,{}", user, e);
        }
    }

    private void addAIRoleGroup(User user) {
        try {
            // 添加角色组
            AddRoleGroupArg addRoleGroupArg = new AddRoleGroupArg();
            // 配置文件fs-permission-role-group覆盖分组
            addRoleGroupArg.setRoleGroupType(0);
            addRoleGroupArg.setRoleGroupName("AI 角色");
            addRoleGroupArg.setRoleGroupDescription("AI 角色");
            addRoleGroupArg.setId("AISalesAssistantRoleGroup");
            AuthContext context = AuthContext.builder().appId(CRM_APP_ID)
                    .tenantId(user.getTenantId()).userId(User.SUPPER_ADMIN_USER_ID).build();
            addRoleGroupArg.setAuthContext(context);
            roleClient.addRoleGroup(addRoleGroupArg);
        } catch (Exception e) {
            log.error("addRoleAndFuncRecord error,{}", user, e);
        }
    }


    private void batchCreateFunc(User user, List<String> describeApiNames, Map<String, List<String>> funcCodes, Map<String, String> funcName) {
        List<CreateFunctionPrivilege.FunctionPojo> functions = Lists.newArrayList();

        for (String describeApiName : describeApiNames) {
            for (String funcCode : funcCodes.get(describeApiName)) {
                CreateFunctionPrivilege.FunctionPojo pojo = new CreateFunctionPrivilege.FunctionPojo();
                pojo.setAppId(PrivilegeConstants.APP_ID);
                pojo.setTenantId(user.getTenantId());
                pojo.setFuncType(PrivilegeConstants.SYSTEM_DEFINED_FUNCTION_CODE_TYPE);
                pojo.setParentCode(PrivilegeConstants.PAAS_PARENT_CODE);
                pojo.setFuncName(funcName.get(funcCode));
                pojo.setFuncCode(describeApiName + "||" + funcCode);
                functions.add(pojo);
            }
        }
        CreateFunctionPrivilege.Arg createFunctionPrivilegeArg = CreateFunctionPrivilege.Arg.builder()
                .authContext(CommercialInforQueryUtil.buildOldAuthContext(user))
                .functionPojoList(functions)
                .build();
        log.info("batchCreateFunc createFunctionPrivilegeArg:{}", createFunctionPrivilegeArg);
        CreateFunctionPrivilege.Result createFuncResult = functionPrivilegeProxy.createFunctionPrivilege(createFunctionPrivilegeArg, buildHeaders(user.getTenantId()));

        if (!createFuncResult.isSuccess()) {
            log.error("createFunctionPrivilege error,arg:{},result:{}", createFunctionPrivilegeArg, createFuncResult);
            throw new PermissionError(I18N.text(I18NKey.INIT_PERMISSION_FAIL_REASON, createFuncResult.getErrMessage()));
        }
    }
    private void brushEnterpriseRelation(String tenantId) {
        log.info("begin brushEnterpriseRelation tenant:{}", tenantId);
        try {
            User user = new User(String.valueOf(tenantId), "-10000");
            boolean openPartner = prmService.isOpenPartner(user);
            if (!openPartner) {
                return;
            }
            log.info("begin brushEnterpriseRelation open partner, tenant:{}", tenantId);
            prmService.brushEnterpriseRelation(user);
        } catch (Exception e) {
            log.error("brushEnterpriseRelation error, tenant:{}", tenantId, e);
        }
    }

    public void execute(LicenseModel.MultiBusinessUnitMessage message) {
        if (message == null || !"1".equals(message.getValue()) || !"openOrganization".equals(message.getKey())) {
            return;
        }
        sfaTaskRateLimiterService.getLicenseMQConsumeInitRateLimiter().acquire();
        try {
            String tenantId = message.getEnterpriseId();
            String ea = eieaConverter.enterpriseIdToAccount(Integer.parseInt(tenantId));
            if (ea.endsWith("_sandbox")) {
                //沙盒企业先判断是否有客户主数据的对象，如果有了就不处理以下逻辑
                try {
                    IObjectDescribe objectDescribe = describeLogicService.findObject(tenantId, "AccountMainDataObj");
                    if (ObjectUtils.isNotEmpty(objectDescribe)) {
                        log.warn("initAccountMainDataObj AccountMainDataObj is not null. ea:{}", ea);
                        return;
                    }
                } catch (Exception e) {
                    log.error("initAccountMainDataObj describeLogicService.findObject error. ea:{}", ea);
                }
            }

            //更新标准价目表归属组织为全集团
            PriceBookUtils.updateStandardPriceBookOrganization(tenantId);
            log.info("===== updateStandardPriceBookOrganization end====" + tenantId);
            crmMetaDataService.initAccountMainDataObj(tenantId);
            initAccountMainDataObjFunction(User.systemUser(tenantId));
            User user = new User(tenantId, CommonConstant.SUPER_USER);
            log.info("===== openMainAccountForDescribe begin====" + tenantId);
            LicenseBusinessProcessUtil.openMainAccountForDescribe(user);
            log.info("===== openMainAccountForLayout begin====" + tenantId);
            LicenseBusinessProcessUtil.openMainAccountForLayout(user);
            log.info("===== openMainAccountForLayout end====" + tenantId);
            partnerFieldHandler(user);
            masterDataBizService.addDownStreamMasterDataField(user);
            masterDataBizService.closeMasterDataApp(user);
            log.info("===== openMainAccount refreshMultiCurrency begin====" + tenantId);
            RefreshInternational.Arg arg = new RefreshInternational.Arg();
            arg.setObjectApiNames(Lists.newArrayList("AccountMainDataObj"));
            multiCurrencyLogicService.refreshMultiCurrency(arg, user);
            log.info("===== openMainAccount refreshMultiCurrency end====" + tenantId);
            String mappingName = "\"rule_accountobj2accountmaindataobj__c\"";
            crmMetaDataService.initMappingRule(tenantId, mappingName);
            log.info("===== openMainAccount initMappingRule end====" + tenantId);
            configService.createTenantConfig(user, "sfa_open_accountMainData", "1", ConfigValueType.STRING);
            open_ACCOUNT_TREE_APP(user, true);
            open_CUSTOMER_EQUITY_RELATIONSHIP_APP(user, true);
        } catch (Exception ex) {
            log.error("LicenseMQConsumeService.MultiBusinessUnitMessage message:{},ex:", message, ex);
        }
    }

    private void partnerFieldHandler(User user) {
        if (!prmService.isOpenPartner(user)) {
            return;
        }
        log.info("===== batchAddObject2Prm open=====");
        prmService.batchAddObject2Prm(user, Lists.newArrayList("AccountMainDataObj"));
        log.info("===== batchAddObject2Prm end=====");
    }

    private void initLeadsDuplicateFunction(User user) {
        try {
            List<String> funcCodes = Lists.newArrayList(
                    ObjectAction.COLLECT_TO.getActionCode());
            functionPrivilegeService.batchCreateFunc(user, "LeadsObj", funcCodes);
            List<String> funcAccessCodes = Lists.newArrayList();
            funcCodes.forEach(x -> {
                if (ObjectAction.VIEW_LIST.getActionCode().equals(x)) {
                    x = "LeadsObj";
                } else {
                    x = String.format("LeadsObj||%s", x);
                }
                funcAccessCodes.add(x);
            });
            Thread.sleep(200);
            functionPrivilegeService.updatePreDefinedFuncAccess(user, "CRM", "00000000000000000000000000000006", funcAccessCodes, Lists.newArrayList());
        } catch (Exception ex) {
            log.error(ex.getMessage(), ex);
        }
    }

    private void initAccountMainDataObjFunction(User user) {
        try {
            List<String> funcCodes = Lists.newArrayList(
                    ObjectAction.VIEW_LIST.getActionCode(),
                    ObjectAction.VIEW_DETAIL.getActionCode(),
                    ObjectAction.CREATE.getActionCode(),
                    ObjectAction.UPDATE.getActionCode(),
                    ObjectAction.BATCH_IMPORT.getActionCode(),
                    ObjectAction.BATCH_EXPORT.getActionCode(),
                    ObjectAction.CHANGE_OWNER.getActionCode(),
                    ObjectAction.ALLOCATE_MAIN_DATA.getActionCode(),
                    ObjectAction.CHOOSE_MAIN_DATA.getActionCode(),
                    ObjectAction.INVALID.getActionCode(),
                    ObjectAction.RECOVER.getActionCode(),
                    ObjectAction.DELETE.getActionCode(),
                    ObjectAction.EDIT_TEAM_MEMBER.getActionCode(),
                    ObjectAction.PRINT.getActionCode(),
                    ObjectAction.VIEW_ENTIRE_BPM.getActionCode(),
                    ObjectAction.START_BPM.getActionCode(),
                    ObjectAction.STOP_BPM.getActionCode(),
                    ObjectAction.CHANGE_BPM_APPROVER.getActionCode(),
                    ObjectAction.INTELLIGENTFORM.getActionCode(),
                    ObjectAction.LOCK.getActionCode(),
                    ObjectAction.CLONE.getActionCode(),
                    ObjectAction.UNLOCK.getActionCode(),
                    ObjectAction.MODIFYLOG_RECOVER.getActionCode());
            functionPrivilegeService.batchCreateFunc(user, "AccountMainDataObj", funcCodes);
            List<String> funcAccessCodes = Lists.newArrayList();
            funcCodes.forEach(x -> {
                if (ObjectAction.VIEW_LIST.getActionCode().equals(x)) {
                    x = "AccountMainDataObj";
                } else {
                    x = String.format("AccountMainDataObj||%s", x);
                }
                funcAccessCodes.add(x);
            });
            Thread.sleep(200);
            functionPrivilegeService.updatePreDefinedFuncAccess(user, "CRM", "00000000000000000000000000000006", funcAccessCodes, Lists.newArrayList());
            Thread.sleep(200);
            functionPrivilegeService.updatePreDefinedFuncAccess(user, "CRM", "AccountMainDataManagerRole", funcAccessCodes, Lists.newArrayList());
        } catch (Exception ex) {
            log.error("initAccountMainDataObjFunction getMessage:{},e:", ex.getMessage(), ex);
        }
    }

    private void initAccountMainDataObjFieldPrivilege(User user) {
        try {
            Map<String, Integer> fieldPermission = Maps.newHashMap();
            fieldPermission.put("name", FieldPermissionEnum.read_write.getPermission());
            fieldPermission.put("main_account_no", FieldPermissionEnum.read_write.getPermission());
            fieldPermission.put("erp_code", FieldPermissionEnum.read_write.getPermission());
            fieldPermission.put("erp_id", FieldPermissionEnum.read_write.getPermission());
            fieldPermission.put("account_type", FieldPermissionEnum.read_write.getPermission());
            fieldPermission.put("industry_level1", FieldPermissionEnum.read_write.getPermission());
            fieldPermission.put("industry_level2", FieldPermissionEnum.read_write.getPermission());
            fieldPermission.put("enterprise_type", FieldPermissionEnum.read_write.getPermission());
            fieldPermission.put("legal_representative", FieldPermissionEnum.read_write.getPermission());
            fieldPermission.put("registered_capital", FieldPermissionEnum.read_write.getPermission());
            fieldPermission.put("registration_status", FieldPermissionEnum.read_write.getPermission());
            fieldPermission.put("business_scope", FieldPermissionEnum.read_write.getPermission());
            fieldPermission.put("tel", FieldPermissionEnum.read_write.getPermission());
            fieldPermission.put("email", FieldPermissionEnum.read_write.getPermission());
            fieldPermission.put("fax", FieldPermissionEnum.read_write.getPermission());
            fieldPermission.put("url", FieldPermissionEnum.read_write.getPermission());
            fieldPermission.put("remark", FieldPermissionEnum.read_write.getPermission());
            fieldPermission.put("pin_yin", FieldPermissionEnum.read_write.getPermission());
            fieldPermission.put("data_own_department", FieldPermissionEnum.read_write.getPermission());
            fieldPermission.put("biz_status", FieldPermissionEnum.read_write.getPermission());
            fieldPermission.put("uniform_social_credit_code", FieldPermissionEnum.read_write.getPermission());
            fieldPermission.put("biz_reg_name", FieldPermissionEnum.read_write.getPermission());
            fieldPermission.put("data_own_organization", FieldPermissionEnum.read_write.getPermission());
            fieldClient.updateFieldPermissionByRole(buildAuthContext(user.getTenantId(), user.getOutUserId()), "AccountMainDataManagerRole", "CRM", fieldPermission);
        } catch (Exception ex) {
            log.error(ex.getMessage(), ex);
        }
    }

    private AuthContext buildAuthContext(String tenantId, String userId) {
        return AuthContext.builder().tenantId(tenantId).userId(userId).appId("CRM").build();
    }


    private void addRoleRecordForEntityIds(User user, List<String> entityIds) {
        BatchAddRoleRecordTypeModel.Arg arg = new BatchAddRoleRecordTypeModel.Arg();
        arg.setAuthContext(user);
        arg.setRecordTypeId("default__c");
        arg.setEntityIds(entityIds);
        arg.setRoleCode("00000000000000000000000000000006");
        log.debug("add role recordType arg:{}", arg);
        AddRoleRecordTypeModel.Result result = recordTypeAuthProxy.batchAddRoleRecordType(arg, PAAS_PRIVILEGE_HEADDER.buildHeader(user.getTenantId()));
        log.info("add role recordType, arg:{}, result: {}", arg, JSONObject.toJSONString(result));
    }


    private void dealNewOpportunityFiledOfObj(User user, IObjectDescribe projectDescribe, String title) {
        if (projectDescribe.containsField("new_opportunity_id")) {
            log.info("skip dealNewOpportunityFiledOfObj cause target field exist");
            return;
        }
        InsertOrUpdateResult newOpportunityIDFieldResult = descUtil.getUpdateOrInsertFieldDescribe(projectDescribe, "new_opportunity_id", "商机2.0",
                Utils.NEW_OPPORTUNITY_API_NAME, title, "new_opportunity_project_list");
        String fieldDescribeString = "{\"default_is_expression\":false,\"auto_adapt_places\":false,\"description\":\"商机2.0\",\"is_unique\":false,\"where_type\":\"field\",\"relation_outer_data_privilege\":\"not_related\",\"related_where_type\":\"\",\"is_required\":false,\"wheres\":[{\"connector\":\"OR\",\"filters\":[{\"value_type\":2,\"operator\":\"EQ\",\"field_name\":\"account_id\",\"field_values\":[\"$account_id$\"]}]}],\"define_type\":\"package\",\"input_mode\":\"\",\"is_single\":false,\"is_extend\":false,\"is_index\":true,\"is_active\":true,\"is_encrypted\":false,\"label\":\"商机2.0\",\"target_api_name\":\"NewOpportunityObj\",\"target_related_list_name\":\"new_opportunity_project_list\",\"target_related_list_label\":\"" + title + "\",\"action_on_target_delete\":\"cascade_delete\",\"is_need_convert\":false,\"related_wheres\":[],\"api_name\":\"new_opportunity_id\",\"is_index_field\":true,\"help_text\":\"\",\"status\":\"released\"}";
        ObjectReferenceFieldDescribe newOpportunityField = JSON.parseObject(fieldDescribeString, ObjectReferenceFieldDescribe.class);
        newOpportunityField.set("type", "object_reference");
        newOpportunityIDFieldResult.setFieldDescribe(newOpportunityField);
        log.debug("trace dealNewOpportunityFiled, newOpportunityIDFieldResult:{}", JSONObject.toJSONString(newOpportunityIDFieldResult));
        if (newOpportunityIDFieldResult.getIsInsert()) {
            descUtil.addFieldDescribe(projectDescribe, Lists.newArrayList(newOpportunityIDFieldResult.getFieldDescribe()));
        } else {
            describeLogicService.updateFieldDescribe(projectDescribe, Lists.newArrayList(newOpportunityIDFieldResult.getFieldDescribe()));
        }
        // 把新增的字段放入到指定的layout中的FormComponent中的基本信息FieldSection的里面
        FieldLayoutPojo fieldLayoutPojo = descUtil.getFieldLayoutPojo("object_reference", false, false);
        log.debug("trace dealNewOpportunityFiled, fieldLayoutPojo:{}", JSONObject.toJSONString(fieldLayoutPojo));
        descUtil.insertFieldToLayout(user, projectDescribe, newOpportunityIDFieldResult.getFieldDescribe(), fieldLayoutPojo);
    }

    /**
     * 竞争对手对象中预置的商机2.0字段的问题
     */
    public void dealNewOpportunityFiledOfCOMPETITIVE_LINES_API_NAME(User user) {
        Map<String, IObjectDescribe> describeMap = describeLogicService.findObjects(user.getTenantId()
                , Lists.newArrayList(COMPETITIVE_LINES_API_NAME));
        if (describeMap != null) {
            //region 竞争对手明细添加商机2.0
            IObjectDescribe CompetitorDescribe = describeMap.get(COMPETITIVE_LINES_API_NAME);
            if (ObjectUtils.isNotEmpty(CompetitorDescribe)) {
                dealNewOpportunityFiledOfObj(user, CompetitorDescribe, "竞争对手明细");
            } else {
                log.warn("dealNewOpportunityFiledOfCOMPETITIVE_LINES_API_NAME>获取描述失败={}, {}", user.getTenantId(), COMPETITIVE_LINES_API_NAME);
                throw new ValidateException(I18N.text(I18NKeyUtil.SFA_CONFIG_GETDESCRIPTIONFAILED));
            }
            //endregion
        } else {
            log.warn("dealNewOpportunityFiledOfCOMPETITIVE_LINES_API_NAME>获取描述失败={},{}", user.getTenantId()
                    , Lists.newArrayList(COMPETITIVE_LINES_API_NAME));
            throw new ValidateException(I18N.text(I18NKeyUtil.SFA_CONFIG_GETDESCRIPTIONFAILED));
        }
    }

    /**
     * @param user
     * @param isOpenMainDataTags true:打开多组织，false：开通license
     */
    public void open_ACCOUNT_TREE_APP(User user, boolean isOpenMainDataTags) {
        if (isOpenMainDataTags) {
            boolean flag = sfaLicenseService.checkModuleLicenseExist(user.getTenantId(), ACCOUNT_TREE_APP);
            if (!flag) {
                log.warn("open_ACCOUNT_TREE_APP 打开多组织，但是没有 ACCOUNT_TREE_APP license {}", user.getTenantId());
                return;
            }
        } else {
            try {
                IObjectDescribe describe = describeLogicService.findObject(user.getTenantId(), "AccountMainDataObj");
                if (ObjectUtils.isEmpty(describe)) {
                    log.warn("open_ACCOUNT_TREE_APP 开通license 但是没有主数据对象");
                    return;
                }
            } catch (Exception e) {
                log.warn("open_ACCOUNT_TREE_APP e:", e);
                return;
            }
        }
        log.info("Start to init ACCOUNT_TREE_APP");
        crmMetaDataService.initAccountTreeRelationLayoutRule(user.getTenantId());
        addRoleRecordForEntityIds(user, Lists.newArrayList("AccountTreeRelationObj"));
    }

    /**
     * @param user
     * @param isOpenMainDataTags true:打开多组织，false：开通license
     */
    public void open_CUSTOMER_EQUITY_RELATIONSHIP_APP(User user, boolean isOpenMainDataTags) {

        //判断是否是招商局
        if (!mcrInitService.isZSJENV()) {
            return;
        }
        if (isOpenMainDataTags) {
            boolean flag = sfaLicenseService.checkModuleLicenseExist(user.getTenantId(), CUSTOMER_EQUITY_RELATIONSHIP_APP);
            if (!flag) {
                log.warn("open_CUSTOMER_EQUITY_RELATIONSHIP_APP 打开多组织，但是没有  license {}", user.getTenantId());
                return;
            }
        } else {
            try {
                IObjectDescribe describe = describeLogicService.findObject(user.getTenantId(), "AccountMainDataObj");
                if (ObjectUtils.isEmpty(describe)) {
                    log.warn("open_CUSTOMER_EQUITY_RELATIONSHIP_APP 开通license 但是没有主数据对象");
                    return;
                }
            } catch (Exception e) {
                log.error("open_CUSTOMER_EQUITY_RELATIONSHIP_APP e:", e);
                return;
            }
        }
        log.info("Start to init CUSTOMER_EQUITY_RELATIONSHIP_APP");
        equityRelationshipInitService.initEquityRelationship(user.getTenantId());
    }

    private void handleOutlookIntegrationApp(String tenantId, User user) {
        addRoleRecordForEntityIds(user, Lists.newArrayList("EmailAccountObj"));
        LicenseBusinessProcessUtil.addOutlookSpecialField(tenantId);
    }

    private void handleAIInteractiveAssistantApp(User user) {
        // 预置业务类型
        addRoleRecordForEntityIds(user, Lists.newArrayList("SalesTopicLibraryObj", "InteractionStrategyObj", "InteractionStrategyDetailObj", "TermBankObj", "TermBankDetailObj", "RequirementObj", "AgentPromptVersionObj"));
        // 刷布局（可能+字段）
        JSONObject params = new JSONObject();
        params.put("tenantIds", Collections.singletonList(user.getTenantId()));
        params.put("mode", "update");
        String body = params.toJSONString();
        try {
            crmMetaDataServiceProxy.initRequirementAll(body);
        } catch (Exception e) {
            log.error("handleAIInteractiveAssistantApp init requirement error:", e);
        }
        crmMetaDataServiceProxy.upgradeActiveRecordLayoutWithAiLicense(body);
    }

    private void handleAIInteractiveAssistantLimit(User user) {
        // 客户互动Agent角色
        String roleCode = addAIInteractiveAssistantRole(user);
        // 客户互动Agent功能权限
        Map<String, String> funcNameMap = new HashMap<>();
        funcNameMap.put("crm_assistant_component", "AI助手");
        funcNameMap.put("sfa_activity_audio_record", "互动语料");
        funcNameMap.put("activity_meeting_summary", "互动摘要");
        funcNameMap.put("sfa_activity_interactive_issues", "互动话题");
        funcNameMap.put("sfa_activity_suggest_issues", "建议话题");
        funcNameMap.put("sfa_activity_new_business_info", "业务情报");
        funcNameMap.put("sfa_activity_todo", "待办");
        funcNameMap.put("sfa_activity_attendees_insight", "参会人洞察");
        funcNameMap.put("sfa_activity_record", "录音");
//        funcNameMap.put("sfa_activity_meet", "建会议");
        List<String> accountFuncCodes = Lists.newArrayList("sfa_activity_suggest_issues", "sfa_activity_interactive_issues", "sfa_activity_record");
        List<String> activeRecordsFuncCodes = Lists.newArrayList("crm_assistant_component", "sfa_activity_audio_record", "activity_meeting_summary", "sfa_activity_interactive_issues", "sfa_activity_suggest_issues", "sfa_activity_new_business_info", "sfa_activity_todo", "sfa_activity_attendees_insight");
        List<String> contactFuncCodes = Lists.newArrayList("sfa_activity_record");
        List<String> leadsFuncCodes = Lists.newArrayList("sfa_activity_record", "sfa_activity_interactive_issues");
        List<String> opportunityFuncCodes = Lists.newArrayList("sfa_activity_suggest_issues", "sfa_activity_interactive_issues", "sfa_activity_record");
        Map<String, List<String>> funcCodeMap = ImmutableMap.of(
                Utils.ACCOUNT_API_NAME, accountFuncCodes,
                Utils.ACTIVE_RECORD_API_NAME, activeRecordsFuncCodes,
                Utils.CONTACT_API_NAME, contactFuncCodes,
                Utils.LEADS_API_NAME, leadsFuncCodes,
                Utils.NEW_OPPORTUNITY_API_NAME, opportunityFuncCodes
        );
        List<String> funcCodes = new ArrayList<>();
        for (Map.Entry<String, List<String>> entry : funcCodeMap.entrySet()) {
            String object = entry.getKey();
            entry.getValue().stream().map(code -> object + "||" + code).forEach(funcCodes::add);
        }
        FuncCodePrivilege.Arg funcCodePrivilegeArg = new FuncCodePrivilege.Arg(CommercialInforQueryUtil.buildOldAuthContext(user), funcCodes);
        FuncCodePrivilege.Result funcCodePrivilegeResult = functionPrivilegeProxy.getHaveFuncCodesPrivilegeRoles(funcCodePrivilegeArg, buildHeaders(user.getTenantId()));

        List<String> preparingAddFuncCodes = new ArrayList<>();
        List<String> preparingDelFuncCodes = new ArrayList<>();
        for (String funcCode : funcCodes) {
            List<String> roles = funcCodePrivilegeResult.getResult().get(funcCode);
            if (roles.contains("AISalesAssistantRole")) {
                preparingDelFuncCodes.add(funcCode);
            }
            if (!roles.contains(roleCode)) {
                preparingAddFuncCodes.add(funcCode);
            }
        }
        if (!preparingDelFuncCodes.isEmpty() || !preparingAddFuncCodes.isEmpty()) {
            if (!preparingDelFuncCodes.isEmpty()) {
                functionPrivilegeService.updatePreDefinedFuncAccess(user, "CRM", "AISalesAssistantRole", Collections.emptyList(), preparingDelFuncCodes);
            }
            if (!preparingAddFuncCodes.isEmpty()) {
                ArrayList<String> objectApiNames = Lists.newArrayList(Utils.ACCOUNT_API_NAME, Utils.ACTIVE_RECORD_API_NAME, Utils.CONTACT_API_NAME, Utils.LEADS_API_NAME, Utils.NEW_OPPORTUNITY_API_NAME);
                batchCreateFunc(user, objectApiNames, funcCodeMap, funcNameMap);
                functionPrivilegeService.updatePreDefinedFuncAccess(user, "CRM", roleCode, preparingAddFuncCodes, Collections.emptyList());
            }
        } else {
            log.info("Skip add AI interactive assistant func: {}", user.getTenantId());
        }
    }

    private String addAIInteractiveAssistantRole(User user) {
        addAIRoleGroup(user);
        String roleCode = "AIInteractiveAssistantRole";
        try {
            String querySql = String.format("SELECT * FROM fc_role WHERE tenant_id='%s' AND role_code ='%s' " + "AND app_id='CRM' and is_deleted = 'false' ", user.getTenantId(), roleCode);
            @SuppressWarnings("rawtypes")
            List<Map> queryResult = objectDataService.findBySql(user.getTenantId(), querySql);

            if (CollectionUtils.notEmpty(queryResult)) {
                log.warn("AIInteractiveAssistantRole role exist,{}", user);
            } else {
                CreateRoleArg createRoleArg = new CreateRoleArg();
                createRoleArg.setDescription("纷享AI平台预设角色，用于赋能员工使用客户互动Agent能力");
                createRoleArg.setRoleCode(roleCode);
                createRoleArg.setGroupCode("AISalesAssistantRoleGroup");
                createRoleArg.setRoleName("客户互动Agent");
                createRoleArg.setRoleType(1);
                createRoleArg.setLicenseCode(AI_INTERACTIVE_ASSISTANT_LIMIT);
                createRoleArg.setAuthContext(AuthContext.builder().appId(CRM_APP_ID).tenantId(user.getTenantId()).userId(User.SUPPER_ADMIN_USER_ID).build());
                roleClient.createRole(createRoleArg);
            }

        } catch (Exception e) {
            log.error("addAIInteractiveAssistantRole error,{}", user, e);
        }
        return roleCode;
    }
}
