package com.facishare.crm.task.sfa.model.rebate;

import com.alibaba.fastjson.annotation.JSONField;
import com.facishare.crm.task.sfa.model.RuleWhere;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Builder;
import lombok.Data;

import java.util.List;

@Data
@Builder
public class ConditionData {
    private List<RuleWhere> data;

    private String object_api_name;
    @JSONField(name = "gift_condition_unit_id")
    @JsonProperty("gift_condition_unit_id")
    private String giftConditionUnitId;
    @JSONField(name = "gift_condition_unit__s")
    @JsonProperty("gift_condition_unit__s")
    private String giftConditionUnitName;
}
