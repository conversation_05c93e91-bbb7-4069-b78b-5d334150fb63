package com.facishare.crm.task.sfa.enums;

import lombok.Getter;

/**
 * <AUTHOR>
 * @time 2024-01-16 18:05
 * @Description
 */
public enum SmsTypeEnums {
    CUSTOM_SMS("custom_sms"),
    TEMPLATE_SMS("template_sms");

    @Getter
    private final String type;

    SmsTypeEnums(String type) {
        this.type = type;
    }
    public static SmsTypeEnums getSmsType(String type) {
        for (SmsTypeEnums e : values()) {
            if (e.type.equals(type)) {
                return e;
            }
        }
        return null;
    }
}