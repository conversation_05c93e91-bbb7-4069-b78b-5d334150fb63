package com.facishare.crm.task.sfa.model.qywx;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import org.apache.commons.lang3.BooleanUtils;

import java.util.Arrays;
import java.util.Comparator;
import java.util.List;
import java.util.Objects;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class ConversionChangeArg {

	private String ea;
	private List<ChangeModel> changeModelList;


 	@Data
	@ToString
	public static class ChangeModel {
		private String roomId;
		private String fromCipherId;
		private String toCipherId;
		private String fromPlaintextId;
		private String toPlaintextId;
		private String tenantId;
		private String ea;
		private Boolean intelligentMessage;

		public ChangeModel() {

		}
		public ChangeModel(String fromPlaintextId, String toPlaintextId) {
			this.fromPlaintextId = fromPlaintextId;
			this.toPlaintextId = toPlaintextId;
		}
		public ChangeModel(String roomId) {
			this.roomId = roomId;
		}

		@Override
		public boolean equals(Object o) {
			if (this == o)
				return true;
			if (o == null || getClass() != o.getClass())
				return false;
			ChangeModel that = (ChangeModel) o;
			if (this.roomId != null) {
				return Objects.equals(this.roomId, that.roomId);
			}
			if (that.roomId != null) {
				return false;
			}

			if (BooleanUtils.isTrue(this.intelligentMessage)) {
				boolean boo1 =Objects.equals(this.fromCipherId, that.fromCipherId) && Objects.equals(this.toCipherId, that.toCipherId);
				boolean boo2 = Objects.equals(this.fromCipherId, that.toCipherId) && Objects.equals(this.toCipherId, that.fromCipherId);
				return boo1 || boo2;
			}

			boolean boo1 = Objects.equals(fromPlaintextId, that.fromPlaintextId) && Objects.equals(toPlaintextId, that.toPlaintextId);
			boolean boo2 = Objects.equals(fromPlaintextId, that.toPlaintextId) && Objects.equals(toPlaintextId, that.fromPlaintextId);
			return boo1 || boo2;
		}

		@Override
		public int hashCode() {
			if (this.roomId != null) {
				return Objects.hash(roomId);
			}
			String[] array;
			if (BooleanUtils.isTrue(this.intelligentMessage)) {
				array = new String[]{fromCipherId, toCipherId};
			} else {
				array = new String[]{fromPlaintextId, toPlaintextId};
			}
			Arrays.sort(array, Comparator.naturalOrder());
			return Arrays.hashCode(array);
		}
	}

}
