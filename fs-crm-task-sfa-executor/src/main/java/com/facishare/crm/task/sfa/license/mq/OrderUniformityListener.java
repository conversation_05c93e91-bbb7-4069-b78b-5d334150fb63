package com.facishare.crm.task.sfa.license.mq;

import com.alibaba.fastjson.JSON;
import com.facishare.crm.task.sfa.services.model.OrderUniformityMessage;
import com.facishare.crm.task.sfa.services.uniformity.OrderUniformityServiceManager;
import com.facishare.crm.task.sfa.services.validate.manager.OrderValidationChainServiceManager;
import com.facishare.crm.task.sfa.util.constant.SalesOrderUniformityConstants;
import com.facishare.paas.appframework.common.mq.RocketMQMessageListener;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.common.message.MessageExt;
import org.springframework.beans.factory.annotation.Autowired;

import java.nio.charset.Charset;
import java.util.List;

@Slf4j
public class OrderUniformityListener implements RocketMQMessageListener {
    @Autowired
    private OrderUniformityServiceManager serviceManager;
    @Autowired
    private OrderValidationChainServiceManager orderValidationChainServiceManager;

    interface AsyncTaskBiz {
        String SALES_ORDER_UNIFORMITY = "sales_order_uniformity";
        String SALES_ORDER_AMORTIZE = "sales_order_amortize";
    }

    @Override
    public void consumeMessage(List<MessageExt> messages) {
        if (CollectionUtils.empty(messages)) {
            return;
        }
        for (MessageExt message : messages) {
            log.debug("message:{}", messages);
            try {
                consumeMessage(message);
            } catch (Exception e) {
                log.error("consumeMessage error", e);
            }
        }
    }

    private void consumeMessage(MessageExt message) throws Exception {
        String body = new String(message.getBody(), Charset.forName("UTF-8"));
        switch (message.getTags()) {
            case AsyncTaskBiz.SALES_ORDER_UNIFORMITY:
                OrderUniformityMessage messageBody = JSON.parseObject(body, OrderUniformityMessage.class);
                if (null != messageBody) {
                    messageBody.setMsgId(message.getMsgId());
                    // 兼容老版本，全网后可以去掉
                    if (SalesOrderUniformityConstants.SalesOrderUniformityTypeEnum.POLICY_AMORTIZE.getName().equals(messageBody.getType())) {
                        serviceManager.getAmortizeService(messageBody.getType()).executeAmortizeUniformity(messageBody);
                    } else {
                        serviceManager.getService(messageBody.getType()).executeOrderUniformity(messageBody);
                    }
                }
                break;
            case AsyncTaskBiz.SALES_ORDER_AMORTIZE:
                log.info("sales order amortize"+body);
                messageBody = JSON.parseObject(body, OrderUniformityMessage.class);
                if (null != messageBody) {
                    messageBody.setMsgId(message.getMsgId());
                    try {
                        serviceManager.getAmortizeService(messageBody.getType()).executeAmortizeUniformity(messageBody);
                    } finally {
                        orderValidationChainServiceManager.getService(messageBody.getType(), messageBody.getActionCode())
                                .ifPresent(x -> x.execute(messageBody));
                    }
                }
                break;
            default:
                log.warn("{} tags not supported.", message.getTags());

        }
    }
}

