package com.facishare.crm.task.sfa.services.channel.core;

import com.facishare.crm.sfa.prm.platform.utils.ObjectUtils;
import com.facishare.crm.task.sfa.services.channel.ChannelMachine;
import com.facishare.crm.task.sfa.services.channel.ActionExecutor;
import com.facishare.crm.task.sfa.services.channel.enums.ChannelEvent;
import com.facishare.crm.task.sfa.services.channel.enums.RouterType;
import com.facishare.crm.task.sfa.services.channel.exception.ItemNullException;
import com.facishare.crm.task.sfa.services.channel.model.ChannelContext;
import com.facishare.crm.task.sfa.services.channel.model.RegisterMessage;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.core.model.User;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * ============================================================
 *
 * @Description:
 * @CreatedBy: Sundy on 2025-05-22
 * ============================================================
 */
@Slf4j
@Component
public class RegisterActionExecutor implements ActionExecutor {
    @Resource
    private ChannelMachine channelMachine;

    @Override
    public void execute(String body) {
        RegisterMessage registerMessage = ObjectUtils.convertObject(body, RegisterMessage.class);
        if (registerMessage == null) {
            throw new ItemNullException("RegisterMessage is null");
        }
        User systemUser = registerMessage.createSystemUser();
        I18N.setContext(systemUser.getTenantId(), registerMessage.getLanguage());
        log.info("ChannelEventRouter#register mq consume, registerMessage:{}", registerMessage);
        log.info("ChannelEventRouter#register begin");
        ChannelContext channelContext = ChannelContext.builder()
                .user(systemUser)
                .payload(registerMessage)
                .build();
        if (registerMessage.isPass()) {
            channelMachine.getInstance().fire(registerMessage.fetchCurrentStatus(), ChannelEvent.REGISTER_PASS, channelContext);
        } else {
            channelMachine.getInstance().fire(registerMessage.fetchCurrentStatus(), ChannelEvent.REGISTER_REJET, channelContext);
        }
        log.info("ChannelEventRouter#register end");
    }

    @Override
    public RouterType getRouterType() {
        return RouterType.REGISTER;
    }
}
