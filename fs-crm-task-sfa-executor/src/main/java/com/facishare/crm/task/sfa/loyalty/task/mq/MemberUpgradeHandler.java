package com.facishare.crm.task.sfa.loyalty.task.mq;

import com.alibaba.fastjson.JSONObject;
import com.facishare.crm.sfa.lto.loyalty.constants.LoyaltyConstants;
import com.facishare.crm.sfa.lto.loyalty.model.Loyalty;
import com.facishare.crm.sfa.lto.loyalty.utils.NumberUtils;
import com.facishare.crm.task.sfa.loyalty.model.TaskLoyalty;
import com.facishare.crm.task.sfa.loyalty.service.LoyaltyTierBenefitsService;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.metadata.api.IObjectData;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
public class MemberUpgradeHandler extends LoyaltyMqHandler {

    @Resource
    ServiceFacade serviceFacade;
    @Resource
    LoyaltyTierBenefitsService loyaltyTierBenefitsService;

    @Override
    public Tag getTag() {
        return Tag.memberUpgrade;
    }

    @Override
    public void handler(String body) {
        Loyalty.MemberUpgrade memberUpgrade = JSONObject.parseObject(body, Loyalty.MemberUpgrade.class);
        String tenantId = memberUpgrade.getTenantId();
        String memberId = memberUpgrade.getMemberId();

        List<IObjectData> tierBenefitsList = loyaltyTierBenefitsService.findTierBenefits(new TaskLoyalty.TierBenefitsParam(tenantId, memberUpgrade.getAfterTierId(), "sys_coupon"));
        if (CollectionUtils.isEmpty(tierBenefitsList)) {
            return;
        }
        Map<String, IObjectData> map = findTierObj(tenantId, memberUpgrade.getBeforeTierId(), memberUpgrade.getAfterTierId());
        IObjectData beforeTier = map.get(memberUpgrade.getBeforeTierId());
        IObjectData afterTier = map.get(memberUpgrade.getAfterTierId());
        if (afterTier == null) {
            log.info("未找到会员等级,不处理升级逻辑");
            return;
        }
        if (!StringUtils.isEmpty(memberUpgrade.getBeforeTierId())) {
            if (beforeTier == null) {
                log.info("未找到会员等级,不处理升级逻辑");
                return;
            }
            if (!afterTier.get(LoyaltyConstants.LoyaltyTier.TIER_CLASS_ID, String.class).equals(beforeTier.get(LoyaltyConstants.LoyaltyTier.TIER_CLASS_ID, String.class))) {
                log.info("等级不属于同一个等级分类,不处理升级逻辑");
                return;
            }
            long beforeTierRequiredPoints = NumberUtils.getLong(beforeTier, LoyaltyConstants.LoyaltyTier.POINTS_REQUIRED);
            long afterTierRequiredPoints = NumberUtils.getLong(afterTier, LoyaltyConstants.LoyaltyTier.POINTS_REQUIRED);
            if (beforeTierRequiredPoints >= afterTierRequiredPoints) {
                log.info("变动前等级小于变动后等级,不发优惠券");
                return;
            }
        }
        for (IObjectData benefit : tierBenefitsList) {
            loyaltyTierBenefitsService.couponHandler(tenantId, memberId, benefit, afterTier);
        }
    }

    /**
     * 查询等级
     */
    private Map<String, IObjectData> findTierObj(String tenantId, String... tierIds) {
        List<String> tierIdList = new ArrayList<>();
        for (String tier : tierIds) {
            if (!StringUtils.isEmpty(tier)) {
                tierIdList.add(tier);
            }
        }
        Map<String, IObjectData> map = new HashMap<>();
        List<IObjectData> list = serviceFacade.findObjectDataByIdsIgnoreAll(tenantId, tierIdList, LoyaltyConstants.LoyaltyTier.API_NAME);
        for (IObjectData data : list) {
            map.put(data.getId(), data);
        }
        return map;
    }


}
