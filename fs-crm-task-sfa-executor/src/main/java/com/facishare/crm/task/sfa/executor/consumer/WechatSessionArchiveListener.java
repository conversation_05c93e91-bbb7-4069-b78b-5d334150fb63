package com.facishare.crm.task.sfa.executor.consumer;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.facishare.crm.task.sfa.services.qywx.archive.WechatSessionArchiveService;
import com.facishare.paas.metadata.api.Tenantable;
import com.fxiaoke.rocketmq.consumer.AutoConfMQPushConsumer;
import com.fxiaoke.rocketmq.util.MessageHelper;
import com.github.trace.TraceContext;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.client.consumer.listener.ConsumeConcurrentlyStatus;
import org.apache.rocketmq.client.consumer.listener.MessageListenerConcurrently;
import org.apache.rocketmq.common.message.MessageExt;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.context.event.ContextRefreshedEvent;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;

import java.nio.charset.StandardCharsets;

@Slf4j
@Component
public class WechatSessionArchiveListener implements InitializingBean, AutoCloseable {
    private final WechatSessionArchiveService service;

    private AutoConfMQPushConsumer consumer;

    public WechatSessionArchiveListener(WechatSessionArchiveService service) {
        this.service = service;
    }

    private void consumeMessage(MessageExt msg) {
        if ("sfa-archive-previous-day-wechat-session".equals(msg.getTags())) {
            String bodyStr = new String(msg.getBody(), StandardCharsets.UTF_8);
            JSONObject body = JSON.parseObject(bodyStr);
            log.info("receive msg [{}]: {}", msg.getMsgId(), body);
            String tenantId = body.getString(Tenantable.TENANT_ID);
            service.previousDaySession(tenantId);
        }
    }

    @Override
    public void afterPropertiesSet() {
        consumer = new AutoConfMQPushConsumer("sfa-recalculate-consumer", "sfa-wechat-session-schedule", (MessageListenerConcurrently) (list, context) -> {
            if (!list.isEmpty()) {
                for (MessageExt msg : list) {
                    MessageHelper.fillContextFromMessage(TraceContext.get(), msg);
                    try {
                        consumeMessage(msg);
                    } catch (Exception e) {
                        log.error("consume message error: ", e);
                    }
                }
            }
            return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
        });
    }

    @EventListener(ContextRefreshedEvent.class)
    public void refresh(ContextRefreshedEvent event) {
        if (consumer != null && event.getApplicationContext().getParent() == null) {
            consumer.start();
        }
    }

    @Override
    public void close() throws Exception {
        if (consumer != null) {
            consumer.close();
        }
    }
}
