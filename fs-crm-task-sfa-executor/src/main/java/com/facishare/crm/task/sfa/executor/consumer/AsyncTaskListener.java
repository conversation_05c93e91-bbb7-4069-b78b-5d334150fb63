package com.facishare.crm.task.sfa.executor.consumer;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.facishare.crm.sfa.prm.platform.utils.TraceGenerator;
import com.facishare.crm.task.sfa.model.*;
import com.facishare.crm.task.sfa.rest.CRMMetaDataService;
import com.facishare.crm.task.sfa.services.*;
import com.facishare.crm.task.sfa.services.changeowner.ChangeRelateObjectOwnerService;
import com.facishare.crm.task.sfa.services.channel.ChannelEventRouter;
import com.facishare.crm.task.sfa.services.channel.enums.RouterType;
import com.facishare.paas.appframework.common.mq.RocketMQMessageListener;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.ObjectMappingService;
import com.facishare.paas.metadata.api.IObjectMappingRuleInfo;
import com.github.trace.TraceContext;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.rocketmq.common.message.MessageExt;
import org.springframework.beans.factory.annotation.Autowired;

import javax.annotation.Resource;
import java.nio.charset.Charset;
import java.util.List;
import java.util.Objects;

/**
 * 异步任务监听器
 *
 * <AUTHOR>
 */
@Slf4j
public class AsyncTaskListener implements RocketMQMessageListener {
    @Autowired
    private AttributeService attributeService;

    @Autowired
    AttributePriceBookServiceImpl attributePriceBookService;

    @Autowired
    private SyncBomNodeService syncBomNodeService;

    @Autowired
    private OccupyService occupyService;

    @Autowired
    private CopyPriceBookProductService copyPriceBookProductService;
    @Autowired
    private MultiUnitPriceBookBizService multiUnitPriceBookBizService;
    @Autowired
    private SaleContractBizService saleContractBizService;
    @Autowired
    private UpdateAggregateFieldService updateAggregateFieldService;
    @Autowired
    private InitLayoutRuleService layoutRuleService;
    @Autowired
    private SyncBomInstanceService syncBomInstanceService;
    @Autowired
    private SyncBomConstraintService syncBomConstraintService;
    @Autowired
    private ProductCategoryBizService productCategoryBizService;
    @Autowired
    private ChangeRelateObjectOwnerService changeRelateObjectOwnerService;
    @Autowired
    private ManualGiftService manualGiftService;
    @Autowired
    private OpenRebateService openRebateService;
    @Autowired
    private PrmService prmService;
    @Autowired
    private StandardBomService standardBomService;
    @Autowired
    private PartnerService partnerService;

    @Autowired
    private MasterDataBizService masterDataBizService;
    @Autowired
    private CRMMetaDataService crmMetaDataService;
    @Autowired
    private ObjectMappingService objectMappingService;
    @Autowired
    private StandardBomCommonService standardBomCommonService;
    @Resource
    private ChannelConsumer channelConsumer;
    @Resource
    private PartnerChannelInitService partnerChannelInitService;
    @Resource
    private ProductCategoryConsumer productCategoryConsumer;
    @Resource
    private PartnerLoyaltyInitService partnerLoyaltyInitService;
    @Autowired
    private PeriodicRefreshProductService periodicRefreshProductService;
    @Autowired
    private SalesOrderBizService salesOrderBizService;
    @Resource
    private ChannelEventRouter channelEventRouter;
    @Resource
    private ApiCallLogService apiCallLogService;

    interface AsyncTaskBiz {
        String ATTRIBUTE_PRODUCT = "ATTRIBUTE_PRODUCT";
        String DEL_ATTRIBUTE_VALUE_UPDATE_ATTRIBUTE_PRICE_BOOK = "DEL_ATTRIBUTE_VALUE_UPDATE_ATTRIBUTE_PRICE_BOOK";
        String PRODUCT_DISASSOCIATE_ATTRIBUTE_PRICE_BOOK = "PRODUCT_DISASSOCIATE_ATTRIBUTE_PRICE_BOOK";
        String SYNC_BOM_TO_OTHERS = "SYNC_BOM_TO_OTHERS";
        String SALES_ORDER_CHANGE_OCCUPY = "SALES_ORDER_CHANGE_OCCUPY";
        String COPY_PRICE_BOOK_PRODUCT = "COPY_PRICE_BOOK_PRODUCT";
        String REFRESH_PRICE_BOOK_PRODUCT_UNIT = "REFRESH_PRICE_BOOK_PRODUCT_UNIT";
        String REFRESH_SALE_CONTRACT = "REFRESH_SALE_CONTRACT";
        String UPDATE_AGGREGATE_FIELD = "UPDATE_AGGREGATE_FIELD";
        String INIT_LAYOUT_RULE = "INIT_LAYOUT_RULE";
        String SYNC_BOM_INSTANCE_CHANGE = "SYNC_BOM_INSTANCE_CHANGE";
        String SYNC_CATEGORY_FIELD = "SYNC_CATEGORY_FIELD";
        String CHANGE_RELATE_OBJECT_OWNER = "CHANGE_RELATE_OBJECT_OWNER";

        String UPDATE_AGGREGATE_OBJ = "UPDATE_AGGREGATE_OBJ";
        String UPDATE_MANUAL_GIFT_TYPE_FIELD = "UPDATE_MANUAL_GIFT_TYPE_FIELD";

        String OPEN_REBATE_MESSAGE = "OPEN_REBATE_MESSAGE";
        String SYNC_BOM_CONSTRAINT_CHANGE = "SYNC_BOM_CONSTRAINT_CHANGE";
        String CHANNEL_REGISTER_CREATE_ENTERPRISE = "CHANNEL_REGISTER_CREATE_ENTERPRISE";
        String CHANNEL_SIGNING_APPROVAL = "CHANNEL_SIGNING_APPROVAL";
        String SALES_ORDER_CREATE_STANDARD_BOM = "SALES_ORDER_CREATE_STANDARD_BOM";
        String COMMON_CREATE_STANDARD_BOM = "COMMON_CREATE_STANDARD_BOM";
        String OPEN_PARTNER = "OPEN_PARTNER";
        String MASTER_DATA_APP_SYNC_MAPPING = "MASTER_DATA_APP_SYNC_MAPPING";
        String RENEW_EXPIRATION = "RENEW_EXPIRATION";
        String RENEW_EXPIRATION_BUTTON = "RENEW_EXPIRATION_BUTTON";
        String OPEN_CATEGORY_SWITCH = "OPEN_CATEGORY_SWITCH";
        String MANUAL_INITIATE_RENEWAL = "MANUAL_INITIATE_RENEWAL";
        String MANUAL_INITIATE_RENEWAL_BUTTON = "MANUAL_INITIATE_RENEWAL_BUTTON";
        String CHANNEL_RENEWAL_SUCCESSFUL = "CHANNEL_RENEWAL_SUCCESSFUL";
        String CHANNEL_RENEWAL_SIGN_SUCCESSFUL = "CHANNEL_RENEWAL_SIGN_SUCCESSFUL";
        String PERIODIC_REFRESH_PRODUCT = "PERIODIC_REFRESH_PRODUCT";

        String CONTRACT_TYPE_UPDATE_DATA = "CONTRACT_TYPE_UPDATE_DATA";
        String CHANGE_AGREEMENT_DETAIL_STATUS = "CHANGE_AGREEMENT_DETAIL_STATUS";
        String OPEN_CHANNEL_ACCESS = "OPEN_CHANNEL_ACCESS";
        String HANDLE_TRANSFER_ORDER = "HANDLE_TRANSFER_ORDER";
        String REGISTER_EVENT = "REGISTER_EVENT";
        String SIGN_EVENT = "SIGN_EVENT";
        String RENEW_EVENT = "RENEW_EVENT";
        String API_CALL_LOG = "API_CALL_LOG";
    }

    @SneakyThrows
    @Override
    public void consumeMessage(List<MessageExt> messages) {
        if (CollectionUtils.empty(messages)) {
            return;
        }
        for (MessageExt message : messages) {
            log.debug("AsyncTaskBiz message:{}", messages);
            consumeMessage(message);
        }
    }

    private void consumeMessage(MessageExt message) throws Exception {
        //往日志里注册traceId
        if (Objects.isNull(TraceContext.get().getTraceId())) {
            String traceId = Objects.isNull(message.getKeys()) ? message.getMsgId() : message.getKeys();
            TraceContext.get().setTraceId(traceId);
        }
        String body = new String(message.getBody(), Charset.forName("UTF-8"));
        switch (message.getTags().toUpperCase()) {
            case AsyncTaskBiz.ATTRIBUTE_PRODUCT:
                AttributeMessage attributeMessage = JSON.parseObject(body, AttributeMessage.class);
                attributeService.execute(attributeMessage);
                break;
            case AsyncTaskBiz.HANDLE_TRANSFER_ORDER:
                salesOrderBizService.refreshSalesOrderData(body, false);
                break;
            case AsyncTaskBiz.DEL_ATTRIBUTE_VALUE_UPDATE_ATTRIBUTE_PRICE_BOOK:
                AttributeValueMessage attributeValueMessage = JSON.parseObject(body, AttributeValueMessage.class);
                attributePriceBookService.executeAttributeValue(attributeValueMessage);
                break;
            case AsyncTaskBiz.PRODUCT_DISASSOCIATE_ATTRIBUTE_PRICE_BOOK:
                AttributeMessage attributeMessage1 = JSON.parseObject(body, AttributeMessage.class);
                attributePriceBookService.executeAttribute(attributeMessage1);
                break;
            case AsyncTaskBiz.SYNC_BOM_TO_OTHERS:
                syncBomNodeService.execute(body);
                break;
            case AsyncTaskBiz.SALES_ORDER_CHANGE_OCCUPY:
                PolicyOccupyModel policyOccupyModel = JSON.parseObject(body, PolicyOccupyModel.class);
                occupyService.execute(policyOccupyModel);
                break;
            case AsyncTaskBiz.COPY_PRICE_BOOK_PRODUCT:
                CopyPriceBookProductMessage copyPriceBookProductMessage = JSON.parseObject(body, CopyPriceBookProductMessage.class);
                copyPriceBookProductService.execute(copyPriceBookProductMessage);
                break;
            case AsyncTaskBiz.REFRESH_PRICE_BOOK_PRODUCT_UNIT:
                MultiUnitPriceBookMessage multiUnitPriceBookMessage = JSON.parseObject(body, MultiUnitPriceBookMessage.class);
                multiUnitPriceBookBizService.refreshPriceBookProductActualUnit(multiUnitPriceBookMessage.getTenantId());
                break;
            case AsyncTaskBiz.UPDATE_AGGREGATE_FIELD:
                updateAggregateFieldService.execute(body);
                break;
            case AsyncTaskBiz.UPDATE_AGGREGATE_OBJ:
                updateAggregateFieldService.executeUpdateAggregateObj(body);
                break;
            case AsyncTaskBiz.SYNC_BOM_INSTANCE_CHANGE:
                syncBomInstanceService.execute(body);
                break;
            case AsyncTaskBiz.SYNC_BOM_CONSTRAINT_CHANGE:
                syncBomConstraintService.execute(body);
                break;
            case AsyncTaskBiz.INIT_LAYOUT_RULE:
                InitLayoutRuleMessage initLayoutRuleMessage = JSON.parseObject(body, InitLayoutRuleMessage.class);
                layoutRuleService.executeInitLayoutRule(initLayoutRuleMessage);
                break;
            case AsyncTaskBiz.SYNC_CATEGORY_FIELD:
                productCategoryBizService.syncCategoryField(body);
                break;
            case AsyncTaskBiz.CHANGE_RELATE_OBJECT_OWNER:
                // 更换对象负责人，同步更换相关对象负责人
                changeRelateObjectOwnerService.changeRelateObjectOwner(body);
                break;
            case AsyncTaskBiz.UPDATE_MANUAL_GIFT_TYPE_FIELD:
                manualGiftService.execute(body);
                break;
            case AsyncTaskBiz.REFRESH_SALE_CONTRACT:
                saleContractBizService.refreshSaleContractLineMultiUnitData(body);
                break;
            case AsyncTaskBiz.OPEN_REBATE_MESSAGE:
                JSONObject jsonObject = JSON.parseObject(body);
                String tenantId = jsonObject.getString("tenantId");
                String userId = jsonObject.getString("userId");
                openRebateService.openRebate(tenantId, userId);
                break;
            case AsyncTaskBiz.REGISTER_EVENT:
                // 渠道准入：创建互联企业审批
                channelEventRouter.route(RouterType.REGISTER, body);
                break;
            case AsyncTaskBiz.CHANNEL_REGISTER_CREATE_ENTERPRISE:
                // 渠道准入：创建互联企业审批 950
                channelConsumer.channelRegisterCreateEnterpriseHandler_950(body);
                break;
            case AsyncTaskBiz.SIGN_EVENT:
                // 渠道准入：签约审批
                channelEventRouter.route(RouterType.SIGN, body);
                break;
            case AsyncTaskBiz.CHANNEL_SIGNING_APPROVAL:
                // 渠道准入：签约审批 950
                channelConsumer.channelSigningApprovalHandle_950(body);
                break;
            case AsyncTaskBiz.RENEW_EXPIRATION:
                // 渠道准入：「申请延期」按钮
                channelConsumer.renewExpirationHandle(body);
                break;
            case AsyncTaskBiz.RENEW_EXPIRATION_BUTTON:
                // 渠道准入：「申请延期」按钮 950
                channelConsumer.renewExpirationButtonHandle(body);
                break;
            case AsyncTaskBiz.MANUAL_INITIATE_RENEWAL:
                // 渠道准入：「发起签约」按钮
                channelConsumer.manualInitiateRenewalHandle(body);
                break;
            case AsyncTaskBiz.MANUAL_INITIATE_RENEWAL_BUTTON:
                // 渠道准入：「发起签约」按钮 950
                channelConsumer.manualInitiateRenewalButtonHandle_950(body);
                break;
            case AsyncTaskBiz.RENEW_EVENT:
                // 渠道准入：续签成功按钮（合作伙伴协议明细的续签审批通过）
                channelEventRouter.route(RouterType.RENEW, body);
                break;
            case AsyncTaskBiz.CHANNEL_RENEWAL_SIGN_SUCCESSFUL:
                // 渠道准入：续签成功按钮（合作伙伴协议明细的续签审批通过） 950
                channelConsumer.renewalSignSuccessfulHandle_950(body);
                break;
            //即将废弃
            case AsyncTaskBiz.SALES_ORDER_CREATE_STANDARD_BOM:
                standardBomService.execute(body);
                break;
            case AsyncTaskBiz.COMMON_CREATE_STANDARD_BOM:
                //更换tag，处理逻辑沿用原来的，为了兼容老tag,两个case执行逻辑一致
                standardBomCommonService.execute(body);
                break;
            case AsyncTaskBiz.OPEN_PARTNER:
                try {
                    openPartnerJob(body);
                } catch (Exception e) {
                    log.error("AsyncTaskListener#listen partner open Job execute error", e);
                }
                break;
            case AsyncTaskBiz.MASTER_DATA_APP_SYNC_MAPPING:
                masterDataBizService.saveSyncMapping(body);
                break;
            case AsyncTaskBiz.OPEN_CATEGORY_SWITCH:
                // 开启合作伙伴对象化开关
                productCategoryConsumer.openCategorySwitchConsumer(body);
                break;
            case AsyncTaskBiz.PERIODIC_REFRESH_PRODUCT:
                // 开周期性产品刷产品定价模式
                PeriodicRefreshProductMessage periodicRefreshProductMessage = JSON.parseObject(body, PeriodicRefreshProductMessage.class);
                periodicRefreshProductService.refreshData(periodicRefreshProductMessage);
                break;
            case AsyncTaskBiz.CONTRACT_TYPE_UPDATE_DATA:
                SaleContractUpdateDataModel saleContractUpdateDataModel = JSON.parseObject(body, SaleContractUpdateDataModel.class);
                saleContractBizService.updateContractType(saleContractUpdateDataModel);
                break;

            case AsyncTaskBiz.CHANGE_AGREEMENT_DETAIL_STATUS:
                // 启用、停用签约方案后，需要更新协议明细上的签约状态字段
                channelConsumer.changeAgreementDetailStatus(body);
                break;
            case AsyncTaskBiz.OPEN_CHANNEL_ACCESS:
                // 开启渠道准入
                channelConsumer.openChannelAccess(body);
                break;
            case AsyncTaskBiz.API_CALL_LOG:
                // 开启渠道准入
                apiCallLogService.callLog(body);
                break;
            default:
                log.warn("{} tags not supported.", message.getTags());
        }
    }

    private void openPartnerJob(String body) {
        TraceGenerator.generator();
        JSONObject jsonObjectOfOpenPartner = JSON.parseObject(body);
        String tenant = jsonObjectOfOpenPartner.getString("tenantId");
        Boolean success = jsonObjectOfOpenPartner.getBoolean("success");
        User user = User.systemUser(tenant);
        openPartnerJob(user, success);
    }

    /**
     * @param user
     * @param success 开启成功 true、开始失败 false
     */
    private void openPartnerJob(User user, Boolean success) {
        log.info("AsyncTaskListener#openPartnerJob start, tenantId:{}, user:{}, success:{}", user.getTenantId(), user.getUserId(), success);
        try {
            if (Boolean.TRUE.equals(success) && partnerLoyaltyInitService.existsModule(user, "member_loyalty_app")) {
                partnerLoyaltyInitService.init(user.getTenantId());
            }
        } catch (Exception e) {
            log.error("AsyncTaskListener#openPartnerJob partnerLoyaltyInitService.init error", e);
        }
        try {
            partnerService.activatePartnerDepartment(user.getTenantId(), success);
        } catch (Exception e) {
            log.error("AsyncTaskListener#openPartnerJob activatePartnerDepartment error", e);
        }
        try {
            if (Boolean.TRUE.equals(success) && prmService.existsChannelManagementModule(user)) {
                partnerChannelInitService.init(user);
            }
        } catch (Exception e) {
            log.error("AsyncTaskListener#openPartnerJob partnerChannelInit error", e);
        }
        List<IObjectMappingRuleInfo> ruleList = objectMappingService.findByApiName(user, "rule_procuremententerpriseobj2partnerobj__c");
        if (ObjectUtils.isEmpty(ruleList)) {
            String mappingName = "\"rule_procuremententerpriseobj2partnerobj__c\"";
            crmMetaDataService.initMappingRule(user.getTenantId(), mappingName);
            log.info("initMappingRule success:{}", ruleList);
        }
        crmMetaDataService.upgradeRequirementDescribe(user.getTenantId());
    }
}
