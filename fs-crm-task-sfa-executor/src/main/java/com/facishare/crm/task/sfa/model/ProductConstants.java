package com.facishare.crm.task.sfa.model;

/**
 * <AUTHOR>
 * @date 2021/12/28 19:09
 */
public class ProductConstants {
    public static final String ADD_ACTION = "Add";
    public static final String ABOLISH_ACTION = "Abolish";
    public static final String DELETE_ACTION = "Delete";
    public static final String RECOVER_ACTION = "Recover";
    public static final String SUPER_ADMIN_USER_ID = "-10000";

    public static final String PRICING_MODE = "pricing_mode";

    public enum PricingModeEnum {
        ONE("one", "一次性"),
        CYCLE("cycle", "周期性");

        private String value;
        private String label;

        PricingModeEnum(String value, String label) {
            this.value = value;
            this.label = label;
        }

        public String getValue() {
            return this.value;
        }
    }
}
