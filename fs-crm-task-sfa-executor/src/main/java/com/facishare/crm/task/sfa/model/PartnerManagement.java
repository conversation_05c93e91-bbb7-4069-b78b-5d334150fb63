package com.facishare.crm.task.sfa.model;

import com.facishare.crm.sfa.prm.model.SmsMessageContent;
import com.facishare.crm.task.sfa.enums.ReminderTriggerEnums;
import com.facishare.crm.task.sfa.enums.TimeUnitEnums;
import com.facishare.crm.task.sfa.services.model.PrmModel;
import com.google.common.collect.Lists;
import lombok.*;
import org.apache.commons.collections4.CollectionUtils;

import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @time 2023-09-27 14:36
 * @Description
 */
public interface PartnerManagement {
    String SIGN_BIZ_SCOPE = "sign";
    String REGISTER_BIZ_SCOPE = "register";
    String EMAIL_NOTIFY_VIA = "email";
    String SMS_NOTIFY_VIA = "sms";

    @Data
    class ChannelConfigInfo {
        private String configId = "";
        private List<String> smsPhones = Lists.newArrayList();
        private List<String> defaultRoles = Lists.newArrayList();
        private SmsContent smsPassContent;
        private SmsContent smsFailContent;
        private List<String> noticeTypes = Lists.newArrayList();
        private String owner;
    }

    @Builder
    @Getter
    @Setter
    @AllArgsConstructor
    @NoArgsConstructor
    class SmsContent {
        @Builder.Default
        private String content = "";
        @Builder.Default
        private String templateId = "";
        @Builder.Default
        private List<ContentParam> smsContentParam = Lists.newArrayList();
    }

    @Builder
    @Getter
    @Setter
    @AllArgsConstructor
    @NoArgsConstructor
    class ContentParam {
        private String key;
        private String type;
        private String value;
    }

    @Data
    class ChannelConfigInfoResult {
        private int errCode;
        private String errMessage;
        private ChannelConfigInfo result;
    }

    @Data
    class InitMappingResult {
        private int errCode;
        private String errMessage;
        private Boolean result;
    }

    @Data
    class CreateDataResult {
        private int errCode;
        private String errMessage;
        private PrmModel.CreateDataResult result;
    }

    @Data
    class PrmNoticeConfigResult {
        private int errCode;
        private String errMessage;
        private PrmManagementModel.NoticeConfigResult result;
    }

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    class EnterpriseActivationSetting {
        private Boolean effective;
        private String enterpriseActivationSettingId;
        private String condition;
        private String conditionType;
        private List<String> defaultRoles;
        private String enterpriseType;
        private String recyclingMode;
        private Integer expireDays;
        private Integer expireCycleMonth;
        private Integer expireCycleDay;
        private Integer priority;
    }

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    class EnterpriseActivationSettingResult {
        private int errCode;
        private String errMessage;
        private EnterpriseActivationSetting result;

        public boolean isSuccess() {
            return result != null && Boolean.TRUE.equals(result.getEffective());
        }
    }

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    class SettingArg {
        private String dataId;
        private String reminderTrigger;
    }


    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    class ApprovalNotice {
        private String approvalNoticeId;
        private String notifyVia;
        private String aplApiName;
        private String actionVia;
        private String sender;
        private List<String> receiver;
        private List<String> noticeInfo;
        @Builder.Default
        private Boolean enabled = false;
        private String bizScope;
    }

    @Data
    class ApprovalNoticeArg {
        // register、sign
        private String bizScope;
    }

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    class ApprovalNoticeAggregator {
        private Boolean effective;
        private String approvalNoticeId;
        private String notifyVia;
        private String aplApiName;
        private String actionVia;
        private String sender;
        private Set<String> receiver;
        private PrmEmail passEmail;
        private PrmEmail nonPassEmail;
        private ShortMessage passSms;
        private ShortMessage nonPassSms;
    }

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    class ApprovalNoticeAggregatorResult {
        private int errCode;
        private String errMessage;
        private List<ApprovalNoticeAggregator> result;

        public boolean isSuccess() {
            return !CollectionUtils.isEmpty(result);
        }

    }

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    class PrmEmail {
        private String emailId;
        private String emailType;
        private String templateId;
        private String objectApiName;
        private String category;
    }

    @Builder
    @Getter
    @Setter
    @AllArgsConstructor
    @NoArgsConstructor
    class ShortMessage {
        @Builder.Default
        private String smsId = "";
        @Builder.Default
        private String templateId = "";
        @Builder.Default
        // custom_sms、template_sms
        private String smsType = "custom_sms";
        @Builder.Default
        private String content = "";
        // pass、non_pass
        private String category = "";
        @Builder.Default
        private List<SmsMessageContent.SmsContentParam> smsContentParam = Lists.newArrayList();
    }

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    class ApprovalNoticeAggregatorMatch {
        private int errCode;
        private String errMessage;
        private AggregatorMatch result;

        public boolean isSuccess() {
            return result != null && Boolean.TRUE.equals(result.getEffective());
        }
    }

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    class ReminderCache {
        private String outTenantId;
        private String outUserId;
    }

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    class ReminderCacheArg {
        private List<ReminderCache> reminderCacheList;
    }

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    class SignReminderInfoResult {
        private int errCode;
        private String errMessage;
        private SignReminderInfo result;

        public boolean isSuccess() {
            return result != null && Boolean.TRUE.equals(result.getEffective());
        }
    }

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    class AggregatorMatch {
        private Boolean effective;
        private List<String> activateRoles;
        private List<ApprovalNoticeAggregator> approvalNoticeAggregators;
    }


    @Builder
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    class OutUserSelectArg {
        private String destEnterpriseAccount;
        private Integer pageNumber;
        private Integer pageSize;
        private Integer queryType;
    }

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    class SignReminderInfo {
        @Builder.Default
        private Boolean effective = Boolean.TRUE;
        private String signTimeUnit;
        private Integer signTime;
        private String reminderTrigger;
        private String scheduleType;
        private Integer fixedMonth;
        private Integer fixedDay;
        private List<ReminderType> reminderTypes;
        private ExpireReminderPersonView expireReminderPersonView;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @Builder
    class ExpireReminderPersonView {
        @Builder.Default
        private List<ReminderPerson> internalPerson = Lists.newArrayList();
        @Builder.Default
        private List<ReminderPerson> externalPerson = Lists.newArrayList();
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @Builder
    class ReminderPerson {
        private String reminderPersonId;
        private String identity;
        private String memberType;
        private String signSchemeId;
        private String dataId;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @Builder
    class ReminderType {
        private String reminderTypeId;
        @Builder.Default
        private String reminderTrigger = ReminderTriggerEnums.AUTO.getTrigger();
        private String signSchemeId;
        private String reminderMethod;
        @Builder.Default
        private String timeUnit = TimeUnitEnums.MONTH.getUnit();
        @Builder.Default
        private Integer reminderTime = 0;
        private String templateId;
        private String message;
        @Builder.Default
        private Boolean activated = false;
    }
}
