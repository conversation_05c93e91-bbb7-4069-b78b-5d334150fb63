package com.facishare.crm.task.sfa.executor.consumer;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.facishare.crm.task.sfa.services.SalesOrderBizService;
import com.fxiaoke.rocketmq.util.MessageHelper;
import com.github.trace.TraceContext;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.client.consumer.listener.ConsumeConcurrentlyContext;
import org.apache.rocketmq.client.consumer.listener.ConsumeConcurrentlyStatus;
import org.apache.rocketmq.client.consumer.listener.MessageListenerConcurrently;
import org.apache.rocketmq.common.message.MessageExt;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.nio.charset.Charset;
import java.util.List;
import java.util.Objects;


@Slf4j
@Component
public class OrderTransferStockFinishedListener implements MessageListenerConcurrently {

    @Autowired
    private SalesOrderBizService salesOrderBizService;

    @Override
    public ConsumeConcurrentlyStatus consumeMessage(List<MessageExt> msgList, ConsumeConcurrentlyContext consumeConcurrentlyContext) {
        MessageHelper.fillContextFromMessage(TraceContext.get(), msgList.get(0));
        try {
            for (MessageExt messageExt : msgList) {
                String body = new String(messageExt.getBody(), Charset.forName("UTF-8"));
                JSONObject jsonObject = JSON.parseObject(body);
                if(Objects.equals(messageExt.getTags(),"channel-partner-order-transfer-init-finished")){
                    try {
                        salesOrderBizService.after(jsonObject.getString("tenantId"),"stock");
                    } catch (Exception e) {
                        log.error("domainListener参数：{}", body, e);
                    }
                }
            }
        } finally {
            TraceContext.remove();
        }
        return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
    }
}
