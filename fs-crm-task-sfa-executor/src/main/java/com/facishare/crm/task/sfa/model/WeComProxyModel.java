package com.facishare.crm.task.sfa.model;

import com.facishare.crm.task.sfa.enums.WeChatMsgType;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @time 2024-01-18 15:38
 * @Description
 */
public interface WeComProxyModel {
    @Data
    class SendMsg2EnterpriseWechatArg {
        // 企业微信Id
        private String corpId;
        // 企业微信应用Id
        private String appId;
        // 企业微信下游人员Id
        private List<String> outUserIdList;
        private WeChatMsgType msgType;
        private Object msgContent;//取值 TextMsgContent ， TextCardMsgContent，NewsMsgContent，NewsArticle
    }

    /**
     * 小程序通知消息内容
     * <AUTHOR>
     * @date 2024-01-22
     */
    @Data
    public class MiniProgramNoticeMsgContent implements Serializable {
        private static final long serialVersionUID = 1L;
        private String appid;

        private String page;

        private String title;

        private String description;

        private boolean emphasis_first_item;

        private List<KeyValuePair> content_item;

        @Data
        public static class KeyValuePair implements Serializable {
            private String key;
            private String value;
        }
    }
}
