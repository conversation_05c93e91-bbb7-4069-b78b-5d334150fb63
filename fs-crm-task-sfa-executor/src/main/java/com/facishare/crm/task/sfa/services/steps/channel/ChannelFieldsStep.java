package com.facishare.crm.task.sfa.services.steps.channel;

import com.facishare.crm.sfa.prm.api.enhancer.DescribeEnhancer;
import com.facishare.crm.sfa.prm.platform.infrastructure.execution.InitStep;
import com.facishare.crm.task.sfa.common.describebuilder.*;
import com.facishare.crm.task.sfa.model.PartnerDataExt;
import com.facishare.crm.task.sfa.util.DescUtil;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.ISelectOption;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.api.service.IObjectDescribeService;
import com.facishare.paas.metadata.exception.MetadataServiceException;
import com.facishare.paas.metadata.impl.describe.*;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Optional;

import static com.facishare.crm.task.sfa.model.MasterDataAppMappingModel.ACCOUNT_OBJ;
import static com.facishare.crm.task.sfa.model.PartnerDataExt.PARTNER_OBJ;
import static com.facishare.crm.task.sfa.services.steps.constants.ChannelStepConstants.CHANNEL_ACCESS;

/**
 * ============================================================
 *
 * @Description:
 * @CreatedBy: Sundy on 2025-03-04
 * ============================================================
 */
@Component
@Slf4j
public class ChannelFieldsStep implements InitStep<ChannelStepContext> {
    @Resource
    private DescUtil descUtil;
    @Resource
    private DescribeEnhancer describeEnhancer;
    @Autowired
    private IObjectDescribeService iObjectDescribeService;

    @Override
    public boolean execute(ChannelStepContext channelStepContext) {
        User user = channelStepContext.getUser();
        if (PARTNER_OBJ.equals(channelStepContext.getRelatedObjectApiName())) {
            try {
                addSelfFieldSelect(user, PARTNER_OBJ);
                List<IFieldDescribe> partnerFields = getPartnerFields();
                descUtil.addCustomField(channelStepContext.getUser().getTenantId(), PARTNER_OBJ, partnerFields);
            } catch (MetadataServiceException e) {
                log.error("渠道准入初始化： 添加合作伙伴自定义字段失败, tenant:{}", channelStepContext.getUser().getTenantId(), e);
            }
        } else if (ACCOUNT_OBJ.equals(channelStepContext.getRelatedObjectApiName())) {
            List<IFieldDescribe> accountFields = getAccountFields();
            try {
                addSelfFieldSelect(user, ACCOUNT_OBJ);
                descUtil.addCustomField(user.getTenantId(), ACCOUNT_OBJ, accountFields);
            } catch (MetadataServiceException e) {
                log.error("渠道准入初始化： 添加客户自定义字段失败, tenant:{}", user.getTenantId(), e);
            }
        } else {
            log.warn("Invalid related object api name: {}", channelStepContext.getRelatedObjectApiName());
        }
        return true;
    }

    private void addSelfFieldSelect(User user, String objectApiName) {
        try {
            IObjectDescribe describe = describeEnhancer.fetchObject(user, objectApiName);
            if (describe == null) {
                log.warn("addSelfFieldSelect but describe is null:{}", objectApiName);
                return;
            }
            IFieldDescribe outResources = describe.getFieldDescribe("out_resources");
            if (outResources == null) {
                log.warn("addSelfFieldSelect but out_resources is null:{}", objectApiName);
                return;
            }
            SelectOneFieldDescribe selectOneFieldDescribe = (SelectOneFieldDescribe) outResources;
            List<ISelectOption> selectOptions = selectOneFieldDescribe.getSelectOptions();
            if (CollectionUtils.isEmpty(selectOptions)) {
                selectOptions = Lists.newArrayList();
            }
            boolean anyMatch = selectOptions.stream().anyMatch(x -> "self_registration".equals(x.getValue()));
            if (anyMatch) {
                log.warn("addSelfFieldSelect but selectOptions is exits:{}", objectApiName);
                return;
            }
            SelectOption selectOption = new SelectOption("自注册", "self_registration", "common.option.self.registration");
            selectOptions.add(selectOption);
            selectOneFieldDescribe.setSelectOptions(selectOptions);
            iObjectDescribeService.updateFieldDescribe(describe, Lists.newArrayList(selectOneFieldDescribe));
        } catch (Exception e) {
            log.error("addSelfFieldSelect error, tenant:{}, objectApiName:{}", user.getTenantId(), objectApiName, e);
        }

    }

    @Override
    public String getTaskName() {
        return CHANNEL_ACCESS;
    }

    @Override
    public int getOrder() {
        return 6;
    }

    @Override
    public boolean allowFailure() {
        return true;
    }

    private List<IFieldDescribe> getAccountFields() {
        List<IFieldDescribe> fields = buildCommonFields();
        DateFieldDescribe dateField = DateFieldDescribeBuilder.builder()
                .apiName("acc_expired_time")
                .index(true)
                .unique(false)
                .label("互联账号到期时间")
                .required(false)
                .build();
        fields.add(dateField);
        return fields;
    }

    private List<IFieldDescribe> buildCommonFields() {
        List<IFieldDescribe> fields = Lists.newArrayList();
        FileAttachmentFieldDescribe attachmentFieldDescribe = FileAttachmentFieldDescribeBuilder.builder()
                .apiName("agreement_attachment")
                .fileAmountLimit(1)
                .label("协议附件")
                .fileSizeLimit(*********)
                .build();
        fields.add(attachmentFieldDescribe);
        ImageFieldDescribe businessLicense = ImageFieldDescribeBuilder.builder()
                .label("工商营业执照")
                .apiName("business_license")
                .required(false)
                .fileAmountLimit(1)
                .helpText("提供高清扫描件或复印件并加盖公章")
                .build();
        fields.add(businessLicense);
        ImageFieldDescribe identityCards = ImageFieldDescribeBuilder.builder()
                .label("法人身份证")
                .apiName("identity_cards")
                .required(false)
                .fileAmountLimit(2)
                .build();
        fields.add(identityCards);
        SelectOneFieldDescribe selectOneFieldDescribe = buildSigningStatusField();
        fields.add(selectOneFieldDescribe);
        return fields;
    }

    private List<IFieldDescribe> getPartnerFields() {
        List<IFieldDescribe> fields = Lists.newArrayList();

        TextFieldDescribe applicantName = TextFieldDescribeBuilder.builder()
                .maxLength(200)
                .apiName("applicant_name")
                .required(false)
                .unique(false)
                .label("姓名")
                .build();
        fields.add(applicantName);

        LongTextFieldDescribe businessScope = LongTextFieldDescribeBuilder.builder()
                .maxLength(5000)
                .apiName("business_scope")
                .required(false)
                .label("经营范围")
                .build();
        fields.add(businessScope);

        LongTextFieldDescribe pastSuccess = LongTextFieldDescribeBuilder.builder()
                .maxLength(5000)
                .apiName("past_success")
                .required(false)
                .label("过往成功案例")
                .build();
        fields.add(pastSuccess);

        LongTextFieldDescribe coDescribe = LongTextFieldDescribeBuilder.builder()
                .maxLength(5000)
                .apiName("co_description")
                .required(false)
                .label("合作描述")
                .build();
        fields.add(coDescribe);

        CountryFieldDescribe country = CountryFieldDescribeBuilder.builder()
                .label("意向-国家")
                .apiName("interest_country")
                .build();
        fields.add(country);


        ProvinceFieldDescribe province = ProvinceFieldDescribeBuilder.builder()
                .label("意向-省")
                .apiName("interest_province")
                .cascadeParentApiName("interest_country")
                .build();
        fields.add(province);

        CityFiledDescribe city = CityFieldDescribeBuilder.builder()
                .label("意向-市")
                .apiName("interest_city")
                .cascadeParentApiName("interest_province")
                .build();
        fields.add(city);

        DistrictFieldDescribe district = DistrictFieldDescribeBuilder.builder()
                .label("意向-区")
                .apiName("interest_district")
                .cascadeParentApiName("interest_city")
                .build();
        fields.add(district);

        LocationFieldDescribe location = LocationFieldDescribeBuilder.builder()
                .label("意向-定位")
                .apiName("interest_location")
                .build();
        fields.add(location);

        TextFieldDescribe address = TextFieldDescribeBuilder.builder()
                .label("意向-详细地址")
                .apiName("interest_address")
                .required(false)
                .unique(false)
                .maxLength(500)
                .usedIn("component")
                .build();
        fields.add(address);

        AreaFieldDescribe area = AreaFieldDescribeBuilder.builder()
                .apiName("interest_areas")
                .label("意向代理区域/城市")
                .areaCountry("interest_country")
                .areaLocation("interest_location")
                .areaDetailAddress("interest_address")
                .areaCity("interest_city")
                .areaProvince("interest_province")
                .areaDistrict("interest_district")
                .build();
        fields.add(area);

        DateFieldDescribe dateField = DateFieldDescribeBuilder.builder()
                .apiName("expired_time")
                .index(true)
                .unique(false)
                .label("到期时间")
                .required(false)
                .build();
        fields.add(dateField);
        fields.addAll(buildCommonFields());
        return fields;
    }

    private SelectOneFieldDescribe buildSigningStatusField() {
        SelectOption pendingSignature = new SelectOption();
        pendingSignature.setLabel("待签约");
        pendingSignature.setValue("pending_signature");

        SelectOption signed = new SelectOption();
        signed.setLabel("已签约");
        signed.setValue("signed");

        SelectOption reject = new SelectOption();
        reject.setLabel("已驳回");
        reject.setValue("reject");

        SelectOption invalid = new SelectOption();
        invalid.setLabel("已过期");
        invalid.setValue("invalid");

        SelectOption renewal = new SelectOption();
        renewal.setLabel("已续签");
        renewal.setValue("renewal");

        SelectOption pendingRenewal = new SelectOption();
        pendingRenewal.setLabel("已续签");
        pendingRenewal.setValue("pending_renewal");

        List<ISelectOption> selectOptions = Lists.newArrayList(pendingSignature, signed, reject, invalid, renewal, pendingRenewal);

        return SelectOneFieldDescribeBuilder.builder()
                .label("协议签约状态")
                .required(false)
                .apiName("signing_status")
                .defaultValud("pending_signature")
                .selectOptions(selectOptions)
                .build();
    }
}
