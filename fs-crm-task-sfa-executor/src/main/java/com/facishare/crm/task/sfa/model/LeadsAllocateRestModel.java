package com.facishare.crm.task.sfa.model;

import lombok.Builder;
import lombok.Data;

import java.util.List;

public interface LeadsAllocateRestModel {
    @Data
    @Builder
    class Arg{
        String ownerId;
        List<String> objectIDs;
        String objectPoolId;
        Integer allocateOperationSource;
        Integer isAutoAllocate;
    }
    @Data
    class Result{
        int errCode;
        String errMessage;
    }
}
