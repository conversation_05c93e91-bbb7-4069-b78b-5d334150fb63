package com.facishare.crm.task.sfa.loyalty.task.mq;

import com.alibaba.fastjson.JSONObject;
import com.facishare.crm.sfa.lto.loyalty.model.Loyalty;
import com.facishare.crm.task.sfa.loyalty.service.LoyaltyOrgService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Service
public class OrgWarningHandler extends LoyaltyMqHandler {

    @Resource
    LoyaltyOrgService loyaltyOrgService;

    @Override
    public Tag getTag() {
        return Tag.org;
    }

    @Override
    public void handler(String body) {
        Loyalty.OrgTask orgTask = JSONObject.parseObject(body, Loyalty.OrgTask.class);
        loyaltyOrgService.syncUpdateOrg(orgTask);
    }
}
