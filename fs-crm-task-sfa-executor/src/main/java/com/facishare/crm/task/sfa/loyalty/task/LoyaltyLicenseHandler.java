package com.facishare.crm.task.sfa.loyalty.task;

import com.beust.jcommander.internal.Lists;
import com.facishare.crm.task.sfa.model.LicenseModel;
import com.facishare.crm.task.sfa.services.PartnerLoyaltyStatsService;
import com.facishare.crm.task.sfa.services.license.handler.AbstractLicenseHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

@Slf4j
@Component
public class LoyaltyLicenseHandler extends AbstractLicenseHandler {

    @Resource
    private PartnerLoyaltyStatsService partnerLoyaltyStatsService;

    private static final String LICENSE = "member_loyalty_app";

    @Override
    public List<String> moduleCodes() {
        return Lists.newArrayList(LICENSE);
    }

    @Override
    public void execute(LicenseModel.LicenseMessage message) {
        String tenantId = message.getTenantId();
        log.info("LoyaltyLicenseHandler execute tenantId:{}", tenantId);
        partnerLoyaltyStatsService.upsertPartnerLoyaltyStatsJob(tenantId);
    }

}
