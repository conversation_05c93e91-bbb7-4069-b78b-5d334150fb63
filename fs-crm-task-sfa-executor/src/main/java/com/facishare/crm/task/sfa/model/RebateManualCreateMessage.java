package com.facishare.crm.task.sfa.model;

import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class RebateManualCreateMessage {

    private String tenantId;
    private String userId;
    /**
     * 返利政策ID
     */
    private String rebatePolicyId;
    private String accountId;
    private String objectApiName;
    private String objectId;
    private boolean sendMsg;
    /**
     * 请求唯一ID
     */
    private String requestId;
    /**
     * 临时计算的对象数据
     */
    private ObjectDataDocument objectData;

    /**
     * 忠诚度事件支持
     */
    private String eventId;
    /**
     * 忠诚度事件 结果Id
     */
    private String resultId;

}