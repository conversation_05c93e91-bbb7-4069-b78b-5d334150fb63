package com.facishare.crm.task.sfa.services.channel.model;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * ============================================================
 *
 * @Description:
 * @CreatedBy: Sundy on 2025-05-20
 * ============================================================
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class SignMessage extends BaseMessage {
    private String objectApiName;
    private String dataId;
    private String approvalStatus;
    private String app;
    private String signSchemeId;

    public boolean isPass() {
        return "pass".equals(this.approvalStatus);
    }
}
