package com.facishare.crm.task.sfa.follow.util;

import com.fxiaoke.release.FsGrayRelease;
import com.fxiaoke.release.FsGrayReleaseBiz;
import org.springframework.stereotype.Component;

/**
 * @Description
 * <AUTHOR>
 * @Date 2022/2/7 19:42
 */
@Component
public class FollowGrayUtils {

    private static final FsGrayReleaseBiz gray = FsGrayRelease.getInstance("sfa-follow");


    /**
     * 跟进后计算到期时间白名单
     * @param tenantId
     * @return
     */
    public boolean isRecalculateVIP(String tenantId){
        return gray.isAllow("recalculate-vip",tenantId);
    }

    public boolean isFollowSettingCacheGray(String tenantId){
        return gray.isAllow("follow_setting_type_cache",tenantId);
    }


    public boolean isFollowSettingGray(String tenantId){
        return gray.isAllow("follow_setting_type",tenantId);
    }

    /**
     * 线索自动分配发送通知灰度
     * @param tenantId
     * @return
     */
    public  Boolean isAutoAllocateImprovedSendNotice(String tenantId) {
        return gray.isAllow("auto_allocate_improved_send_notice", tenantId);
    }

    /**
     *  线索自动分配异步调用分配逻辑
     */
    public Boolean isAutoAllocateImprovedAsync(String tenantId) {
        return gray.isAllow("auto_allocate_improved_async", tenantId);
    }


    /**
     *  线索自动分配跳过负责人已经存在的情况
     */
    public Boolean isAutoAllocateSkipOwnerExist(String tenantId){
        return gray.isAllow("auto_allocate_skip_owner_exist", tenantId);
    }
}
