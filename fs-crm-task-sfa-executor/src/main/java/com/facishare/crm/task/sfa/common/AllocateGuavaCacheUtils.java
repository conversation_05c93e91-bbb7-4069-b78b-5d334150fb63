package com.facishare.crm.task.sfa.common;

import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.concurrent.TimeUnit;

/**
 * @Description
 * <AUTHOR>
 * @Date 2021/3/8 14:15
 */

@Slf4j
@Component
public class AllocateGuavaCacheUtils {


    private Cache<String, Boolean> cache;


    @PostConstruct
    private void initCache(){
        cache = CacheBuilder.newBuilder()
                .initialCapacity(4096)
                .expireAfterWrite(10, TimeUnit.SECONDS)
                .removalListener(x->{
                    log.info("{} is removal",x.getKey());
                })
                .build();

    }

    public void putCache(String key,Boolean value){
        cache.put(key,value);
    }

    public Boolean hitCache(String key){
        if (cache.getIfPresent(key) == null){
            putCache(key,Boolean.TRUE);
            return false;
        }
        return cache.getIfPresent(key);
    }
}
