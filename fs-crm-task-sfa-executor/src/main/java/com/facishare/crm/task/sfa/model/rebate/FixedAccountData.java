package com.facishare.crm.task.sfa.model.rebate;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;

@Data
public class FixedAccountData implements Serializable {
    @JSONField(name = "account_id")
    @JsonProperty("account_id")
    private String accountId;
    @JSONField(name = "account_id__r")
    @JsonProperty("account_id__r")
    private String accountName;
    @JSONField(name = "is_delete")
    @JsonProperty("is_delete")
    private int isDelete;
}
