package com.facishare.crm.task.sfa.model;

import com.facishare.crm.sfa.audit.log.EntityConverter;
import com.facishare.crm.sfa.audit.log.model.AuditArg;

/**
 * <AUTHOR> gongchunru
 * @date : 2024/1/23 10:21
 * @description:
 */
public class LeadsAutoAllocateMessageConvert implements EntityConverter<LeadsAutoAllocateMessage> {

    @Override
    public AuditArg convert(LeadsAutoAllocateMessage leadsAutoAllocateMessage) {
        return AuditArg.builder()
                .objectIds(leadsAutoAllocateMessage.getObjectId())
                .ei(leadsAutoAllocateMessage.getTenantId())
                .extra(leadsAutoAllocateMessage.getLeadsPoolId())
                .objectApiName("LeadsObj")
                .build();
    }
}
