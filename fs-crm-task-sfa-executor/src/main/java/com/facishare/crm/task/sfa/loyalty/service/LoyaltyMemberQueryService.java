package com.facishare.crm.task.sfa.loyalty.service;

import com.facishare.crm.sfa.lto.loyalty.constants.LoyaltyConstants;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.IObjectData;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;

@Service
public class LoyaltyMemberQueryService {

    @Resource
    ServiceFacade serviceFacade;

    public String getParentIdByMemberId(String tenantId, String memberId) {
        if (StringUtils.isEmpty(tenantId) || StringUtils.isEmpty(memberId)) {
            return null;
        }
        IObjectData member = serviceFacade.findObjectDataIgnoreAll(User.systemUser(tenantId), memberId, LoyaltyConstants.LoyaltyMember.API_NAME);
        if (member == null) {
            return null;
        }
        return member.get("parent_id", String.class);
    }
}
