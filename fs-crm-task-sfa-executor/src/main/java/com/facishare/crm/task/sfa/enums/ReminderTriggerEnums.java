package com.facishare.crm.task.sfa.enums;

import cn.hutool.core.exceptions.ValidateException;
import com.facishare.crm.sfa.prm.api.enums.SignStatus;
import com.facishare.paas.I18N;
import lombok.Getter;

import static com.facishare.crm.task.sfa.util.constant.I18nKeyConstants.PRM_PARAM_PROTOCOL_ERROR;

/**
 * Created by Sundy on 2024/10/20 23:16
 */
@Getter
public enum ReminderTriggerEnums {
    MANUAL("manual"),
    AUTO("auto");
    private final String trigger;

    ReminderTriggerEnums(String trigger) {
        this.trigger = trigger;
    }

    public static ReminderTriggerEnums from(String trigger) throws ValidateException {
        for (ReminderTriggerEnums e : values()) {
            if (e.trigger.equals(trigger)) {
                return e;
            }
        }
        throw new ValidateException(I18N.text(PRM_PARAM_PROTOCOL_ERROR, "reminder_trigger"));
    }

    public static ReminderTriggerEnums find(String trigger) {
        return find(trigger, null);
    }

    public static ReminderTriggerEnums find(String trigger, ReminderTriggerEnums defaultValue) {
        for (ReminderTriggerEnums e : values()) {
            if (e.trigger.equals(trigger)) {
                return e;
            }
        }
        return defaultValue;
    }
}
