package com.facishare.crm.task.sfa.services;

import com.facishare.crm.sfa.prm.platform.model.ContextMQ;
import com.facishare.crm.sfa.prm.platform.utils.ObjectUtils;
import com.facishare.crm.sfa.prm.platform.utils.TraceGenerator;
import com.facishare.crm.task.sfa.model.ApiCallLogRequest;
import com.facishare.paas.appframework.core.model.User;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * ============================================================
 *
 * @Description:
 * @CreatedBy: Sundy on 2025-06-13
 * ============================================================
 */
@Service
@Slf4j
public class ApiCallLogService {
    @Resource
    private ApiCallLogProxy apiCallLogProxy;

    public void callLog(String body) {
        try {
            TraceGenerator.generator();
            ContextMQ<ApiCallLogRequest> contextMQ = ObjectUtils.convertObject(body, ContextMQ.class);
            if (contextMQ == null) {
                log.warn("Third party call log is empty");
                return;
            }
            User systemUser = new User(contextMQ.getTenantId(), contextMQ.getUserId());
            apiCallLogProxy.saveLog(systemUser, contextMQ.getPayload());
        } catch (Exception e) {
            log.error(e.getMessage());
        }
    }
}
