package com.facishare.crm.task.sfa.services.channel;

import com.facishare.crm.sfa.audit.log.SFAAuditLog;
import com.facishare.crm.sfa.audit.log.context.SFALogContext;
import com.facishare.crm.task.sfa.services.channel.core.ChannelLogConvert;
import com.facishare.crm.task.sfa.services.channel.enums.RouterType;
import com.facishare.crm.task.sfa.services.channel.exception.ChannelFlowException;
import com.facishare.crm.task.sfa.services.channel.exception.ItemNullException;
import com.facishare.crm.task.sfa.services.channel.utils.StepRecorder;
import com.facishare.crm.task.sfa.services.loyalty.dto.LoyaltyExecParam;
import com.facishare.crm.task.sfa.services.loyalty.dto.LoyaltyExecParamConvert;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * ============================================================
 *
 * @Description:
 * @CreatedBy: Sundy on 2025-05-20
 * ============================================================
 */
@Service
@Slf4j
public class ChannelEventRouter {

    private final Map<RouterType, ActionExecutor> routerEventRouteMapping = new HashMap<>();


    @Autowired
    public ChannelEventRouter(List<ActionExecutor> actionExecutors) {
        for (ActionExecutor router : actionExecutors) {
            routerEventRouteMapping.put(router.getRouterType(), router);
        }
    }

    @SFAAuditLog(bizName = "Channel", entityClass = RouterType.class, convertClass = ChannelLogConvert.class,
            message = "#body", status = "#status", extra = "#reason", extra1 = "#steps", extra2 = "#exception")
    public void route(RouterType routerType, String body) {
        ActionExecutor router = Optional.ofNullable(routerEventRouteMapping.get(routerType))
                .orElseThrow(() -> new IllegalArgumentException("Invalid routerType: " + routerType));
        try {
            StepRecorder.record("路由开始");
            log.info("ChannelEventRouter#route routerType: {}, body: {}", routerType, body);
            router.execute(body);
            SFALogContext.putVariable("status", "success");
            StepRecorder.record("路由结束");
        } catch (ItemNullException ine) {
            log.warn("ChannelEventRouter#route [ItemNullException], routerType: {}, body: {}", routerType, body, ine);
            SFALogContext.putVariable("reason", ine.getMessage());
            SFALogContext.putVariable("status", "failed");
            SFALogContext.putVariable("exception", "ItemNullException");
        } catch (ChannelFlowException cfe) {
            log.warn("ChannelEventRouter#route [ChannelFlowException], routerType: {}, body: {}", routerType, body, cfe);
            SFALogContext.putVariable("reason", cfe.getMessage());
            SFALogContext.putVariable("status", "failed");
            SFALogContext.putVariable("exception", "ChannelFlowException");
        } catch (Exception e) {
            log.warn("ChannelEventRouter#route [Exception], routerType: {}, body: {}", routerType, body, e);
            String reason = Optional.of(e).map(Exception::getClass).map(Class::getName).orElse("null");
            SFALogContext.putVariable("reason", reason);
            SFALogContext.putVariable("status", "failed");
            SFALogContext.putVariable("exception", "Exception");
        } finally {
            SFALogContext.putVariable("steps", StepRecorder.formatAndClearSteps());
        }
    }
}
