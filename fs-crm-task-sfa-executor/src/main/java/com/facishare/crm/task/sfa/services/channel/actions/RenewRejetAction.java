package com.facishare.crm.task.sfa.services.channel.actions;

import com.facishare.crm.sfa.prm.api.channel.AgreementStatusRecordService;
import com.facishare.crm.sfa.prm.api.enums.SignStatus;
import com.facishare.crm.sfa.prm.platform.infrastructure.statemachine.core.StateAction;
import com.facishare.crm.sfa.prm.platform.utils.DataUtils;
import com.facishare.crm.task.sfa.services.MetadataServiceExt;
import com.facishare.crm.task.sfa.services.channel.core.ChannelAssertValidator;
import com.facishare.crm.task.sfa.services.channel.enums.ChannelEvent;
import com.facishare.crm.task.sfa.services.channel.model.ChannelContext;
import com.facishare.crm.task.sfa.services.channel.model.RenewMessage;
import com.facishare.crm.task.sfa.services.channel.utils.StepRecorder;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.IObjectData;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

import static com.facishare.crm.sfa.prm.core.constants.ChannelConstants.SIGNING_STATUS;
import static com.facishare.crm.task.sfa.common.util.I18NKeyUtil.SFA_CHANNEL_RENEWAL_SUCCESSFUL_TITLE;

/**
 * ============================================================
 *
 * @Description:
 * @CreatedBy: Sundy on 2025-05-23
 * ============================================================
 */
@Component
@Slf4j
public class RenewRejetAction implements StateAction<SignStatus, ChannelEvent, ChannelContext> {
    @Resource
    private MetadataServiceExt metadataServiceExt;
    @Resource
    private AgreementStatusRecordService agreementStatusRecordService;
    @Resource
    private ChannelAssertValidator channelAssertValidator;

    @Override
    public void execute(SignStatus sourceState, ChannelEvent event, ChannelContext context) {
        log.info("RenewRejetAction#execute Renew Rejet, sourceState: {}, event: {}, context:{}", sourceState, event, context);
        StepRecorder.record("RenewRejetAction#execute");
        RenewMessage renewMessage = (RenewMessage) context.getPayload();
        User user = context.getUser();
        // 将签约状态改为拒绝
        IObjectData objectData = channelAssertValidator.assertValidData(user, renewMessage.getObjectApiName(), renewMessage.getObjectDataId());
        String value = DataUtils.getValue(objectData, SIGNING_STATUS, String.class, null);
        log.info("RenewRejetAction#execute, changeSignStatusWithRecord objectData singingStatus:{}", value);
        agreementStatusRecordService.changeSignStatusWithRecord(user, I18N.text(SFA_CHANNEL_RENEWAL_SUCCESSFUL_TITLE), renewMessage.getObjectApiName(), objectData, SignStatus.REJECT);
        StepRecorder.record("changeSignStatusWithRecord");
    }
}
