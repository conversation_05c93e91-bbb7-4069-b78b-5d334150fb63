package com.facishare.crm.task.sfa.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import lombok.Builder;
import lombok.Data;

import java.util.List;

@Data
public class FundAccountModel {
    public static final String ACCOUNT_TRANSACTION_FLOW = "AccountTransactionFlowObj";
    public static final String AUTHORIZED_TYPE = "outcome";
    public static final String FUND_ACCOUNT_OBJ = "FundAccountObj";
    //  接入模块	 2：返利
    public static final String ACCESS_MODULE = "access_module";
    public static final String ACCESS_MODULE_REBATE = "2";
    /**
     * 账户类型
     * 1：金额   2：货补金额  3：货补数量
     */
    public static final String ACCOUNT_TYPE = "account_type";
    public static final String ACCOUNT_TYPE_MONEY = "1";
    public static final String ACCOUNT_TYPE_PRODUCT = "2";
    public static final String ACCOUNT_TYPE_PRODUCT_NUM = "3";

    @Data
    @Builder
    public static class Arg {
        private String authorizedObjectApiName;
        private String authorizedType;
        private String authorizedObjectDataId;
    }

    @Data
    public static class AuthorizeResult {
        private Integer errCode;
        private String errMessage;
        private Result result;
    }

    @Data
    public static class Result {
        private List<AuthorizeData> datas;
    }

    @Data
    public static class AuthorizeData {
        @SerializedName("trade_amount_fieldapiname")
        @JsonProperty("trade_amount_fieldapiname")
        private String tradeAmountFieldapiname;
        @SerializedName("authorize_account_id")
        @JsonProperty("authorize_account_id")
        private String authorizeAccountId;
        /**
         * 1：预设账户  2：用户自定义账户
         */
        private String type;
        /**
         * 1：金额   2：货补金额  3：货补数量
         */
        @SerializedName("account_type")
        @JsonProperty("account_type")
        private String accountType;
        /**
         * 1：默认         2：返利
         */
        @SerializedName("access_module")
        @JsonProperty("access_module")
        private String accessModule;
    }

}