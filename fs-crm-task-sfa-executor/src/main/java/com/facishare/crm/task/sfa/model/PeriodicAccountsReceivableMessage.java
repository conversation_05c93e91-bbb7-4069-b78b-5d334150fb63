package com.facishare.crm.task.sfa.model;

import lombok.*;

import java.util.Map;

@Data
@AllArgsConstructor
@NoArgsConstructor
@ToString
@Builder
public class PeriodicAccountsReceivableMessage {
    private String tenantId;
    private String masterDescribeApiName;
    private String detailDescribeApiName;
    private String detailDataId;
    private String executeType;
    private String executeTime;
    private Map<String, Object> modifyObjectData;
}
