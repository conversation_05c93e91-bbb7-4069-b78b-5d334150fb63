package com.facishare.crm.task.sfa.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import lombok.*;

/**
 * @描述说明：
 * @作者：chench
 * @创建日期：2025-04-27
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@ToString
@Builder
public class SaleContractGoalModel {
    @JsonProperty("tenant_id")
    @SerializedName("tenant_id")
    private String tenantId;
    @JsonProperty("rule_goal_id")
    @SerializedName("rule_goal_id")
    private String ruleGoalId;
    @JsonProperty("contract_id")
    @SerializedName("contract_id")
    private String contractId;
    @JsonProperty("to_check_time")
    @SerializedName("to_check_time")
    private Long toCheckTime;
    @JsonProperty("check_point_id")
    @SerializedName("check_point_id")
    private String checkPointId;
}
