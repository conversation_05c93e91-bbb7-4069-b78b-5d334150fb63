package com.facishare.crm.task.sfa.address.mq;

import com.alibaba.fastjson.JSON;
import org.apache.rocketmq.common.message.MessageExt;
import com.facishare.crm.task.sfa.model.AccountToAddressMessage;
import com.facishare.crm.task.sfa.services.AccountToAddressService;
import com.facishare.paas.appframework.common.mq.RocketMQMessageListener;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import lombok.extern.slf4j.Slf4j;

import javax.annotation.Resource;
import java.nio.charset.Charset;
import java.util.List;

/**
 * <AUTHOR>
 * 执行器接收MQ入口
 */
@Slf4j
public class AccountToAddrForOpenApiListener implements RocketMQMessageListener {

    @Resource
    private AccountToAddressService accountToAddressService;

    @Override
    public void consumeMessage(List<MessageExt> list) {
        if (CollectionUtils.empty(list)) {
            return;
        }

        for (MessageExt message : list) {
            log.debug("message:{}", message);
            consumeMessage(message);
        }
    }

    private void consumeMessage(MessageExt message) {
        String messageBody = new String(message.getBody(), Charset.forName("UTF-8"));
        AccountToAddressMessage accountToAddressMessage = JSON.parseObject(messageBody, AccountToAddressMessage.class);
        accountToAddressService.execute(accountToAddressMessage, "mt_rest");
    }
}
