package com.facishare.crm.task.sfa.enums;

import com.facishare.paas.I18N;
import com.facishare.paas.appframework.core.exception.ValidateException;
import lombok.Getter;

import static com.facishare.crm.sfa.prm.core.constants.PrmI18NConstants.PRM_ENUM_PARAM_TYPE_ERROR;

/**
 * ============================================================
 *
 * @Description:
 * @CreatedBy: Sundy on 2025-03-04
 * ============================================================
 */
@Getter
public enum AgreementSignDateMode {
    BY_DATE("by_date"),
    CUSTOM("custom"),
    BY_DATE_CYCLE("by_date_cycle");

    private final String mode;

    AgreementSignDateMode(String mode) {
        this.mode = mode;
    }

    public static AgreementSignDateMode from(String mode) {
        for (AgreementSignDateMode e : values()) {
            if (e.mode.equals(mode)) {
                return e;
            }
        }
        throw new ValidateException(I18N.text(PRM_ENUM_PARAM_TYPE_ERROR, mode));
    }

    public static AgreementSignDateMode find(String mode) {
        return find(mode, null);
    }

    public static AgreementSignDateMode find(String mode, AgreementSignDateMode defaultValue) {
        for (AgreementSignDateMode e : values()) {
            if (e.mode.equals(mode)) {
                return e;
            }
        }

        return defaultValue;
    }
}
