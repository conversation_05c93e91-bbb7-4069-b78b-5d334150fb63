package com.facishare.crm.task.sfa.enums;

import cn.hutool.core.exceptions.ValidateException;
import com.facishare.paas.I18N;

/**
 * Created by Sundy on 2024/10/21 00:42
 */
public enum MemberTypeEnums {
    <PERSON><PERSON><PERSON>("role"),
    LINK_ENTERPRISE("link_enterprise"),
    LINK_GROUP("link_group"),
    LINK_ROLE("link_role"),
    DEPARTMENT("department"),
    GROUP("group"),
    PERSON("person");

    private final String type;

    MemberTypeEnums(String type) {
        this.type = type;
    }

    public static MemberTypeEnums of(String trigger) {
        for (MemberTypeEnums e : values()) {
            if (e.type.equals(trigger)) {
                return e;
            }
        }
        throw new ValidateException(I18N.text("parse MemberTypeEnums error"));
    }
}
