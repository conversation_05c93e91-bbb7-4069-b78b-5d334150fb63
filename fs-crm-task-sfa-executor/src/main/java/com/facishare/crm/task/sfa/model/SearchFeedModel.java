package com.facishare.crm.task.sfa.model;

import lombok.Builder;
import lombok.Data;

/**
 * <AUTHOR> gongchunru
 * @date : 2023/12/12 11:35
 * @description:
 */
public interface SearchFeedModel {


    @Data
    @Builder
    class Arg{
        private String resourceId;
        private Integer limit;
        private SearchArg searchArg;
    }

    //{"resourceId":"AccountObj|6572c001841ae600013f2cb1","limit":10,"searchArg":{"feedType":0,"subType":0,"startTime":**********,"endTime":**********,"senderId":"","keyword":""}}
    @Data
    @Builder
    class SearchArg{
        private Integer feedType;
        private Integer subType;
        private Long startTime;
        private Long endTime;
        private String senderId;
        private String keyword;
    }

}
