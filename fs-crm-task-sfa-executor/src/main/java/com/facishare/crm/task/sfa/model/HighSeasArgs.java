package com.facishare.crm.task.sfa.model;

import lombok.Builder;
import lombok.Data;

import java.util.List;

public interface HighSeasArgs {
    @Data
    @Builder
    class BatchSaveHighSeasClaimLogArg {
        List<HighSeasClaimLog> data;
    }

    @Data
    @Builder
    class HighSeasClaimLog {
        String highSeasId;
        String accountId;
        String employeeId;
        Long claimTime;
    }

    @Data
    @Builder
    class ProcessPoolFieldPermissionArg {
        PoolFieldPermission data;
    }

    @Data
    @Builder
    class PoolFieldPermission {
        String objectApiName;
        String op;
        List<String> fieldNameList;
    }
}