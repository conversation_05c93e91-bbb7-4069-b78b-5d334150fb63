package com.facishare.crm.task.sfa.services.channel;

import com.facishare.crm.sfa.prm.platform.utils.TraceGenerator;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

/**
 * ============================================================
 *
 * @Description:
 * @CreatedBy: Sundy on 2025-05-20
 * ============================================================
 */
@Order(1)
@Aspect
@Component
@Slf4j
public class ChannelAOP {
    @Pointcut("execution(* com.facishare.crm.task.sfa.services.channel.ChannelEventRouter.*(..))")
    private void tracePointcut() {
    }

    @Before("tracePointcut()")
    public void authCheck() {
        TraceGenerator.generator();
    }
}
