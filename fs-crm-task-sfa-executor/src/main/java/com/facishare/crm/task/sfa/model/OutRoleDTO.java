package com.facishare.crm.task.sfa.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * ============================================================
 *
 * @Description:
 * @CreatedBy: Sundy on 2025-03-03
 * ============================================================
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class OutRoleDTO {
    private String objectApiName;
    private String dataId;
    private List<String> activateRoles;
    private String app;
}
