package com.facishare.crm.task.sfa.model.loyalty;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 发放积分记录
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MemberRecord {
    private String tenantId;
    private String id;
    private String name;
    private String policyId;
    private String memberId;
    private String eventId;
    private Long total;
    private String isActive;
    private String uniqueCode;
    /**
     * 总共汇总发放的积分
     */
    private Long totalSum;

    /**
     * 总共发放次数
     */
    private Long totalCount;

}
