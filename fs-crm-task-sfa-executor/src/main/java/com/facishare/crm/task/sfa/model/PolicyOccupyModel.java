package com.facishare.crm.task.sfa.model;

import java.util.List;
import java.util.Map;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PolicyOccupyModel {

    private Map<String, List<PolicyOccupyData>> useMap;
    private List<String> orderIds;
    private String type;
    /**
     * 审批流操作状态
     */
    private String approvalStatus;
    private String tenantId;
    private String userId;
}
