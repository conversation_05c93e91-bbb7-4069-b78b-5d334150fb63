package com.facishare.crm.task.sfa.services.channel.actions;

import com.facishare.crm.sfa.prm.api.channel.AgreementStatusRecordService;
import com.facishare.crm.sfa.prm.api.enums.SignStatus;
import com.facishare.crm.sfa.prm.platform.infrastructure.statemachine.core.StateAction;
import com.facishare.crm.task.sfa.model.ChannelModel;
import com.facishare.crm.task.sfa.services.*;
import com.facishare.crm.task.sfa.services.channel.core.ChannelAssertValidator;
import com.facishare.crm.task.sfa.services.channel.exception.ItemNullException;
import com.facishare.crm.task.sfa.services.channel.model.ChannelContext;
import com.facishare.crm.task.sfa.services.channel.enums.ChannelEvent;
import com.facishare.crm.task.sfa.services.channel.model.SignMessage;
import com.facishare.crm.task.sfa.services.channel.utils.StepRecorder;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.IObjectData;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

import static com.facishare.crm.task.sfa.common.util.I18NKeyUtil.SFA_CHANNEL_RENEWAL_SIGN_REJECT;

/**
 * ============================================================
 *
 * @Description:
 * @CreatedBy: Sundy on 2025-05-20
 * ============================================================
 */
@Component
@Slf4j
public class SignRejetAction implements StateAction<SignStatus, ChannelEvent, ChannelContext> {
    @Resource
    private MetadataServiceExt metadataServiceExt;
    @Resource
    private ChannelRestService channelRestService;
    @Resource
    private ChannelSignTaskService channelSignTaskService;
    @Resource
    private AgreementStatusRecordService agreementStatusRecordService;
    @Resource
    private ChannelAgreementDetailService channelAgreementDetailService;
    @Resource
    private ChannelNotificationService channelNotificationService;
    @Resource
    private ChannelAssertValidator channelAssertValidator;

    @Override
    public void execute(SignStatus sourceState, ChannelEvent event, ChannelContext context) {
        log.info("SignRejetAction#execute Sign Rejet, sourceState: {}, event: {}, context:{}", sourceState, event, context);
        StepRecorder.record("SignRejetAction#execute");
        User user = context.getUser();
        SignMessage signMessage = (SignMessage) context.getPayload();
        IObjectData objectData = channelAssertValidator.assertValidData(user, signMessage.getObjectApiName(), signMessage.getDataId());
        ChannelModel.AggregatorMatchDTO aggregatorResult = assertValidSignScheme(user, signMessage);
        StepRecorder.record("assertValidSignScheme");
        // 审批拒绝，重新计算签约协议
        IObjectData updatedData = agreementStatusRecordService.changeSignStatusWithRecord(user, I18N.text(SFA_CHANNEL_RENEWAL_SIGN_REJECT), signMessage.getObjectApiName(), objectData, SignStatus.REJECT);
        StepRecorder.record("changeSignStatusWithRecord");
        IObjectData partnerAgreementDetailData = channelAgreementDetailService.fetchChannelAgreementDetailData(user, signMessage.getObjectApiName(), objectData.getId(), null);
        StepRecorder.record("fetchChannelAgreementDetailData");
        if (partnerAgreementDetailData != null) {
            partnerAgreementDetailData.set("sign_status", SignStatus.REJECT.getStatus());
            metadataServiceExt.bulkUpdateByFields(user, Lists.newArrayList(partnerAgreementDetailData), Lists.newArrayList("sign_status"));
            StepRecorder.record("updateChannelAgreementDetailSignStatus");
        }
        channelSignTaskService.recalculateExpireTime(user, signMessage.getObjectApiName(), updatedData);
        StepRecorder.record("recalculateExpireTime");
        channelNotificationService.sendChannelNotice(user, objectData, false, aggregatorResult.getApprovalNoticeAggregators());
        StepRecorder.record("sendChannelNotice");
    }

    private ChannelModel.AggregatorMatchDTO assertValidSignScheme(User user, SignMessage signMessage) {
        ChannelModel.AggregatorMatchDTO aggregatorResult = channelRestService.matchSignScheme(user, signMessage.getObjectApiName(), signMessage.getDataId());
        if (aggregatorResult == null) {
            log.warn("SignRejetAction#execute aggregator result not found, dataId:{}, objectApiName:{}", signMessage.getDataId(), signMessage.getObjectApiName());
            throw new ItemNullException("aggregator result not found");
        }
        return aggregatorResult;
    }
}
