package com.facishare.crm.task.sfa.model.loyalty;

import com.facishare.paas.metadata.api.IObjectData;
import com.google.common.collect.Lists;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 忠诚度政策
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class IncentivePolicy {
    private String tenantId;
    private String id;
    private String name;

    private String programId;
    private String firstCategory;
    private String secondCategory;
    private String thirdCategory;
    private String usedObjectApiName;
    private String triggerAction;
    private String activeStatus;
    private Long startTime;
    private Long endTime;
    private String calculateStatus;
    private String storeRange;
    private String orgType;
    private String orgId;
    private String pointsPoolIds;
    private String defaultPoolId;
    private Long totalPoints;
    private Long receivedTotalPoints;
    private Long unusedTotalPoints;
    private Long personPointsLimit;
    private Long personJoinLimit;

    private Boolean needPointsMagnification;
    private String remark;
    private Long lastModifiedTime;
    private int cycleCount;
    private int executeCount;
    private String cycleInfo;
    private IObjectData srcData;
    @Builder.Default
    private List<IncentivePolicyRule> rules = Lists.newArrayList();
    /**
     * 条件字段
     */
    private Map<String, Set<String>> conditionFieldMap;
    /**
     * 是否有规则条件
     */
    private Boolean hasRuleCondition;

    /**
     * 规则条件中引用的聚合规则id
     */
    private Set<String> aggRuleIds;

}
