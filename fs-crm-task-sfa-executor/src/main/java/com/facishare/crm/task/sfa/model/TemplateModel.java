package com.facishare.crm.task.sfa.model;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @time 2024-07-01 15:08
 * @Description
 */
public interface TemplateModel {
    @Data
    class Arg {
        private String templateId;
        private String objectId;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    class Result {
        private String subject;
        private String template;
        private boolean success;
        private String errorMessage;
    }
}
