package com.facishare.crm.task.sfa.license.mq;

import com.alibaba.fastjson.JSON;
import org.apache.rocketmq.common.message.MessageExt;
import com.facishare.crm.task.sfa.model.LicenseModel;
import com.facishare.crm.task.sfa.services.LicenseMQConsumeService;
import com.facishare.paas.appframework.common.mq.RocketMQMessageListener;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import lombok.extern.slf4j.Slf4j;

import javax.annotation.Resource;
import java.nio.charset.Charset;
import java.util.List;

@Slf4j
public class LicenseListener implements RocketMQMessageListener {
    @Resource
    private LicenseMQConsumeService licenseMQConsumeService;

    @Override
    public void consumeMessage(List<MessageExt> list) {
        if (CollectionUtils.empty(list)) {
            return;
        }

        for (MessageExt message : list) {
            log.info("LicenseListener message:{}", message);
            consumeMessage(message);
        }
    }

    private void consumeMessage(MessageExt message) {
        if (message.getReconsumeTimes() > 2){
            log.warn("LicenseListener message reconsumeTimes > 2, message:{}", message);
            return;
        }
        String messageBody = new String(message.getBody(), Charset.forName("UTF-8"));
        LicenseModel.LicenseMessage licenseMessage = JSON.parseObject(messageBody, LicenseModel.LicenseMessage.class);
        licenseMQConsumeService.execute(licenseMessage);
    }
}
