package com.facishare.crm.task.sfa.model.rebate;

import com.facishare.paas.metadata.api.IObjectData;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 返利产生规则
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class RebatePolicyRule {
    private String id;
    private String name;
    private String topic;
    private String rebatePolicyId;
    private String ruleType;
    private String ruleCondition;
    private String executionResult;
    private Integer priority;
    private String sourceObjectApiName;
    private String useType;
    private String functionInfo;
    private String rebateUsedDate;
    private String type;
    private String productConditionType;
    private String productRangeType;
    private String productConditionContent;
    private String productRange;
    private Long startDate;
    private Long endDate;
    private IObjectData srcData;
    private String dimension;
    private String ruleConditionPro;

    /**
     * 获取规则中引用的聚合规则id(包含规则条件中和每满条件中引用的)
     *
     * @return
     */
//    public Set<String> getAggRuleIds() {
//        List<RuleWhere> filters = Lists.newArrayList();
//        List<RuleWhere.FiltersBean> wheres = buildPriceRuleWheres(ruleCondition);
//        wheres.forEach(where -> filters.addAll(where.));
//        Set<String> aggIds = filters.stream()
//                .filter(filter -> AGGREGATE.name().equals(filter.getFieldNameType().toUpperCase()))
//                .map(Filter::getFieldName)
//                .collect(Collectors.toSet());
//        aggIds.addAll(getCycleAggRuleIds());
//        return aggIds;
//    }
//
//    private List<RuleWhere.FiltersBean> buildPriceRuleWheres(String condition) {
//        List<RuleWhere.FiltersBean> wheres = Lists.newArrayList();
//        List<String> ruleObjects = JSON.parseArray(condition, String.class);
//        if (CollectionUtils.notEmpty(ruleObjects)) {
//            ruleObjects.forEach(ruleObject -> {
//                if (!Strings.isNullOrEmpty(ruleObject)) {
//                    RuleWhere.FiltersBean where = new RuleWhere.FiltersBean();
//                    where.fromJsonString(ruleObject);
//                    where.initDefaultValue(MASTER_DETAIL_API_NAME.get(sourceObjectApiName));
//                    wheres.add(where);
//                }
//            });
//        }
//
//        return wheres;
//    }
}
