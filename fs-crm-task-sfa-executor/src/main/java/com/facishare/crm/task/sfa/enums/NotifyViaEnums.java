package com.facishare.crm.task.sfa.enums;

import com.facishare.paas.I18N;
import com.facishare.paas.appframework.core.exception.ValidateException;
import lombok.Getter;

import static com.facishare.crm.task.sfa.util.constant.I18nKeyConstants.PRM_PARAM_PROTOCOL_ERROR;

/**
 * Created by Sundy on 2024/10/21 00:17
 */
@Getter
public enum NotifyViaEnums {
    SMS("sms"),
    EMAIL("email"),
    CRM("crm"),
    QI_XIN("qi_xin"),
    PRM_ALERT("prm_alert"),
    PRM_CRM("prm_crm"),
    OFFICIAL_ACCOUNT("official_account");

    private final String via;

    NotifyViaEnums(String via) {
        this.via = via;
    }

    public static NotifyViaEnums of(String via) {
        for (NotifyViaEnums e : values()) {
            if (e.via.equals(via)) {
                return e;
            }
        }
        throw new ValidateException(I18N.text(PRM_PARAM_PROTOCOL_ERROR, "notifyVia"));
    }
}

