package com.facishare.crm.task.sfa.model.loyalty;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;

public interface MetricModel {
    @Data
    @ToString
    class AggregateInfo implements Serializable {
        @JSONField(name = "aggregate_id")
        @JsonProperty("aggregate_id")
        private String aggregateId;
        @JSONField(name = "aggregate_name")
        @JsonProperty("aggregate_name")
        private String aggregateName;
        @JSONField(name = "type")
        @JsonProperty("type")
        private String type;
    }


    @Data
    @ToString
    class AttributeInfo implements Serializable {
        @JSONField(name = "attribute_id")
        @JsonProperty("attribute_id")
        private String attributeId;
        @JSONField(name = "attribute_name")
        @JsonProperty("attribute_name")
        private String attributeName;
        @JSONField(name = "type")
        @JsonProperty("type")
        private String type;
        @J<PERSON>NField(name = "attribute_field_api_name")
        @JsonProperty("attribute_field_api_name")
        private String attributeFieldApiName;
    }


    @Data
    @ToString
    class FieldInfo implements Serializable {
        @JSONField(name = "field_api_name")
        @JsonProperty("field_api_name")
        private String fieldApiName;
        @JSONField(name = "field_label")
        @JsonProperty("field_label")
        private String fieldLabel;
        @JSONField(name = "object_api_name")
        @JsonProperty("object_api_name")
        private String objectApiName;
        @JSONField(name = "object_label")
        @JsonProperty("object_label")
        private String objectLabel;
        @JSONField(name = "type")
        @JsonProperty("type")
        private String type;
        @JSONField(name = "field_describe_api_name")
        @JsonProperty("field_describe_api_name")
        private String fieldDescribeApiName;
    }

    @Data
    @ToString
    class APLInfo implements Serializable {
        @JSONField(name = "apl_api_name")
        @JsonProperty("apl_api_name")
        private String aplApiName;
        @JSONField(name = "apl_name")
        @JsonProperty("apl_name")
        private String aplName;
        @JSONField(name = "name_space")
        @JsonProperty("name_space")
        private String nameSpace;
        @JSONField(name = "return_type")
        @JsonProperty("return_type")
        private String returnType;
        @JSONField(name = "bind_object_api_name")
        @JsonProperty("bind_object_api_name")
        private String bindObjectApiName;
        @JSONField(name = "type")
        @JsonProperty("type")
        private String type;
    }
}
