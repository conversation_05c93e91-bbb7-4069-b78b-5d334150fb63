package com.facishare.crm.task.sfa.model;

import lombok.*;

import java.util.List;
import java.util.Map;

@Data
@AllArgsConstructor
@NoArgsConstructor
@ToString
@Builder
public class LeadsAutoAllocateMessage {
    /**
     * 新增=1，退回=2，收回=3
     */
    private Integer allocateOperationSource;
    private String tenantId;
    private String objectId;
    private String leadsPoolId;
    private int batch;
}
