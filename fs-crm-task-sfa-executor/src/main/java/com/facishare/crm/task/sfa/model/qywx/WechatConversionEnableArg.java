package com.facishare.crm.task.sfa.model.qywx;

import lombok.Data;

@Data
public class WechatConversionEnableArg {
    private String ea;
    private String outEa;
    private MessageStorageArg storageLocation;

    @Data
    static class MessageStorageArg {
        private Integer salesRetentionType;  //存到销售记录 0:关闭 1:开启
        private Integer conversionObjType;   //存档会话对象 0:关闭 1:开启   有可能为null
    }
}
