package com.facishare.crm.task.sfa.services.channel;

import com.facishare.crm.sfa.prm.api.enums.SignStatus;
import com.facishare.crm.sfa.prm.platform.infrastructure.statemachine.core.StateMachine;
import com.facishare.crm.sfa.prm.platform.infrastructure.statemachine.core.StateMachineDispatcher;
import com.facishare.crm.task.sfa.services.channel.enums.ChannelEvent;
import com.facishare.crm.task.sfa.services.channel.model.ChannelContext;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;

/**
 * ============================================================
 *
 * @Description:
 * @CreatedBy: Sundy on 2025-05-22
 * ============================================================
 */
@Slf4j
@Component
public class ChannelMachine {
    public static final String STATE_MACHINE_NAME = "Channel";

    @Resource
    private StateMachineDispatcher stateMachineDispatcher;

    @Getter
    private StateMachine<SignStatus, ChannelEvent, ChannelContext> instance;

    @PostConstruct
    public void init() {
        this.instance = stateMachineDispatcher.newStateMachine(STATE_MACHINE_NAME); // 假设这是创建 B 的方法
        log.info("ChannelMachine instance initialized: {}", instance);
    }

}
