package com.facishare.crm.task.sfa.loyalty.task;

import com.facishare.crm.task.sfa.mq.integral.TenantUtils;
import com.github.autoconf.ConfigFactory;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

@Service
public class LoyaltyGrayService {

    @Resource
    TenantUtils tenantUtils;

    private static String SFA_LOYALTY_GRAY_EI;

    static {
        ConfigFactory.getInstance().getConfig("variables_fs_gray_product_vertical", config -> SFA_LOYALTY_GRAY_EI = config.get("sfa_loyalty_gray_ei", ""));
    }

    public List<String> getTenantIdListForCurrentEnv() {
        if (StringUtils.isEmpty(SFA_LOYALTY_GRAY_EI)) {
            return new ArrayList<>();
        }
        List<String> tenantIdList = Arrays.stream(SFA_LOYALTY_GRAY_EI.split("\\|")).collect(Collectors.toList());
        return tenantUtils.batchGetSimpleEnterprise(tenantIdList);
    }
}
