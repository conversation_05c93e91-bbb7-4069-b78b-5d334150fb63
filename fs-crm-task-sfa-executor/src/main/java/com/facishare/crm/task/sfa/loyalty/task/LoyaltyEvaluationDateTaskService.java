package com.facishare.crm.task.sfa.loyalty.task;

import com.facishare.crm.sfa.lto.loyalty.constants.LoyaltyConstants;
import com.facishare.crm.sfa.lto.loyalty.service.LoyaltyMemberTierService;
import com.facishare.crm.sfa.lto.loyalty.utils.LoyaltyThreshold;
import com.facishare.crm.sfa.lto.loyalty.utils.LoyaltyUtils;
import com.facishare.crm.task.sfa.util.SearchUtil;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.QueryResult;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.impl.search.OrderBy;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@Service
public class LoyaltyEvaluationDateTaskService {

    @Resource
    ServiceFacade serviceFacade;
    @Resource
    LoyaltyMemberTierService loyaltyMemberTierService;

    /**
     * 会员计划维度进行评定日计算
     */
    public List<IObjectData> evaluationDateTask(String tenantId, String programId, List<IFilter> filters, boolean forcedCalculation) {
        List<IObjectData> res = new ArrayList<>();
        IObjectData tierClassInfo = loyaltyMemberTierService.getEffectiveTierClassByProgramId(tenantId, programId);
        if (tierClassInfo == null) {
            log.info("会员计算评定日定时任务-未找到等级分类");
            return res;
        }
        Long lastModifiedTime = tierClassInfo.getLastModifiedTime();
        if (!forcedCalculation) {//非强制更新,检测规则是否近期有更新,如果没有更新,不重新计算下次评定日
            if (lastModifiedTime == null || System.currentTimeMillis() - lastModifiedTime > LoyaltyThreshold.getEvaluationDateCalculationPeriod() * 24 * 60 * 60 * 1000L) {
                log.info("会员计算评定日定时任务-近期没有更新,不执行计算.tenantId:[{}],programId:[{}],tierClassId:[{}]", tenantId, programId, tierClassInfo.getId());
                return res;
            }
        }
        //构建查询
        SearchTemplateQuery query = new SearchTemplateQuery();
        query.getFilters().addAll(filters);
        SearchUtil.fillFilterEq(query.getFilters(), IObjectData.IS_DELETED, 0);
        SearchUtil.fillFilterEq(query.getFilters(), LoyaltyConstants.LoyaltyMember.PROGRAM_ID, programId);
        query.setLimit(LoyaltyThreshold.getCommonPageSize());
        query.setOrders(Lists.newArrayList(new OrderBy(IObjectData.ID, true)));
        //判断模式
        String levelMode = tierClassInfo.get(LoyaltyConstants.LoyaltyTierClass.LEVEL_MODE, String.class);
        if ("FixedDateModel".equals(levelMode)) {
            Map<String, Object> updateFields = new HashMap<>();
            updateFields.put(LoyaltyConstants.LoyaltyMember.EVALUATION_DATE, LoyaltyUtils.evaluationDate(tierClassInfo, null));
            QueryResult<IObjectData> queryResult = serviceFacade.findBySearchQuery(User.systemUser(tenantId), LoyaltyConstants.LoyaltyMember.API_NAME, query);
            if (queryResult != null && !CollectionUtils.isEmpty(queryResult.getData())) {
                serviceFacade.batchUpdateWithMap(User.systemUser(tenantId), queryResult.getData(), updateFields);
                res = queryResult.getData();
            }
        } else {
            QueryResult<IObjectData> queryResult = serviceFacade.findBySearchQuery(User.systemUser(tenantId), LoyaltyConstants.LoyaltyMember.API_NAME, query);
            if (queryResult != null && !CollectionUtils.isEmpty(queryResult.getData())) {
                List<IObjectData> memberList = queryResult.getData();
                for (IObjectData member : memberList) {
                    long evaluationDate = LoyaltyUtils.evaluationDate(tierClassInfo, member);
                    member.set(LoyaltyConstants.LoyaltyMember.EVALUATION_DATE, evaluationDate);
                }
                serviceFacade.batchUpdateByFields(User.systemUser(tenantId), memberList, Lists.newArrayList(LoyaltyConstants.LoyaltyMember.EVALUATION_DATE));
                res = queryResult.getData();
            }
        }
        return res;
    }

}
