package com.facishare.crm.task.sfa.common;

import com.facishare.crm.task.sfa.model.qywx.QyweixinDepartmentInfo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import com.google.common.cache.*;

import javax.annotation.PostConstruct;
import java.util.concurrent.TimeUnit;

@Slf4j
@Component
public class WechatEmployeeCacheUtils {
    private Cache<String, QyweixinDepartmentInfo> cache;
    @PostConstruct
    private void initCache(){
        cache = CacheBuilder.newBuilder()
                .initialCapacity(4096)
                .expireAfterWrite(5, TimeUnit.MINUTES)
                .removalListener(x->{
                    log.info("{} is removal",x.getKey());
                })
                .build();

    }

    public void put(String key,QyweixinDepartmentInfo value){
        cache.put(key,value);
    }
    public QyweixinDepartmentInfo get(String key) {
        if (cache.getIfPresent(key) == null){
            return null;
        }
        return cache.getIfPresent(key);
    }
}
