package com.facishare.crm.task.sfa.model.qywx;

public interface FriendSyncConfigConstant {

	String CONFIG_TABLE_NAME = "biz_wechat_friend_sync_config";

	String SYNC_TO_LEADS = "sync_to_leads";
	String LEADS_SYNC_TIMING = "leads_sync_timing";
	String LEADS_SYNC_CONDITION = "leads_sync_condition";
	String SYNC_TO_ACCOUNT = "sync_to_account";
	String ACCOUNT_SYNC_TIMING = "account_sync_timing";
	String ACCOUNT_SYNC_CONDITION = "account_sync_condition";
	String SYNC_TO_CONTACT = "sync_to_contact";
	String CONTACT_SYNC_TIMING = "contact_sync_timing";
	String CONTACT_SYNC_CONDITION = "contact_sync_condition";
	String LEADS_CHECK_DUPLICATE = "leads_check_duplicate";
	String LEADS_DUPLICATE_CONFIG = "leads_duplicate_config";
	String ACCOUNT_CHECK_DUPLICATE = "account_check_duplicate";
	String ACCOUNT_DUPLICATE_CONFIG = "account_duplicate_config";
	String CONTACT_CHECK_DUPLICATE = "contact_check_duplicate";
	String CONTACT_DUPLICATE_CONFIG = "contact_duplicate_config";
	String LEADS_DUPLICATE_HANDLE_MODE = "leads_duplicate_handle_mode";
	String CONTACT_DUPLICATE_HANDLE_MODE = "contact_duplicate_handle_mode";
	String ACCOUNT_DUPLICATE_HANDLE_MODE = "account_duplicate_handle_mode";

	String TENANT_ID = "tenant_id";
	String OBJECT_DESCRIBE_API_NAME = "object_describe_api_name";
	String FRIEND_RECORD_API_NAME = "WechatFriendsRecordObj";

	String ID = "id";
	String IS_DELETED = "is_deleted";
	String CREATE_TIME= "create_time";
  	String LAST_MODIFIED_BY= "last_modified_by";
	String LAST_MODIFIED_TIME= "last_modified_time";
	String SYS_MODIFIED_TIME= "sys_modified_time";

}

