package com.facishare.crm.task.sfa.loyalty.task.mq;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.facishare.crm.sfa.lto.loyalty.model.Loyalty;
import com.facishare.crm.sfa.lto.loyalty.service.LoyaltyMemberService;
import com.facishare.crm.sfa.lto.loyalty.service.memberTierStrategy.IMemberTierStrategy;
import com.facishare.crm.sfa.lto.loyalty.utils.LoyaltyI18nKey;
import com.facishare.crm.task.sfa.common.SfaTaskRateLimiterService;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.github.trace.TraceContext;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;

@Service
public class MemberEvaluationTaskHandler extends LoyaltyMqHandler {

    @Resource
    LoyaltyMemberService loyaltyMemberService;
    @Resource
    SfaTaskRateLimiterService limiterService;

    @Override
    public Tag getTag() {
        return Tag.task;
    }

    @Override
    public void handler(String body) {
        Loyalty.MemberTask memberTask = JSONObject.parseObject(body, Loyalty.MemberTask.class);
        String traceId = TraceContext.get().getTraceId();
        String tenantId = memberTask.getTenantId();
        if (StringUtils.isEmpty(memberTask.getTraceId())) {
            memberTask.setTraceId(traceId);
        } else {
            TraceContext.get().setTraceId(memberTask.getTraceId());
        }
        limiterService.getLoyaltyMemberTaskLimiter().acquire();
        String memberId = memberTask.getMemberId();
        log.info("会员定时任务-会员执行开始.tenantId:[{}],memberId:[{}],param:{}", tenantId, memberId, JSON.toJSONString(memberTask));
        try {
            IMemberTierStrategy.Operator operator;
            if (!StringUtils.isEmpty(memberTask.getOperator())) {
                operator = IMemberTierStrategy.Operator.valueOf(memberTask.getOperator());
            } else {
                operator = IMemberTierStrategy.Operator.upgrades;
            }
            loyaltyMemberService.tryMemberLock(tenantId, memberId, (lock) -> loyaltyMemberService.unLockUpdateMemberPointsAndTier(tenantId, memberId, operator));
            log.info("会员定时任务-会员执行成功.memberId:[{}]", memberId);
        } catch (ValidateException e) {
            log.warn("会员定时任务-更新会员信息失败.", e);
            if (e.getErrorCode() == LoyaltyI18nKey.ErrorStatus.TryLockFailed.getErrorCode()) {
                //只有获取锁失败才进行重试
                throw e;
            }
        } catch (Exception e) {
            log.error("会员定时任务-更新会员信息异常.", e);
            throw e;
        }
    }

}
