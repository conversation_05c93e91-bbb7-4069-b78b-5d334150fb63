package com.facishare.crm.task.sfa.model.proxy.bi;

import lombok.Data;

import java.util.List;

public interface RptHeaderQuery {
    @Data
    class Arg {
        private String viewId;
        private String listRange;
    }

    @Data
    class Result {
        private Integer code;
        private String message;
        private ResultData data;
    }

    @Data
    class ResultData {
        private List<Header> headerList;
    }

    @Data
    class Header {
        private String fieldId;
        private String fieldName;
        private String formatStr;
        private String apiName;
        private String objectDescribeApiName;
        private String columnName;
        private String dimensionConfig;
        private String fieldType;
        private String subFieldType;
        private Boolean canShowDetail;
        private String columnKey;
    }
}