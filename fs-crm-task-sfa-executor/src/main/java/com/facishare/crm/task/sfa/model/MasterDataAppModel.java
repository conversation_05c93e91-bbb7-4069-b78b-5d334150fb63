package com.facishare.crm.task.sfa.model;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.*;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/5/28 12:36
 */
public interface MasterDataAppModel {
    @Data
    class AppConfigList {
        private List<AppConfig> configs;
    }

    @Data
    class AppConfig {
        @JSONField(name = "api_name")
        @JsonProperty("api_name")
        private String apiName;
        @J<PERSON><PERSON>ield(name = "display_name")
        @JsonProperty("display_name")
        private String displayName;
    }

    @Setter
    @Getter
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    class SyncMapping {
        private String sourceObjectApiName;
        private String targetObjectApiName;
        private String sourceDataId;
        private String targetDataId;
        private String downstreamTenantId;
        private String upstreamTenantId;
    }
}
