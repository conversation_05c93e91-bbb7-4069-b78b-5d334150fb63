package com.facishare.crm.task.sfa.enums;

import lombok.Getter;

/**
 * ============================================================
 * @IgnoreI18n
 * @Description:
 * @CreatedBy: Sundy on 2024-12-10
 * ============================================================
 */
@Getter
public enum MembershipMemberTypeEnums {
    INDIVIDUAL("individual", "个人会员"),
    CORPORATE("corporate", "企业会员");

    private final String code;
    private final String desc;

    MembershipMemberTypeEnums(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }
}
