package com.facishare.crm.task.sfa.loyalty.task.mq;

import com.alibaba.fastjson.JSONObject;
import com.facishare.crm.sfa.lto.loyalty.constants.LoyaltyConstants;
import com.facishare.crm.sfa.lto.loyalty.model.Loyalty;
import com.facishare.crm.task.sfa.loyalty.task.LoyaltyEvaluationDateTaskService;
import com.facishare.crm.task.sfa.util.SearchUtil;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.search.IFilter;
import com.google.common.collect.Lists;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
public class MemberEvaluationDateHandler extends LoyaltyMqHandler {

    @Resource
    LoyaltyEvaluationDateTaskService loyaltyEvaluationDateTaskService;
    @Resource
    ServiceFacade serviceFacade;

    @Override
    public Tag getTag() {
        return Tag.memberEvaluationDate;
    }

    /**
     * 会员维度进行计算评定日
     * 主要是导入时的批量执行，导入最大只有50条所以一次执行即可完成，无需创建下次任务
     */
    @Override
    public void handler(String body) {
        Loyalty.MemberEvaluationDate memberEvaluationDate = JSONObject.parseObject(body, Loyalty.MemberEvaluationDate.class);
        String tenantId = memberEvaluationDate.getTenantId();
        List<IObjectData> memberList = serviceFacade.findObjectDataByIds(tenantId, memberEvaluationDate.getMemberIdList(), LoyaltyConstants.LoyaltyMember.API_NAME);
        Map<String, List<String>> mapForProgramIdToMemberIdList = new HashMap<>();
        for (IObjectData member : memberList) {
            String programId = member.get(LoyaltyConstants.LoyaltyMember.PROGRAM_ID, String.class);
            List<String> memberIdList = mapForProgramIdToMemberIdList.getOrDefault(programId, Lists.newArrayList());
            memberIdList.add(member.getId());
            mapForProgramIdToMemberIdList.put(programId, memberIdList);
        }
        for (Map.Entry<String, List<String>> entry : mapForProgramIdToMemberIdList.entrySet()) {
            String programId = entry.getKey();
            List<String> memberIdList = entry.getValue();
            List<IFilter> filters = Lists.newArrayList();
            SearchUtil.fillFilterIn(filters, IObjectData.ID, memberIdList);
            loyaltyEvaluationDateTaskService.evaluationDateTask(tenantId, programId, filters, true);
        }
    }
}
