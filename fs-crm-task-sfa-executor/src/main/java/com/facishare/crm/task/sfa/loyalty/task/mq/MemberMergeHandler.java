package com.facishare.crm.task.sfa.loyalty.task.mq;


import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson2.JSON;
import com.facishare.crm.sfa.lto.loyalty.constants.LoyaltyConstants;
import com.facishare.crm.sfa.lto.loyalty.model.Loyalty;
import com.facishare.crm.sfa.lto.loyalty.service.LoyaltyMemberService;
import com.facishare.crm.sfa.lto.loyalty.service.memberTierStrategy.IMemberTierStrategy;
import com.facishare.crm.sfa.lto.loyalty.utils.LoyaltyThreshold;
import com.facishare.crm.task.sfa.common.util.CRMRecordUtil;
import com.facishare.crm.task.sfa.util.SearchUtil;
import com.facishare.paas.appframework.common.service.CRMNotificationService;
import com.facishare.paas.appframework.common.service.model.NewCrmNotification;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.log.ActionType;
import com.facishare.paas.appframework.log.EventType;
import com.facishare.paas.appframework.metadata.ObjectDataExt;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.QueryResult;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.impl.ObjectData;
import com.facishare.paas.metadata.impl.search.OrderBy;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.fxiaoke.rocketmq.producer.AutoConfMQProducer;
import com.fxiaoke.rocketmq.producer.DefaultTopicMessage;
import com.github.trace.TraceContext;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@Service
public class MemberMergeHandler extends LoyaltyMqHandler {

    @Resource
    ServiceFacade serviceFacade;
    @Resource
    LoyaltyMemberService loyaltyMemberService;
    @Resource
    CRMNotificationService crmNotificationService;
    @Resource(name = "loyaltyMq")
    AutoConfMQProducer mqProducer;

    @Override
    public Tag getTag() {
        return Tag.memberMerge;
    }

    @Override
    public void handler(String body) {
        String traceId = TraceContext.get().getTraceId();
        Loyalty.MemberMerge memberMerge = JSONObject.parseObject(body.getBytes(), Loyalty.MemberMerge.class);
        String tenantId = memberMerge.getTenantId();
        if (StringUtils.isEmpty(memberMerge.getTraceId())) {
            memberMerge.setTraceId(traceId);
        } else {
            TraceContext.get().setTraceId(memberMerge.getTraceId());
        }
        log.info("会员合并-开始.tenantId:[{}],param:{}", tenantId, JSON.toJSONString(memberMerge));
        if (memberMerge.getNo() == null) {//积分明细
            log.info("会员合并-积分明细.param:{}", JSON.toJSONString(memberMerge));
            commonHandlerForMerge(memberMerge, LoyaltyConstants.LoyaltyPointsDetail.API_NAME, LoyaltyConstants.LoyaltyPointsDetail.MEMBER_ID, 1);
        } else if (memberMerge.getNo() == 1) {//会员变动记录
            log.info("会员合并-会员变动记录.param:{}", JSON.toJSONString(memberMerge));
            commonHandlerForMerge(memberMerge, LoyaltyConstants.LoyaltyMemberChangeRecords.API_NAME, LoyaltyConstants.LoyaltyMemberChangeRecords.MEMBER_ID, 2);
        } else {
            IObjectData targetMember = new ObjectData();
            List<String> memberIdList = new ArrayList<>(memberMerge.getSouceIdList());
            memberIdList.add(memberMerge.getTargetId());
            List<IObjectData> memberList = serviceFacade.findObjectDataByIdsIgnoreAll(tenantId, memberIdList, LoyaltyConstants.LoyaltyMember.API_NAME);
            for (IObjectData member : memberList) {
                if (memberMerge.getTargetId().equals(member.getId())) {
                    Map<String, Object> updateFields = new HashMap<>();
                    updateFields.put(LoyaltyConstants.LoyaltyMember.MEMBER_STATUS, "normal");
                    loyaltyMemberService.unLockUpdateMemberPointsAndTier(tenantId, member, IMemberTierStrategy.Operator.upgrades, updateFields);
                    targetMember = member;
                } else {
                    Map<String, Object> updateFields = new HashMap<>();
                    updateFields.put(LoyaltyConstants.LoyaltyMember.MEMBER_STATUS, "merged");
                    loyaltyMemberService.unLockUpdateMemberPoints(tenantId, member, updateFields);
                }
            }
            log.info("会员合并-完成.tenantId:[{}],param:{}", tenantId, JSON.toJSONString(memberMerge));
            sendCRMRecord(tenantId, memberMerge.getOperator(), targetMember);
        }
    }

    public void sendCRMRecord(String tenantId, String operator, IObjectData targetMember) {
        if (StringUtils.isEmpty(operator)) {
            return;
        }
        int operatorId;
        try {
            operatorId = Integer.parseInt(operator);
        } catch (Exception e) {
            return;
        }
        CRMRecordUtil.sendNewCRMRecord(
                crmNotificationService,
                User.systemUser(tenantId),
                NewCrmNotification.CUSTOM_REMIND_RECORD_TYPE,
                Lists.newArrayList(operatorId),
                User.SUPPER_ADMIN_USER_ID,
                "会员合并",
                "会员[{0}]合并已完成",
                "sfa.loyalty.member.merge_record_title",
                null,
                "sfa.loyalty.member.merge_record_info",
                Lists.newArrayList(targetMember.getDisplayName()),
                CRMRecordUtil.getUrlParameter(LoyaltyConstants.LoyaltyMember.API_NAME, targetMember.getId())
        );
    }


    public void commonHandlerForMerge(Loyalty.MemberMerge memberMerge, String descApiName, String memberIdField, Integer nextNo) {
        String tenantId = memberMerge.getTenantId();
        List<String> souceIdList = memberMerge.getSouceIdList();
        SearchTemplateQuery query = new SearchTemplateQuery();
        SearchUtil.fillFilterIn(query.getFilters(), memberIdField, souceIdList);
        SearchUtil.fillFilterLTE(query.getFilters(), IObjectData.CREATE_TIME, memberMerge.getEndTime());
        SearchUtil.fillFilterGTE(query.getFilters(), IObjectData.CREATE_TIME, memberMerge.getStartTime());
        if (!StringUtils.isEmpty(memberMerge.getOffset())) {
            SearchUtil.fillFilterGT(query.getFilters(), IObjectData.ID, memberMerge.getOffset());
        }
        query.setOrders(Lists.newArrayList(new OrderBy(IObjectData.ID, true)));
        query.setLimit(LoyaltyThreshold.getCommonPageSize());
        QueryResult<IObjectData> queryResult = serviceFacade.findBySearchQueryIgnoreAll(User.systemUser(tenantId), descApiName, query);
        if (queryResult == null || CollectionUtils.isEmpty(queryResult.getData())) {
            memberMerge.setNo(nextNo);
            memberMerge.setOffset(null);
            mqProducer.send(new DefaultTopicMessage(getTag().toString(), JSON.toJSONBytes(memberMerge)));
            return;
        }
        List<IObjectData> dataList = queryResult.getData();
        IObjectData lastData = dataList.get(dataList.size() - 1);
        memberMerge.setOffset(lastData.getId());
        List<IObjectData> updateDataList = ObjectDataExt.copyList(dataList);
        for (IObjectData data : updateDataList) {
            data.set(memberIdField, memberMerge.getTargetId());
        }
        serviceFacade.batchUpdateByFields(User.systemUser(tenantId), updateDataList, Lists.newArrayList(memberIdField));
        IObjectDescribe describe = serviceFacade.findObject(tenantId, descApiName);
        serviceFacade.bulkRecordEditLog(User.builder().tenantId(tenantId).userId(memberMerge.getOperator()).build(), EventType.MODIFY, ActionType.Modify, describe, updateDataList, dataList);
        mqProducer.send(new DefaultTopicMessage(getTag().toString(), JSON.toJSONBytes(memberMerge)));
    }
}
