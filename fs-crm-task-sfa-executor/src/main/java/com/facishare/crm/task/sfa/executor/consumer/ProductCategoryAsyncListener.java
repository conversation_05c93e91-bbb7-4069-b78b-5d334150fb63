package com.facishare.crm.task.sfa.executor.consumer;

import com.alibaba.fastjson.JSON;
import com.beust.jcommander.internal.Lists;
import com.facishare.crm.sfa.prm.platform.utils.TraceGenerator;
import com.facishare.crm.task.sfa.model.ProductCategoryMessage;
import com.facishare.crm.task.sfa.services.ProductCategoryBizService;
import com.facishare.paas.appframework.common.mq.RocketMQMessageListener;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.google.common.collect.Sets;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.common.message.MessageExt;
import org.springframework.beans.factory.annotation.Autowired;

import java.nio.charset.StandardCharsets;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 2022/4/7 11:25
 */
@Slf4j
public class ProductCategoryAsyncListener implements RocketMQMessageListener {
    @Autowired
    private ProductCategoryBizService productCategoryBizService;

    @SneakyThrows
    @Override
    public void consumeMessage(List<MessageExt> messages) {
        TraceGenerator.generator();
        if (CollectionUtils.empty(messages)) {
            return;
        }
        Set<String> tenantSet = Sets.newHashSet();
        log.info("产品分类更新选项：message size :{}", messages.size());
        List<String> tags = Lists.newArrayList();
        for (MessageExt message : messages) {
            String body = new String(message.getBody(), StandardCharsets.UTF_8);
            String tag = message.getTags().toUpperCase();
            if (ProductCategoryBiz.PRODUCT_CATEGORY_SYNC_DESCRIBE.equals(tag) || ProductCategoryBiz.SYNC_DESCRIBE_BIZ_DELAY.equals(tag)) {
                ProductCategoryMessage syncDescribeMsg = JSON.parseObject(body, ProductCategoryMessage.class);
                String tenantId = syncDescribeMsg.getTenantId();
                tenantSet.add(tenantId);
                tags.add(tag);
            }
        }
        log.info("产品分类更新选项：tenantSet{}, tag:{}", tenantSet, tags);
        for (String tenant : tenantSet) {
            boolean success = productCategoryBizService.synchronizeDescribe(tenant);
            if (success) {
                log.info("产品分类更新选项：更新选项值成功, tenant:{}", tenant);
            } else {
                log.warn("产品分类更新选项：更新选项值失败, tenant:{}", tenant);
            }
        }
    }

    interface ProductCategoryBiz {
        String PRODUCT_CATEGORY_SYNC_DESCRIBE = "PRODUCT_CATEGORY_SYNC_DESCRIBE";
        String SYNC_DESCRIBE_BIZ_DELAY = "PRODUCT_CATEGORY_SYNC_DESCRIBE_DELAY";
    }
}
