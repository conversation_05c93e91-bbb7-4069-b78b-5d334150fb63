package com.facishare.crm.task.sfa.model.rebate;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class FixedProductData implements Serializable {
    @JSONField(name = "object_api_name")
    @JsonProperty("object_api_name")
    private String objectApiName;
    @JSONField(name = "data")
    @JsonProperty("data")
    private List<DataBean> data;

    @Data
    public static class DataBean implements Serializable {
        /**
         * 产品id
         */
        @JSONField(name = "product_id")
        @JsonProperty("product_id")
        private String productId;
        /**
         * 产品名称
         */
        @JSONField(name = "product_id__r")
        @JsonProperty("product_id__r")
        private String productIdR;
        /**
         * 是否已经删除
         */
        @JSONField(name = "is_deleted")
        @JsonProperty("is_deleted")
        private boolean isDeleted;
        /**
         * 多单位id
         */
        @J<PERSON>NField(name = "unit_id")
        @JsonProperty("unit_id")
        private String unitId;
        /**
         * 多单位名称
         */
        @JSONField(name = "unit__s")
        @JsonProperty("unit__s")
        private String unitIdName;
        /**
         * 是否是多单位
         */
        @JSONField(name = "is_multiple_unit")
        @JsonProperty("is_multiple_unit")
        private boolean isMultipleUnit;
    }
}
