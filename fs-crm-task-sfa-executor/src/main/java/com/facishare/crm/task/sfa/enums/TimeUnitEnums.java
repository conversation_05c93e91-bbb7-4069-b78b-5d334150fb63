package com.facishare.crm.task.sfa.enums;

import com.facishare.paas.I18N;
import com.facishare.paas.appframework.core.exception.ValidateException;
import lombok.Getter;

import static com.facishare.crm.task.sfa.util.constant.I18nKeyConstants.PRM_PARAM_PROTOCOL_ERROR;

/**
 * Created by Sundy on 2024/10/20 23:15
 */
@Getter
public enum TimeUnitEnums {
    YEAR("year"),
    QUARTER("quarter"),
    MONTH("month"),
    WEEK("week"),
    DAY("day");

    private final String unit;

    TimeUnitEnums(String unit) {
        this.unit = unit;
    }

    public static TimeUnitEnums of(String unit) {
        for (TimeUnitEnums e : values()) {
            if (e.unit.equals(unit)) {
                return e;
            }
        }
        throw new ValidateException(I18N.text(PRM_PARAM_PROTOCOL_ERROR, "unit"));
    }

    public static TimeUnitEnums of(String unit, TimeUnitEnums defaultValue) {
        for (TimeUnitEnums e : values()) {
            if (e.unit.equals(unit)) {
                return e;
            }
        }
        return defaultValue;
    }
}

