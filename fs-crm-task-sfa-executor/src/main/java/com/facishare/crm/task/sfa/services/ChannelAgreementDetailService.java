package com.facishare.crm.task.sfa.services;

import com.facishare.crm.sfa.prm.api.client.EnterpriseRelationServiceAdapter;
import com.facishare.crm.sfa.prm.api.dto.MainOutUser;
import com.facishare.crm.sfa.prm.api.dto.ResponseDTO;
import com.facishare.crm.sfa.prm.api.enums.AgreementStatus;
import com.facishare.crm.sfa.prm.api.enums.ScheduleType;
import com.facishare.crm.sfa.prm.api.enums.SignStatus;
import com.facishare.crm.sfa.prm.core.service.TimeComputeService;
import com.facishare.crm.sfa.prm.platform.enums.TimeUnit;
import com.facishare.crm.sfa.prm.platform.utils.DataUtils;
import com.facishare.crm.task.sfa.common.gray.GrayUtils;
import com.facishare.crm.task.sfa.common.util.Safes;
import com.facishare.crm.task.sfa.enums.AgreementSignDateMode;
import com.facishare.crm.task.sfa.enums.ReminderTriggerEnums;
import com.facishare.crm.task.sfa.enums.TimeUnitEnums;
import com.facishare.crm.task.sfa.model.ChannelModel;
import com.facishare.crm.task.sfa.services.channel.exception.ItemNullException;
import com.facishare.crm.task.sfa.util.SearchUtil;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.DBRecord;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.impl.search.OrderBy;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.Instant;
import java.time.Year;
import java.time.ZoneId;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import static com.facishare.crm.sfa.prm.core.constants.ChannelConstants.ACCOUNT_OBJ;
import static com.facishare.crm.sfa.prm.core.constants.ChannelConstants.PARTNER_OBJ;

/**
 * ============================================================
 *
 * @Description:
 * @CreatedBy: Sundy on 2025-03-03
 * ============================================================
 */
@Service
@Slf4j
public class ChannelAgreementDetailService {
    @Resource
    private MetadataServiceExt metadataServiceExt;
    @Resource
    private ChannelRestService channelRestService;
    @Resource
    private ChannelTimeComputeService channelTimeComputeService;
    @Resource
    private EnterpriseRelationServiceAdapter enterpriseRelationServiceAdapter;
    @Resource
    private ServiceFacade serviceFacade;
    @Resource
    private TimeComputeService timeComputeService;

    public ChannelModel.ChangeAgreementDetailResult changeChannelAgreementDetailWhenSign(User user, ChannelModel.AgreementDetailSignDTO agreementDetailSignDTO) {
        if (agreementDetailSignDTO.getOutTenantId() == null) {
            log.warn("changeChannelAgreementDetailWhenSign getOutTenantId is null");
            return null;
        }
        IObjectData partnerAgreementDetailData = fetchChannelAgreementDetailData(user, agreementDetailSignDTO.getObjectApiName(), agreementDetailSignDTO.getObjectData().getId(), null);
        if (partnerAgreementDetailData == null) {
            log.warn("changeChannelAgreementDetailWhenSign partnerAgreementDetailData is null");
            return null;
        }
        return changeAgreementDetail_950(user, agreementDetailSignDTO, SignStatus.SIGNED, partnerAgreementDetailData);
    }

    private Long findLastSignEndData(User user, String objectApiName, String dataId, String id) {
        SearchTemplateQuery query = new SearchTemplateQuery();
        query.setLimit(1);
        SearchUtil.fillFilterNotEq(query.getFilters(), DBRecord.ID, id);
        if (ACCOUNT_OBJ.equals(objectApiName)) {
            SearchUtil.fillFilterEq(query.getFilters(), "belong_account_id", dataId);
        } else if (PARTNER_OBJ.equals(objectApiName)) {
            SearchUtil.fillFilterEq(query.getFilters(), "belong_partner_id", dataId);
        } else {
            return null;
        }
        query.setOrders(Lists.newArrayList(new OrderBy(IObjectData.CREATE_TIME, false)));
        List<IObjectData> dataList = metadataServiceExt.findBySearchQueryWithFieldsIgnoreAll(user, "PartnerAgreementDetailObj",
                query, Lists.newArrayList("_id", "end_date"));
        if (CollectionUtils.isEmpty(dataList)) {
            log.warn("can not find last sign date, return current year, tenantId:{}", user.getTenantId());
            throw new ItemNullException("can not find last sign date");
        }
        Long endDate = DataUtils.getValue(dataList.get(0), "end_date", Long.class, null);
        if (endDate == null) {
            log.warn("can not find last sign end date, tenantId:{}", user.getTenantId());
            throw new ItemNullException("can not find last sign end date");
        }
        return endDate;
    }

    private ChannelModel.ChangeAgreementDetailResult changeAgreementDetail_950(User user, ChannelModel.AgreementDetailSignDTO agreementDetailSignDTO, SignStatus signStatus, IObjectData partnerAgreementDetailData) {
        // 计算结束时间
        long currentTimeMillis = System.currentTimeMillis();
        ChannelModel.SignReminderInfoDTO signReminderInfo = channelRestService.querySignReminderInfo(user, agreementDetailSignDTO.getSignSchemeId(), ReminderTriggerEnums.AUTO);
        Long startTime;
        Long endTime;
        if (GrayUtils.isVersionGrayTenant(user.getTenantId())) {
            if (agreementDetailSignDTO.isRenewalEvent()) {
                // 续约，取最近一条状态为
                Long lastSignEndData = findLastSignEndData(user, agreementDetailSignDTO.getObjectApiName(), agreementDetailSignDTO.getObjectData().getId(), partnerAgreementDetailData.getId());
                long tempStartTime = timeComputeService.calculateTimestampByOffset(lastSignEndData, 1, TimeUnit.DAY);
                startTime = timeComputeService.timestampOf(tempStartTime, 0, 0, 0, 0);
                Integer renewalCycleTime = signReminderInfo.getRenewalCycleTime();
                String renewalCycleUnit = signReminderInfo.getRenewalCycleUnit();
                long offsetTime = timeComputeService.calculateTimestampByOffset(startTime, renewalCycleTime, TimeUnit.find(renewalCycleUnit));
                long tempEndTime = timeComputeService.calculateTimestampByOffset(offsetTime, -1, TimeUnit.DAY);
                endTime = timeComputeService.timestampOf(tempEndTime, 23, 59, 59, 0);
            } else {
                startTime = calculateAgreementStartTime(signReminderInfo.getStartDate());
                endTime = calculateAgreementEndTime(signReminderInfo, startTime);
            }
        } else {
            startTime = computeAgreementStartTime(user, currentTimeMillis, signReminderInfo);
            endTime = computeAgreementEndTime(user, currentTimeMillis, signReminderInfo);
        }
        partnerAgreementDetailData.set("start_date", startTime);
        partnerAgreementDetailData.set("end_date", endTime);
        partnerAgreementDetailData.set("signing_time", currentTimeMillis);
        partnerAgreementDetailData.set("agreement_status", AgreementStatus.ACTIVE.getStatus());
        partnerAgreementDetailData.set("sign_status", signStatus.getStatus());
        log.info("changeChannelAgreementDetailWhenSign start_date:{} end_date:{} signing_time:{}", currentTimeMillis, endTime, currentTimeMillis);
        ResponseDTO<MainOutUser> responseDTO = enterpriseRelationServiceAdapter.fetchOutMainUser(user, agreementDetailSignDTO.getObjectApiName(), agreementDetailSignDTO.getObjectData().getId());
        MainOutUser mainOutUser = responseDTO.getResult();
        boolean updateTeam = isUpdateTeam(mainOutUser, agreementDetailSignDTO.getOutTenantId(), agreementDetailSignDTO.isRenewalEvent());
        // 更新开始时间、更新开始、结束日期、协议状态
        List<String> updateFields = Lists.newArrayList("start_date", "end_date", "signing_time", "agreement_status", "sign_status");
        if (updateTeam) {
            partnerAgreementDetailData.setOutTenantId(agreementDetailSignDTO.getOutTenantId());
            partnerAgreementDetailData.setOutOwner(Lists.newArrayList(String.valueOf(mainOutUser.getOutUserId())));
            updateFields.add("out_tenant_id");
            updateFields.add("out_owner");
        }
        log.info("changeChannelAgreementDetailWhenSign#bulkUpdateByFields:{}", updateFields);
        metadataServiceExt.bulkUpdateByFields(user, Lists.newArrayList(partnerAgreementDetailData), updateFields);
        if (updateTeam) {
            log.info("changeChannelAgreementDetailWhenSign#addMember2RelevantTeam, getOutTenantId:{}, getOutUserId:{}", mainOutUser.getOutTenantId(), mainOutUser.getOutUserId());
            metadataServiceExt.addMember2RelevantTeam(user, partnerAgreementDetailData, String.valueOf(mainOutUser.getOutTenantId()), String.valueOf(mainOutUser.getOutUserId()));
        }
        return ChannelModel.ChangeAgreementDetailResult
                .builder()
                .agreementDetailId(partnerAgreementDetailData.getId())
                .reminderTrigger(ReminderTriggerEnums.from(signReminderInfo.getReminderTrigger()))
                .endTime(endTime)
                .reminderTypes(signReminderInfo.getReminderTypes())
                .scheduleType(ScheduleType.find(signReminderInfo.getScheduleType(), ScheduleType.ONE_TIME))
                .build();
    }

    @NotNull
    private Long calculateAgreementEndTime(ChannelModel.SignReminderInfoDTO signReminderInfo, Long startTime) {
        ScheduleType scheduleType = ScheduleType.from(signReminderInfo.getScheduleType());
        if (scheduleType == ScheduleType.ONE_TIME) {
            Integer planDuration = signReminderInfo.getPlanDuration();
            String planDurationUnit = signReminderInfo.getPlanDurationUnit();
            long offsetTime = timeComputeService.calculateTimestampByOffset(startTime, planDuration, TimeUnit.find(planDurationUnit));
            long tempEndTime = timeComputeService.calculateTimestampByOffset(offsetTime, -1, TimeUnit.DAY);
            return timeComputeService.timestampOf(tempEndTime, 23, 59, 59, 0);
        } else {
            Integer renewalCycleTime = signReminderInfo.getRenewalCycleTime();
            String renewalCycleUnit = signReminderInfo.getRenewalCycleUnit();
            long offsetTime = timeComputeService.calculateTimestampByOffset(startTime, renewalCycleTime, TimeUnit.find(renewalCycleUnit));
            long tempEndTime = timeComputeService.calculateTimestampByOffset(offsetTime, -1, TimeUnit.DAY);
            return timeComputeService.timestampOf(tempEndTime, 23, 59, 59, 0);
        }
    }

    private long calculateAgreementStartTime(String startDate) {
        // 如果是第一次签约，取当年时间。
        int currentYear = Year.now().getValue();
        return timeComputeService.getMidnightTimestamp(currentYear, startDate);
    }

    private boolean isUpdateTeam(MainOutUser mainOutUser, String outTenantId, boolean isRenewalEvent) {
        return mainOutUser != null && Objects.equals(outTenantId, String.valueOf(mainOutUser.getOutTenantId())) && !isRenewalEvent;
    }

    private Long computeAgreementStartTime(User user, long currentTimeMillis, ChannelModel.SignReminderInfoDTO signReminderInfo) {
        if (signReminderInfo == null) {
            log.warn("signReminderInfo is null, tenant:{}", user.getTenantId());
            return null;
        }
        ScheduleType scheduleType = ScheduleType.find(signReminderInfo.getScheduleType(), null);
        if (scheduleType == null) {
            log.warn("signReminderInfo#scheduleType is null, tenant:{}", user.getTenantId());
            return null;
        }
        if (scheduleType == ScheduleType.FIXED_DATE) {
            return channelTimeComputeService.computeFixedDateStartTime(signReminderInfo);
        } else if (scheduleType == ScheduleType.CYCLE) {
            AgreementSignDateMode StartMode = AgreementSignDateMode.find(signReminderInfo.getAgreementStartMode(), AgreementSignDateMode.BY_DATE);
            if (StartMode == AgreementSignDateMode.BY_DATE) {
                return currentTimeMillis;
            } else if (StartMode == AgreementSignDateMode.CUSTOM) {
                int currentYear = Year.now().getValue();
                return channelTimeComputeService.computeCustomTime(currentYear, signReminderInfo.getCustomStartDate());
            } else {
                return currentTimeMillis;
            }
        } else if (scheduleType == ScheduleType.ONE_TIME) {
            return currentTimeMillis;
        }
        return currentTimeMillis;
    }

    private Long computeAgreementEndTime(User user, long currentTimeMillis, ChannelModel.SignReminderInfoDTO signReminderInfo) {
        if (signReminderInfo == null) {
            log.warn("signReminderInfo is null, tenant:{}", user.getTenantId());
            return null;
        }
        ScheduleType scheduleType = ScheduleType.find(signReminderInfo.getScheduleType(), null);
        if (scheduleType == null) {
            log.warn("signReminderInfo#scheduleType is null, tenant:{}", user.getTenantId());
            return null;
        }
        Long endTime = null;
        if (scheduleType == ScheduleType.FIXED_DATE) {
            endTime = channelTimeComputeService.computeFixedDateEndTime(signReminderInfo.getFixedMonth(), signReminderInfo.getFixedDay());
        } else if (scheduleType == ScheduleType.CYCLE) {
            AgreementSignDateMode endMode = AgreementSignDateMode.find(signReminderInfo.getAgreementEndMode(), AgreementSignDateMode.BY_DATE_CYCLE);
            if (endMode == AgreementSignDateMode.BY_DATE_CYCLE) {
                endTime = channelTimeComputeService.computeFixedDateEndTime(currentTimeMillis, TimeUnitEnums.of(signReminderInfo.getSignTimeUnit(), null), signReminderInfo.getSignTime());
            } else if (endMode == AgreementSignDateMode.CUSTOM) {
                // eg: 5-31
                String customEndDate = signReminderInfo.getCustomEndDate();
                Long computeFixedDateEndTime = channelTimeComputeService.computeFixedDateEndTime(currentTimeMillis, TimeUnitEnums.of(signReminderInfo.getSignTimeUnit(), null), signReminderInfo.getSignTime());
                if (computeFixedDateEndTime != null) {
                    int yearFromTimestamp = getYearFromTimestamp(computeFixedDateEndTime);
                    endTime = channelTimeComputeService.computeCustomTime(yearFromTimestamp, customEndDate);
                }
            }
        } else if (scheduleType == ScheduleType.ONE_TIME) {
            return null;
        }
        return endTime;
    }

    public static int getYearFromTimestamp(long timestamp) {
        return Instant.ofEpochMilli(timestamp)
                .atZone(ZoneId.systemDefault()) // 指定时区
                .getYear();
    }

    public IObjectData fetchChannelAgreementDetailData(User user, String objectApiName, String dataId, AgreementStatus agreementStatus) {
        SearchTemplateQuery query = new SearchTemplateQuery();
        query.setLimit(1);
        if (agreementStatus != null) {
            SearchUtil.fillFilterEq(query.getFilters(), "agreement_status", agreementStatus.getStatus());
        } else {
            SearchUtil.fillFilterIsNull(query.getFilters(), "agreement_status");
        }
        query.setOrders(Lists.newArrayList(new OrderBy(IObjectData.CREATE_TIME, false)));
        if (ACCOUNT_OBJ.equals(objectApiName)) {
            SearchUtil.fillFilterEq(query.getFilters(), "belong_account_id", dataId);
        } else if (PARTNER_OBJ.equals(objectApiName)) {
            SearchUtil.fillFilterEq(query.getFilters(), "belong_partner_id", dataId);
        } else {
            return null;
        }
        List<IObjectData> dataList = metadataServiceExt.findBySearchQueryIgnoreAll(user, "PartnerAgreementDetailObj", query);
        return Safes.first(dataList);
    }

    public void updateChannelAgreementDetailAbleRenewal(User user, IObjectData channelAgreementDetailData, boolean ableRenewal, SignStatus signStatus) {
        if (channelAgreementDetailData == null) {
            log.warn("updateChannelAgreementDetailAbleRenewal is null, tenant:{}", user.getTenantId());
            return;
        }
        Map<String, Object> updateMap = Maps.newHashMap();
        updateMap.put("able_renewal", ableRenewal);
        updateMap.put("agreement_status", AgreementStatus.COMPLETED.getStatus());
        updateMap.put("sign_status", signStatus.getStatus());
        serviceFacade.updateWithMap(user, channelAgreementDetailData, updateMap);
        log.info("updateChannelAgreementDetailAbleRenewal:更新 able_renewal = {}", ableRenewal);
    }

    public void createAgreementDetail(User user, String objectApiName, String dataId) {
        channelRestService.createAgreementDetail(user, objectApiName, dataId);
    }

    private void updateAgreementDetailAgreementStatus(User user, IObjectData agreementDetailData, SignStatus SignStatus) {
        if (agreementDetailData == null) {
            return;
        }
        Map<String, Object> updateMap = Maps.newHashMap();
        updateMap.put("sign_status", SignStatus.getStatus());
        serviceFacade.updateWithMap(user, agreementDetailData, updateMap);
    }

    public void changeActive2Completed(User user, String objectApiName, String objectDataId, String exceptAgreementDetailId) {
        if (StringUtils.isAnyBlank(objectApiName, objectDataId, exceptAgreementDetailId)) {
            return;
        }
        SearchTemplateQuery query = new SearchTemplateQuery();
        query.setLimit(200);
        SearchUtil.fillFilterNotEq(query.getFilters(), DBRecord.ID, exceptAgreementDetailId);
        SearchUtil.fillFilterEq(query.getFilters(), "agreement_status", AgreementStatus.ACTIVE.getStatus());
        if (ACCOUNT_OBJ.equals(objectApiName)) {
            SearchUtil.fillFilterEq(query.getFilters(), "belong_account_id", objectDataId);
        } else if (PARTNER_OBJ.equals(objectApiName)) {
            SearchUtil.fillFilterEq(query.getFilters(), "belong_partner_id", objectDataId);
        } else {
            return;
        }
        metadataServiceExt.dealObjectDataBySearchQuery(user, "PartnerAgreementDetailObj", query, dataList -> {
            if (CollectionUtils.isEmpty(dataList)) {
                return Sets.newHashSet();
            }
            for (IObjectData data : dataList) {
                data.set("agreement_status", AgreementStatus.COMPLETED.getStatus());
            }
            metadataServiceExt.bulkUpdateByFields(user, dataList, Lists.newArrayList("agreement_status"));
            return dataList.stream().map(DBRecord::getId).collect(Collectors.toSet());
        });
    }

    public ChannelModel.ChangeAgreementDetailResult changeChannelAgreementDetailWhenRenewalSuccessful(User user, ChannelModel.AgreementDetailSignDTO agreementDetailSignDTO) {
        if (agreementDetailSignDTO.getOutTenantId() == null) {
            log.warn("renewalSuccessful getOutTenantId is null");
            return null;
        }
        IObjectData partnerAgreementDetailData = fetchChannelAgreementDetailData(user, agreementDetailSignDTO.getObjectApiName(), agreementDetailSignDTO.getObjectData().getId(), null);
        if (partnerAgreementDetailData == null) {
            log.warn("renewalSuccessful partnerAgreementDetailData is null");
            return null;
        }
        return changeAgreementDetail_950(user, agreementDetailSignDTO, SignStatus.RENEWAL, partnerAgreementDetailData);
    }
}
