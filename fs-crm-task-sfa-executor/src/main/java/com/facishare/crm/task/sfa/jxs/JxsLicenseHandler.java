package com.facishare.crm.task.sfa.jxs;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.facishare.crm.task.sfa.model.LicenseModel;
import com.facishare.crm.task.sfa.services.SFALicenseService;
import com.facishare.crm.task.sfa.services.license.handler.AbstractLicenseHandler;
import com.facishare.crm.task.sfa.util.SFAConcurrentLock;
import com.facishare.crm.task.sfa.util.SearchUtil;
import com.facishare.enterprise.common.util.AESUtil;
import com.facishare.paas.appframework.common.util.ParallelUtils;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.DBRecord;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.QueryResult;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.dao.pg.mapper.metadata.SpecialTableMapper;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.facishare.paas.metadata.util.IdUtil;
import com.fxiaoke.common.http.handler.SyncCallback;
import com.fxiaoke.common.http.spring.OkHttpSupport;
import com.github.autoconf.ConfigFactory;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import okhttp3.Request;
import okhttp3.RequestBody;
import okhttp3.Response;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.List;

import static java.net.HttpURLConnection.HTTP_OK;

/**
 * 精线索
 */
@Slf4j
@Component
public class JxsLicenseHandler extends AbstractLicenseHandler {
    @Autowired
    private SFAConcurrentLock sfaConcurrentLock;

    @Autowired
    private SFALicenseService sfaLicenseService;

    @Autowired
    private ServiceFacade serviceFacade;

    @Autowired
    private SpecialTableMapper specialTableMapper;

    @Resource(name = "httpClientSupport")
    protected OkHttpSupport client;

    static String JXS_AES_KEY = "";
    static boolean CALL_JXS_THREAD_SLEEP = false;
    static int CALL_JXS_THREAD_SLEEP_TIMEOUT = 0;
    static int CALL_JXS_ONCE_COUNT = 0;

    static {
        ConfigFactory.getInstance().getConfig("fs-crm-sales-config", (config) -> {
            JXS_AES_KEY = config.get("jxs_aes_key", "EcKqeRcvLJWiES-Y74UJxsAtnxn");
            CALL_JXS_ONCE_COUNT = config.getInt("call_jxs_once_count", 100);
            CALL_JXS_THREAD_SLEEP = config.getBool("call_jxs_thread_sleep", false);
            CALL_JXS_THREAD_SLEEP_TIMEOUT = config.getInt("call_jxs_thread_sleep_timeout", 3000);
        });
    }

    @Override
    public List<String> moduleCodes() {
        return Lists.newArrayList("Outbound_marketing_app");
    }

    @Override
    public void execute(LicenseModel.LicenseMessage message) {
        sfaConcurrentLock.runWithinWaitTryLock("sfa_jxs_create_account_lock", 3 * 1000, () -> {
            if ("Outbound_marketing_app".equals(message.getLicenseVersion())) {
                ParallelUtils.ParallelTask parallelTask = ParallelUtils.createBackgroundTask();
                parallelTask.submit(() -> {
                    for (int i = 0; i < 100; i++) {
                        SearchTemplateQuery searchTemplateQuery = new SearchTemplateQuery();
                        searchTemplateQuery.setOffset(i);
                        searchTemplateQuery.setLimit(CALL_JXS_ONCE_COUNT);
                        List<IFilter> filters = Lists.newArrayList();
                        SearchUtil.fillFilterEq(filters, DBRecord.IS_DELETED, "0");
                        searchTemplateQuery.setFilters(filters);
                        QueryResult<IObjectData> queryResult = serviceFacade.findBySearchQueryIgnoreAll(User.systemUser(message.getTenantId()),
                                "PersonnelObj",
                                searchTemplateQuery);
                        log.info("queryResult={} searchTemplateQuery={}", queryResult, searchTemplateQuery);
                        if (queryResult == null) {
                            break;
                        }

                        if (CollectionUtils.isEmpty(queryResult.getData())) {
                            break;
                        }

                        for (IObjectData objectData : queryResult.getData()) {
                            try {
                                String userName = objectData.getTenantId() + ":" + objectData.get("user_id", String.class);
                                String content = objectData.getTenantId() + ":" + objectData.get("user_id", String.class);
                                JSONObject response = callJxsCreateUser(userName,
                                        objectData.getTenantId(),
                                        content,
                                        client);
                                saveJxsResponse(objectData.getTenantId(),
                                        objectData.get("user_id", String.class),
                                        response,
                                        specialTableMapper);
                            } catch (Exception e) {

                            }
                        }

                        if (CALL_JXS_THREAD_SLEEP) {
                            try {
                                Thread.sleep(CALL_JXS_THREAD_SLEEP_TIMEOUT);
                            } catch (InterruptedException e) {
                                throw new RuntimeException(e);
                            }
                        }
                    }
                });

                parallelTask.run();
            } else {
                //...
            }
        });
    }

    public static void saveJxsResponse(String tenantId,
                                       String userId,
                                       JSONObject response,
                                       SpecialTableMapper specialTableMapper) {
        JSONObject data = response.getJSONObject("data");
        if (jsonIsEmpty(data)) {
            return;
        }

        StringBuilder insert = new StringBuilder();
        insert.append("INSERT INTO sfa_biz_jxs_token (id, tenant_id, user_id, account, password, is_deleted, create_time) VALUES (");
        insert.append("'" + IdUtil.generateId() + "',");
        insert.append("'" + tenantId + "',");
        insert.append("'" + userId + "',");
        insert.append("'" + data.getString("account") + "',");
        insert.append("'" + data.getString("password") + "',");
        insert.append("0,");
        insert.append(System.currentTimeMillis() + ",");
        insert.append(")");
        int result = specialTableMapper.setTenantId(tenantId).insertBySql(insert.toString());
        log.info("tenantId={} result={} insert={}", tenantId, result, insert);
    }

    //todo 如果jxs那边调用出现问题，什么逻辑，需要和产品确认
    public static JSONObject callJxsCreateUser(String userName, String companyName, String content, OkHttpSupport client) {
        JSONObject body = new JSONObject();
        body.put("userName", userName);
        body.put("companyName", companyName);
        body.put("content", AESUtil.encode(JXS_AES_KEY, content));
        JSONObject response = httpPost("http://10.10.10.10:8080/security/openAccount", body, JSONObject.class, client);
        if (jsonIsEmpty(response)) {
            //...
        }

        if (response.getLongValue("errcode") != HTTP_OK) {
            //...
        }

        if (jsonIsEmpty(response.getJSONObject("data"))) {
            //...
        }

        return response;
    }

    public static boolean jsonIsEmpty(JSONObject param) {
        return param == null || param.isEmpty();
    }

    public static String shortString(String body) {
        if (Strings.isNullOrEmpty(body)) {
            return "";
        }

        return body.length() > 200 ? body.substring(0, 200) + "..." : body;
    }

    public static <T> T httpPost(String url, JSONObject requestData, Class<T> clazz, OkHttpSupport client) {
        Request request = new Request.Builder()
                .url(url)
                .post(RequestBody.create(requestData.toJSONString().getBytes(StandardCharsets.UTF_8)))
                .build();
        return (T) client.syncExecute(request, new SyncCallback() {
            @Override
            public Object response(Response response) throws IOException {
                if (response == null) {
                    log.error("url={} requestData={} 请求失败, response is null", url, requestData);
                    return null;
                }

                if (response.code() != HTTP_OK || response.body() == null) {
                    log.error("url={} requestData={} 请求失败, response={}", url, requestData, response);
                    return null;
                }

                String body = response.body().string();
                log.info("url={} requestData={} body={}", url, requestData, shortString(body));

                try {
                    return JSON.parseObject(body, clazz);
                } catch (Exception e) {
                    log.error("url={} requestData={} body={} 数据解析失败", url, requestData, shortString(body), e);
                }

                return null;
            }
        });
    }
}
