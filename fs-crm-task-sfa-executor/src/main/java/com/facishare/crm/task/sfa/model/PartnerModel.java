package com.facishare.crm.task.sfa.model;

import com.facishare.paas.metadata.api.IObjectData;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 2021/7/8 10:31
 */
public interface PartnerModel {
    @Data
    class ChangePartnerAndOwnerArg {
        private String apiName;
        private Set<String> ids;
        private String partnerId;
    }

    @Data
    class ChannelApproval {
        private String tenantId;
        private String partnerId;
        // 触发审批人
        private String approvalOperator;
        // 审批状态 reject、pass
        private String approvalStatus;
        private String signSchemeId;
    }
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @Builder
    class ActivationSetting {
        private String tenantId;
        @Builder.Default
        private Set<String> deleteIds = new HashSet<>();
        @Builder.Default
        private Map<String, IObjectData> updateDataMapping = new HashMap<>();
        @Builder.Default
        private Map<String, IObjectData> originalDataMapping = new HashMap<>();
        // 需要更新激活 task 的 id，意味着回收时间或模式进行了变更
        @Builder.Default
        private Set<String> updateTaskIds = new HashSet<>();
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @Builder
    class RenewExpiration {
        private String tenantId;
        private String partnerId;
        private Long renewTimestamp;
    }

}
