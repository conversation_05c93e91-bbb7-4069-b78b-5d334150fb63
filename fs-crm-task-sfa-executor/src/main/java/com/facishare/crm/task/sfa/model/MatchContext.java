package com.facishare.crm.task.sfa.model;

import com.facishare.paas.metadata.api.IObjectData;
import lombok.Builder;
import lombok.Data;
import lombok.ToString;
import org.apache.logging.log4j.util.Strings;

import java.io.Serializable;
import java.util.List;
import java.util.Objects;

@Data
@ToString
@Builder
public class MatchContext implements Serializable {
    private String tenantId;

    private String apiName;

    private String id;

    private IObjectData data;

    private List<IObjectData> details;
    /// 是否为蓝票
    private Boolean isBlue;
    /// 是否为核销单据
    private Boolean isDebit;

    private int generateMatchCount;

    private String sendMessage = Strings.EMPTY;

    public void setSendMessage(String message) {
        if (Strings.isNotEmpty(message)) {
            sendMessage = Strings.isEmpty(sendMessage) ? message : sendMessage + "；" + message;
        }
    }
}