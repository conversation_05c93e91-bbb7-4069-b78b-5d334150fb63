package com.facishare.crm.task.sfa.enums;

import cn.hutool.core.exceptions.ValidateException;
import com.facishare.paas.I18N;
import lombok.Getter;

import static com.facishare.crm.task.sfa.util.constant.I18nKeyConstants.PRM_PARAM_PROTOCOL_ERROR;

/**
 * <AUTHOR>
 * @time 2024-07-03 10:39
 * @Description
 */
@Getter
public enum BizScopeEnums {
    REGISTER("register"),
    SIGN("sign"),
    RENEW("renew");

    private final String scope;

    BizScopeEnums(String scope) {
        this.scope = scope;
    }

    public static BizScopeEnums fromString(String scope) {
        for (BizScopeEnums e : values()) {
            if (e.scope.equals(scope)) {
                return e;
            }
        }
        throw new ValidateException(I18N.text(PRM_PARAM_PROTOCOL_ERROR, "bizScope"));
    }
}
