package com.facishare.crm.task.sfa.jxs;

import com.alibaba.fastjson.JSONObject;
import com.facishare.crm.task.sfa.services.SFALicenseService;
import com.facishare.organization.api.event.OrganizationChangedListener;
import com.facishare.organization.api.event.organizationChangeEvent.EmployeeChangeEvent;
import com.facishare.organization.api.model.type.EmployeeEntityStatus;
import com.facishare.paas.metadata.dao.pg.mapper.metadata.SpecialTableMapper;
import com.fxiaoke.common.http.spring.OkHttpSupport;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Objects;

import static com.facishare.crm.task.sfa.jxs.JxsLicenseHandler.saveJxsResponse;

//consumergroup:sfa_ep_user_changed
@Slf4j
@Component
public class MyOrganizationChangedListener extends OrganizationChangedListener {
    @Autowired
    private SpecialTableMapper specialTableMapper;

    @Resource(name = "httpClientSupport")
    protected OkHttpSupport client;

    @Autowired
    private SFALicenseService sfaLicenseService;

    @Override
    protected void onEmployeeChanged(EmployeeChangeEvent event) {
        String tenantId = String.valueOf(event.getEnterpriseId());
        boolean license = sfaLicenseService.checkModuleLicenseExist(tenantId, "Outbound_marketing_app");
        log.info("tenantId={} Outbound_marketing_app license={}", tenantId, license);
        if (!license) {
            return;
        }

        if (event.getOldEmployeeDto() == null && event.getNewEmployeeDto() != null) {
            log.info("新增 {}", event.getNewEmployeeDto());
            String userId = String.valueOf(event.getNewEmployeeDto().getEmployeeId());
            String userName = tenantId + ":" + userId;
            String content = tenantId + ":" + userId;
            JSONObject response = JxsLicenseHandler.callJxsCreateUser(userName,
                    tenantId,
                    content,
                    client);
            saveJxsResponse(tenantId,
                    userId,
                    response,
                    specialTableMapper);
        } else if (event.getOldEmployeeDto() != null && event.getNewEmployeeDto() != null) {
            boolean c1 = !Objects.equals(event.getOldEmployeeDto().getStatus(), event.getNewEmployeeDto().getStatus());
            boolean c2 = Objects.equals(event.getNewEmployeeDto().getStatus(), EmployeeEntityStatus.STOP);
            boolean c3 = Objects.equals(event.getNewEmployeeDto().getStatus(), EmployeeEntityStatus.DELETE);
            log.info("变更 {} {} {}", c1, c2, c3);
            if (c1 && (c2 || c3)) {
                String userId = String.valueOf(event.getNewEmployeeDto().getEmployeeId());
                String update = String.format("UPDATE sfa_biz_jxs_token SET is_delete=1 WHERE tenant_id='%s' AND is_deleted=0 AND userId='%s' ;", tenantId, userId);
                int result = specialTableMapper.setTenantId(tenantId).batchUpdateBySql(update);
                log.info("tenantId={} result={} insert={}", tenantId, result, update);

                //todo 需要通知jxs吗？
            }
        } else {
            //...
        }
    }
}
