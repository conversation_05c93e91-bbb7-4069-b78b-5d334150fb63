package com.facishare.crm.task.sfa.common.constants;

import com.facishare.crm.openapi.Utils;
import com.facishare.paas.metadata.api.IObjectData;
import com.google.common.collect.ImmutableList;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.ImmutableSet;

import java.util.Map;
import java.util.Set;

/**
 * 价格政策常量
 *
 * <AUTHOR>
 */
public interface PricePolicyConstants {

    /**
     * 明细行数据唯一标识符
     */
    String DATA_INDEX = "__data_index";
    /**
     * 主对象数据标记
     */
    String MASTER_INDEX = "MASTER";
    String VIRTUAL_DETAIL_INDEX = "DETAIL";
    String RULE_ENGINE_SCENE = "CRM_ADVANCED_PRICING";
    String REFERENCE_SOURCE_TYPE = "CRM_ADVANCED_PRICING";
    String REFERENCE_SOURCE_LABEL = "价格政策";
    String PRODUCT_ID = "product_id";
    String QUANTITY = "quantity";
    /**
     * SalesOrderProductObj
     * 价格(元)
     */
    String PRODUCT_PRICE = "product_price";
    //主对象产品合计
    String PRODUCT_AMOUNT = "product_amount";
    //主对象价目表合计
    String PRICE_BOOK_AMOUNT = "price_book_amount";
    String PRICE_POLICY_ID = "price_policy_id";
    //整单分摊时，从对象上记录主对象的政策id，代码计算用
    String MASTER_PRICE_POLICY_ID = "master_policy_id";
    String RULE_IDS = "price_policy_rule_ids";
    String GIFT_MAP = "gift_map";
    /**
     * 产品组合标识
     */
    String GROUP_KEY = "group_key";
    /**
     * 促销优惠额
     */
    String POLICY_DYNAMIC_AMOUNT = "policy_dynamic_amount";
    String DYNAMIC_AMOUNT = "dynamic_amount";
    String POLICY_SUBTOTAL = "policy_subtotal";
    String IS_GIVE_AWAY = "is_giveaway";
    String PARENT_GIFT_KEY = "parent_gift_key";
    String PROD_PKG_KEY = "prod_pkg_key";
    String SPLIT_REGEX = "_";
    String ORDER_DATA_INDEX = "data_index";
    String BATCH_NO = "batch_no";
    String AMORTIZE_CALL_BACK_KEY = "amortize_call_back_key";
    //价格政策赠本品，本品ID
    String PRICEPOLICY_THISPRODUCT = "pricepolicythisproduct";
    String ALL_PRODUCT = "ALL";
    String PRICE_BOOK_DISCOUNT = "price_book_discount";

    String RULE_CONDITION_PREFIX = "CONDITION";

    class PricePolicyField {
        public static final String START_DATE = "start_date";
        public static final String END_DATE = "end_date";
        public static final String PRIORITY = "priority";
        public static final String CALCULATE_STATUS = "calculate_status";
        public static final String ACCOUNT_RANGE = "account_range";
        public static final String ACTIVE_STATUS = "active_status";
        public static final String MODIFY_TYPE = "modify_type";
        public static final String LAST_MODIFIED_TIME = "last_modified_time";
        public static final String IMAGE_PATH = "image_path";
    }

    class RuleField {
        public static final String PRICE_POLICY_ID = "price_policy_id";
        public static final String SOURCE_OBJECT_API_NAME = "source_object_api_name";
        public static final String GROUP_NO = "group_no";
        public static final String PRIORITY = "priority";
        public static final String RULE_TYPE = "rule_type";
        public static final String RULE_CONDITION = "rule_condition";
        public static final String EXECUTION_RESULT = "execution_result";
        public static final String AMORTIZE_TYPE = "amortize_type";
        public static final String PRODUCT_RANGE_TYPE = "product_range_type";
        public static final String PRODUCT_FIELDS = "product_fields";
    }

    class AccountField {
        public static final String PRICE_POLICY_ID = "price_policy_id";
        public static final String ACCOUNT_ID = "account_id";
        public static final String APPLY_RANGE = "apply_range";
        public static final ImmutableList<String> REMOVE_IMPORT_FIELDS = ImmutableList.of(IObjectData.NAME, AccountField.APPLY_RANGE);
    }

    class AmortizeInfoField {
        public static final String SALES_ORDER_ID = "sales_order_id";
        public static final String SALES_ORDER_PRODUCT_ID = "sales_order_product_id";
        public static final String PRICE_POLICY_ID = "price_policy_id";
        public static final String PRICE_POLICY_RULE_ID = "price_policy_rule_id";
        public static final String TOTAL_AMOUNT = "total_amount";
        public static final String AMORTIZE_AMOUNT = "amortize_amount";
        public static final String DEFAULT_RECORD_TYPE = "default__c";
        public static final String MARKET_TYPE = "market_type";
        public static final String OBJECT_DATA_ID = "object_data_id";
        public static final String OBJECT_API_NAME = "object_api_name";
        public static final String ORDER_API_NAME = "order_api_name";
        public static final String PRODUCT_API_NAME = "product_api_name";
        public static final String RULE_TYPE = "rule_type";
        public static final String AMORTIZE_TYPE = "amortize_type";
        public static final String PRODUCT_ID = "product_id";
        public static final String AMORTIZE_TIME = "amortize_time";
    }

    class AggregateRuleType {
        //常规聚合值（默认）
        public static final String aggregate = "aggregate";
        //产品组合
        public static final String group = "group";
    }

    class VirtualField {
        //金额
        public static final String VIRTUAL_AMOUNT = "virtual_amount";
        //小计
        public static final String VIRTUAL_SUBTOTAL = "virtual_subtotal";
        //单价
        public static final String VIRTUAL_PRICE = "virtual_price";
        //折扣
        public static final String VIRTUAL_DISCOUNT = "virtual_discount";
    }

    class RuleAmortizeType {
        //不分摊
        public static final String NO = "no";
        //分摊至明细
        public static final String DETAIL = "detail";
    }

    class ModifyType {
        public static final String ALL = "all";
        public static final String MASTER = "master";
        public static final String DETAIL = "detail";
    }
    class AccountRangeType {
        public static final String TYPE = "type";

    }

    enum ApplyRange{
        FIXED("FIXED", "指定客户");
        private String unitId;
        private String unitName;

        public String getValue() {
            return unitId;
        }

        ApplyRange(String ruleType, String unitName) {
            this.unitId = ruleType;
        }
    }

    enum SizeUnit {
        SMALL("small", "小单位"),
        MEDIUM("medium", "中单位"),
        LARGE("large", "大单位"),
        CURRENT_PRODUCT_UNIT("currentProductUnit", "本品单位"),
        BASE_UNIT("baseUnit", "基准单位");

        private String unitId;
        private String unitName;

        public String getUnitId() {
            return unitId;
        }

        SizeUnit(String ruleType, String unitName) {
            this.unitId = ruleType;
        }
    }

    enum PricePolicyRuleType {
        PRICING("pricing"),
        GIFT("gift"),
        TIERED_PRICING("tiered_pricing"),
        COMBINATION_PRICING("combination_pricing"),
        COMBINATION_GIFT("combination_gift");

        private final String ruleType;

        PricePolicyRuleType(String ruleType) {
            this.ruleType = ruleType;
        }

        public String getRuleType() {
            return ruleType;
        }
    }

    Set<String> pricingTypes = ImmutableSet.of(PricePolicyRuleType.PRICING.getRuleType(), PricePolicyRuleType.COMBINATION_PRICING.getRuleType());
    Set<String> giftTypes = ImmutableSet.of(PricePolicyRuleType.GIFT.getRuleType(), PricePolicyRuleType.COMBINATION_GIFT.getRuleType());


    enum ActiveStatusEnum {
        ENABLE("enable"),
        DISABLE("disable");

        private final String status;

        ActiveStatusEnum(String status) {
            this.status = status;
        }

        public String getStatus() {
            return status;
        }
    }

    enum FieldNameType {
        FIELD,
        AGGREGATE
    }
    Map<String, String> MASTER_DETAIL_API_NAME = ImmutableMap.of(
            Utils.SALES_ORDER_API_NAME, Utils.SALES_ORDER_PRODUCT_API_NAME,
            Utils.QUOTE_API_NAME, Utils.QUOTE_LINES_API_NAME);


    enum PricingRuleOperator {
        EQUAL,
        ADD,
        SUBTRACT,
        MULTIPLY,
        DIVIDE
    }

    enum MarketType {
        //PricePolicy（促销）、Rebate（返利）、Coupon(优惠券)

        PRICEPOLICY("PricePolicy"),
        REBATE("Rebate"),
        COUPON("Coupon");

        private final String type;

        MarketType(String type) {
            this.type = type;
        }

        public String getType() {
            return type;
        }
    }

    enum ProductRangeType {
        ALL("ALL"),
        CONDITION("CONDITION");

        private final String productRangeType;

        ProductRangeType(String productRangeType) {
            this.productRangeType = productRangeType;
        }

        public String getProductRangeType() {
            return productRangeType;
        }
    }
}
