package com.facishare.crm.task.sfa.common.util;

public class I18NKeyUtil {
    public static final String SFA_TRADEREFUNDBUSINESS_557_1 = "sfa.TradeRefundBusiness.557.1";
    public static final String SFA_LEADS_AUTOALLOCATE_SERVICE_135 = "sfa.LeadsAutoAllocateService.135";
    public static final String SFA_LEADS_AUTOALLOCATE_SERVICE_136 = "sfa.LeadsAutoAllocateService.136";
    public static final String SFA_LEADS_AUTOALLOCATE_SERVICE_64 = "sfa.LeadsAutoAllocateService.64";
    public static final String SFA_LEADS_AUTOALLOCATE_SERVICE_67 = "sfa.LeadsAutoAllocateService.67";
    public static final String SFA_LEADS_AUTOALLOCATE_SERVICE_74 = "sfa.LeadsAutoAllocateService.74";
    public static final String SFA_LEADS_AUTOALLOCATE_SERVICE_81 = "sfa.LeadsAutoAllocateService.81";
    public static final String SFA_LEADS_AUTOALLOCATE_SERVICE_98 = "sfa.LeadsAutoAllocateService.98";
    public static final String SFA_LEADS_AUTOALLOCATE_SERVICE_331 = "sfa.TradeContractBusiness.269.1";
    public static final String SFA_LEADS_AUTOALLOCATE_SERVICE_137 = "sfa.LeadsAutoAllocateService.137";
    public static final String SFA_LEADS_ALLOCATE_OVER_TIME = "sfa.leads.allocate.over.time";

    /**
     * 线索{0}已超时未分配，请尽快处理
     */
    public static final String SFA_LEADS_ALLOCATE_OVER_TIME_NEW_RECORD = "sfa.leads.allocate.over.time.new.record";
    public static final String SFA_LEADS_FOLLOW_OVER_TIME = "sfa.leads.follow.over.time";

    /**
     * 线索{0}已超时未跟进，请重点关注
     */
    public static final String SFA_LEADS_FOLLOW_OVER_TIME_NEW_RECORD = "sfa.leads.follow.over.time.new.record";
    //开票模式2转换到模式3
    public static final String SFA_INVOICE_MODEL_CHANGE_2_TO_3 = "sfa.invoice.model.change.2.to.3";
    //开票模式2转换到模式3刷数据成功
    public static final String SFA_CHANGE_MODEL_2_TO_3_SUCCESS = "sfa.change.invoice.model.2.to.3.success";
    //开票模式2转换到模式3刷数据失败，请联系管理员
    public static final String SFA_CHANGE_MODEL_2_TO_3_FAILED = "sfa.change.invoice.model.2.to.3.failed";
    //销售线索超时
    public static final String SFA_LEADS_OVER_TIME = "sfa.leads.over.time";

    public static final String SFA_QUALITY_INSPECTION_RECORD = "sfa.quality.inspection.record";
    public static final String SFA_QUALITY_INSPECTION_CRM_MSG = "sfa.quality.inspection.crm_msg";

    //历史线索的重复标记更新完毕，可查看最新结果

    public static final String SFA_LEADS_Dup_Done = "sfa.leads.dup.done";

    //线索【重复】标记更新完毕
    public static final String SFA_LEADS_DUP_DONE_TITLE = "sfa.leads.dup.done.title";


    //{0}线索池的分配规则中已无有效的分配对象
    public static final String SFA_NO_LEADS_TO_ALLOCATE = "sfa.no.leads.to.allocate";
    //请注意，由于被分配的员工线索量均已达上限，%s线索池的分配规则中，已无有效的可分配对象！
    public static final String SFA_ALLOCATE_MEMBER_LIMITED = "sfa.allocate.member.limited";

    //请注意，由于分配对象中（员工、部门）被停用或删除，%s线索池的分配规则中，已无有效的可分配对象，线索将无法自动流转，请及时调整！
    public static final String SFA_NO_ALLOCATE_MEMBER_TO_ALLOCATE = "sfa.no.allocate.member.to.allocate";
    //新开票开启
    public static final String SFA_NEW_INVOICE_OPEN_TITLE = "sfa.new.invoice.open.title";

    //新开票开启刷数据成功
    public static final String SFA_NEW_INVOICE_OPEN_SUCCESS = "sfa.new.invoice.open.success";
    //新开票开启刷数据失败，请联系管理员
    public static final String SFA_NEW_INVOICE_OEPN_FAILED = "sfa.new.invoice.open.failed";

    //可售范围查重，提醒
    public static final String SFA_AVAILABLE_DUP_NOTICE_TITLE = "sfa.available.dup.notice.title";
    public static final String SFA_AVAILABLE_DUP_NOTICE_CONTENT = "sfa.available.dup.notice.content";

    //同步bom节点信息到其他bom
    public static final String SFA_SYNC_BOM_NOTICE_TITLE = "sfa.sync.bom.notice.title";
    public static final String SFA_SYNC_BOM_NOTICE_CONTENT = "sfa.sync.bom.notice.content";

    /**
     * 获取描述失败！
     */
    public static final String SFA_CONFIG_GETDESCRIPTIONFAILED = "sfa.config.getdescriptionfailed";

    /**
     * 【复制{0}价目表明细】执行完毕 执行人:{1}
     */
    public static final String SFA_ASYN_COPY_NOTICE_TITLE = "sfa.asyn.copy.notice.title";

    /**
     * 本次操作覆盖{0}条 【{1}价目表明细】 复制到 【{2}价目表明细】，执行成功{3}条，失败{4}条；
     */
    public static final String SFA_ASYN_COPY_NOTICE_CONTENT = "sfa.asyn.copy.notice.content";
    /**
     * %s 是必填项，不能为空
     */
    public static final String SFA_ASYN_COPY_REQUIRED_CANNOT_EMPTY = "sfa.asyn.copy.required.cannot.empty";
    /**
     * 错误信息
     */
    public static final String SFA_ASYN_COPY_ERROR_SHEET_MSG = "sfa.asyn.copy.error.sheet.msg";


    /**
     * 成交状态日志记录：下 %s %s发生变更，触发客户成交规则，成交状态被更改为 %s 状态。
     */
    public static final String SFA_DEAL_STATUS_CHANGE_LOG_MSG = "sfa.deal.status.change.log.msg";

    /**
     * 成交状态日志记录：下 {1} 发生变更，触发客户成交规则，成交状态被更改为 {2} 状态。
     */
    public static final String SFA_DEAL_STATUS_CHANGE_NO_FIELDS_LOG_MSG = "sfa.deal.status.change.no.fields.log.msg";


    /**
     * 分配销售线索
     */
    public static final String SFA_LEADS_AUTO_ALLOCATE_SUCCESS_TITLE_MSG = "sfa.leads.auto.allocate.success.title.msg";
    /**
     * 系统 给您分配了 销售线索  %s
     */
    public static final String SFA_LEADS_AUTO_ALLOCATE_SUCCESS_CONTENT_MSG = "sfa.leads.auto.allocate.success.content.msg";
    /**
     * 销售线索分配异常
     */
    public static final String SFA_LEADS_AUTO_ALLOCATE_ERROR_TITLE_MSG = "sfa.leads.auto.allocate.error.title.msg";
    /**
     * 注意，由于无法匹配到 {0变量：线索池名称}线索池中符合条件的分配规则，线索 {1变量：线索名称}分配失败，请及时调整相关规则。
     */
    public static final String SFA_LEADS_AUTO_ALLOCATE_ERROR_RULE_CONTENT_MSG = "sfa.leads.auto.allocate.error.rule.content.msg";
    /**
     * 注意，由于    {0}    线索池的分配规则中已无有效的可分配对象，线索     {1}     分配失败，请及时调整相关规则
     */
    public static final String SFA_LEADS_AUTO_ALLOCATE_ERROR_UPDATE_CONTENT_MSG = "sfa.leads.auto.allocate.error.update.content.msg";


    /**
     * 注意，由于{0}线索池的分配规则中已无有效的可分配对象{1}，线索{2}分配失败，请及时调整相关规则
     */
    public static final String SFA_LEADS_AUTO_ALLOCATE_ERROR_NO_MEMBER_CONTENT_MSG = "sfa.leads.auto.allocate.error.no.member.content.msg";
    /**
     * 【失败原因：线索领取上限或保有量均已饱和】
     */
    public static final String SFA_LEADS_AUTO_ALLOCATE_ERROR_FAILURE_REASON1_MSG = "sfa.leads.auto.allocate.error.failure.reason1.msg";
    /**
     * 【失败原因：均已被分配过当前线索】
     */
    public static final String SFA_LEADS_AUTO_ALLOCATE_ERROR_FAILURE_REASON2_MSG = "sfa.leads.auto.allocate.error.failure.reason2.msg";
    /**
     * 【失败原因：员工、部门被停用或删除】
     */
    public static final String SFA_LEADS_AUTO_ALLOCATE_ERROR_FAILURE_REASON3_MSG = "sfa.leads.auto.allocate.error.failure.reason3.msg";

    /**
     * 招投标市场分析 {0} 已生成，请及时查阅。
     */
    public static final String SFA_PROCUREMENT_ANALYSIS_GENERATE_MSG = "sfa.procurement.analysis.generate.msg";
    /**
     * 招投标市场分析 {0} 已生成失败，请查看规则条件设置。
     */
    public static final String SFA_PROCUREMENT_ANALYSIS_GENERATE_MSG_ERR = "sfa.procurement.analysis.generate.msg.err";

    /**
     * 招投标市场分析生成
     */
    public static final String SFA_PROCUREMENT_ANALYSIS_GENERATE_TITLE = "sfa.procurement.analysis.generate.title";


    /**
     * 您好，根据您当前设置的 {0} 订阅规则，{1} 标讯通已为您匹配到 {1} 条标讯数据，请查看详情。
     */
    public static final String SFA_PROCUREMENT_SUBSCRIPTION_GENERATE_MSG = "sfa.procurement.subscription.generate.msg";


    /**
     * 您好，根据您当前设置的 {0} 订阅规则， {1} 标讯通未能匹配到您需要的标讯数据，请适当调整订阅条件。
     */
    public static final String SFA_PROCUREMENT_SUBSCRIPTION_NONE_GENERATE_MSG = "sfa.procurement.subscription.none.generate.msg";

    /**
     * 【标讯通】标讯订阅监测结果通知
     */
    public static final String SFA_PROCUREMENT_SUBSCRIPTION_GENERATE_TITLE = "sfa.procurement.subscription.generate.title";

    /**
     * 【标讯通】历史标讯导入结果通知
     */
    public static final String SFA_PROCUREMENT_IMPORT_GENERATE_TITLE = "sfa.procurement.import.generate.title";

    /**
     * 【标讯通】千里马订阅器初始化结果通知
     */
    public static final String SFA_PROCUREMENT_SUBSCRIPTION_STATUS_UPDATE_TITLE = "sfa.procurement.subscription.status.update.title";

    /**
     * 【订阅规则】获取数据失败
     */
    public static final String SFA_PROCUREMENT_SUBSCRIPTION_QLM_BUY_DATA_TITLE = "sfa.procurement.subscription.qlm.buy.data.title";
    /**
     * 订阅规则 {0} 获取 {1} 到 {2} 的数据失败，请再次尝试。
     */
    public static final String SFA_PROCUREMENT_SUBSCRIPTION_QLM_BUY_DATA_MSG = "sfa.procurement.subscription.qlm.buy.data.msg";

    /**
     * 订阅器：{0}，初始化完成。
     */
    public static final String SFA_PROCUREMENT_SUBSCRIPTION_STATUS_UPDATE_SUCCESSFUL = "sfa.procurement.subscription.status.update.successful";

    /**
     * 订阅器：{0}，初始化失败：{1}
     */
    public static final String SFA_PROCUREMENT_SUBSCRIPTION_STATUS_UPDATE_ERROR = "sfa.procurement.subscription.status.update.error";


    /**
     * 您好，根据您当前设置的 {0} 规则，系统已执行完批量导入，执行成功：{1}，失败:{2}。
     */
    public static final String SFA_PROCUREMENT_IMPORT_GENERATE_MSG = "sfa.procurement.import.generate.msg";
    public static final String SFA_RISK_BRAIN_MONITORING_REMINDER = "risk.brain.monitoring.reminder";
    public static final String SFA_RISK_BRAIN_MONITORING_REMINDER_CONTENT = "risk.brain.monitoring.reminder.content";

    /**
     * 渠道管理自动创建互联关系失败，请手动进行调整并创建互联关系，数据名称:{0}，原因：{1}
     */
    public static final String AUTOMATIC_CREATE_LINK_RELATION_FAILED = "automatic.create.link.relation.failed";
    /**
     * 渠道管理根据合作伙伴映射规则创建合作伙伴联系人失败，请手动进行调整创建合作伙伴联系人后自行创建互联企业关系，合作伙伴数据名称:{0}，原因：{1}
     */
    public static final String AUTOMATIC_CREATE_PARTNER_CONTACT_FAILED = "automatic.create.partner.contact.failed";

    /**
     * 渠道管理根据渠道准入映射规则创建联系人失败，请手动进行调整创建联系人后自行创建互联企业关系，数据名称:{0}，原因：{1}
     */
    public static final String AUTOMATIC_CREATE_CONTACT_FAILED = "automatic.create.contact.failed";

    /**
     * 创建互联企业关系失败
     */
    public static final String CREATE_LINK_RELATION_FAILED = "create.link.relation.failed";

    /**
     * 渠道管理自动创建互联关系成功，数据名称：{0}
     */
    public static final String AUTOMATIC_CREATE_LINK_RELATION_SUCCESS = "automatic.create.link.relation.success";

    /**
     * 客户本周新增动态简报已生成，请查阅
     * A new customer digest for the week has been generated. Please check it out.
     */
    public static final String SFA_CUSTOMER_ACTIVITY_SUMMARY_TITLE = "sfa.customer.activity.summary.title";

    /**
     * 渠道管理
     */
    public static final String SFA_CHANNEL_MANAGEMENT_TITLE = "sfa.channel.management.title";

    /**
     * 更换 {0} 负责人同步更换相关对象负责人异常结果
     * relatedObjectName
     */
    public static final String SFA_CHANGE_RELATED_OBJECT_OWNER_FAILED = "sfa.change.related.object.owner.failed";

    /**
     * 生成标准BOM失败提醒
     */
    public static final String SFA_CREATE_STANDARD_BOM_FAILED = "sfa.create.standard.bom.failed";
    /**
     * 单据编码为[{0}]的{1}自动生成标准BOM时失败，请联系对应的客户成功负责人协助排查
     */
    public static final String SFA_CREATE_STANDARD_BOM_FAILED_CONTENT = "sfa.create.standard.bom.failed.content";

    /**
     * 渠道准入续签提醒
     */
    public static final String PRM_CHANNEL_MANAGEMENT_RENEWAL_TITLE = "prm.channel.management.renewal.title";

    //订单创建提醒
    public static final String  SFA_ORDER_CREATE_REMIND_TITLE = "sfa.order.create.remind";

    //订单创建提醒内容
    public static final String  SFA_ORDER_CREATE_REMIND_CONTENT = "sfa.order.create.remind.content";

    /**
     * 渠道准入状态变更记录
     */
    public static final String SFA_CHANNEL_STATUS_RECORD_TITLE = "sfa.channel.status.record.title";
    /**
     * 渠道准入创建互联企业
     */
    public static final String SFA_CHANNEL_CREATE_LINK_RELATION_TITLE = "sfa.channel.create.link.relation.title";
    /**
     * 渠道准入未续签
     */
    public static final String SFA_CHANNEL_RENEWAL_REMIND_TITLE = "sfa.channel.renewal.remind.title";
    /**
     * 渠道准入已续签
     */
    public static final String SFA_CHANNEL_RENEWAL_SUCCESSFUL_TITLE = "sfa.channel.renewal.successful.title";
    /**
     * 渠道准入签约审批驳回
     */
    public static final String SFA_CHANNEL_RENEWAL_SIGN_REJECT = "sfa.channel.renewal.sign.reject";
    /**
     * 渠道准入签约成功
     */
    public static final String SFA_CHANNEL_RENEWAL_SIGN_SUCCESS = "sfa.channel.renewal.sign.success";

}
