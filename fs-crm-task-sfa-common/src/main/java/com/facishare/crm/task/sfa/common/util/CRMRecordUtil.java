package com.facishare.crm.task.sfa.common.util;

import com.facishare.paas.appframework.common.service.CRMNotificationService;
import com.facishare.paas.appframework.common.service.dto.InternationalItem;
import com.facishare.paas.appframework.common.service.model.NewCrmNotification;
import com.facishare.paas.appframework.core.model.User;
import com.google.common.base.Strings;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.elasticsearch.common.util.set.Sets;

import java.util.List;
import java.util.Map;
import java.util.UUID;


@Slf4j
public class CRMRecordUtil {

    public static void sendNewCRMRecord(CRMNotificationService crmNotifyService,
            User user,
            int type,
            List<Integer> receiverIds,
            String senderId,
            String title,
            String fullContent,
            String titleInternationalKey,
            List<String> internationalParameters,
            String fullContentInternationalKey,
            List<String> fullContentInternationalParameters,
            Map<String, String> urlParameter) {
        InternationalItem titleInternationalItem = null;
        InternationalItem contentInternationalItem = null;
        if (!Strings.isNullOrEmpty(titleInternationalKey)) {
            titleInternationalItem = new InternationalItem();
            titleInternationalItem.setInternationalParameters(internationalParameters);
            titleInternationalItem.setInternationalKey(titleInternationalKey);
        }
        if (!Strings.isNullOrEmpty(fullContentInternationalKey)) {
            contentInternationalItem = new InternationalItem();
            contentInternationalItem.setInternationalParameters(fullContentInternationalParameters);
            contentInternationalItem.setInternationalKey(fullContentInternationalKey);
        }
        sendNewCRMRecord(crmNotifyService, user, type, receiverIds, senderId, title, fullContent, titleInternationalItem, contentInternationalItem, urlParameter);
    }

    public static void sendNewCRMRecord(CRMNotificationService crmNotifyService,
            User user,
            int type,
            List<Integer> receiverIds,
            String senderId,
            String title,
            String fullContent,
            InternationalItem titleInternational,
            InternationalItem fullContentInternational,
            Map<String, String> urlParameter) {
        try {
            NewCrmNotification newCrmNotification = NewCrmNotification.builder()
                    .receiverIDs(Sets.newHashSet(receiverIds))
                    .sourceId(UUID.randomUUID().toString().replace("-", ""))
                    .type(type)
                    .build();
            if (!Strings.isNullOrEmpty(senderId)) {
                newCrmNotification.setSenderId(senderId);
            }
            if (urlParameter != null) {
                newCrmNotification.setUrlType(1);
                newCrmNotification.setObjectApiName(urlParameter.get("objectApiName"));
                newCrmNotification.setObjectId(urlParameter.get("objectId"));
                newCrmNotification.setUrlParameter(urlParameter);
            }
            if (!Strings.isNullOrEmpty(title)) {
                newCrmNotification.setTitle(title);
            }
            if (!Strings.isNullOrEmpty(fullContent)) {
                newCrmNotification.setFullContent(fullContent);
            }
            if (titleInternational != null) {
                newCrmNotification.setTitleInfo(titleInternational);
            }
            if (fullContentInternational != null) {
                newCrmNotification.setFullContentInfo(fullContentInternational);
            }
            crmNotifyService.sendNewCrmNotification(user, newCrmNotification);
        } catch (Exception ex) {
            log.error(ex.getMessage(), ex);
        }
    }

    public static Map<String, String> getUrlParameter(String objectApiName, String objectId) {
        Map<String, String> rest = Maps.newHashMap();
        rest.put("objectApiName", objectApiName);
        rest.put("objectId", objectId);
        return rest;
    }

    public static String getI18ParameterKey(String key) {
        return String.format("#I18N#%s", key);
    }
}
