package com.facishare.crm.task.sfa.common.describebuilder;

import com.facishare.paas.metadata.impl.describe.DepartmentFieldDescribe;

/**
 * <AUTHOR>
 * @date 2018/8/17
 */
public class DepartmentFieldDescribeBuilder {
    private final DepartmentFieldDescribe departmentFieldDescribe;


    public static DepartmentFieldDescribeBuilder builder() {
        return new DepartmentFieldDescribeBuilder();
    }

    public DepartmentFieldDescribe build() {
        return departmentFieldDescribe;
    }

    public DepartmentFieldDescribeBuilder() {
        departmentFieldDescribe = new DepartmentFieldDescribe();
        departmentFieldDescribe.setStatus("released");
        departmentFieldDescribe.setUnique(false);
        departmentFieldDescribe.setIndex(true);
        departmentFieldDescribe.setDefineType("package");
        departmentFieldDescribe.setActive(true);
        departmentFieldDescribe.setIsExtend(false);
        departmentFieldDescribe.setIsIndexField(false);
    }

    public DepartmentFieldDescribeBuilder apiName(String apiName) {
        departmentFieldDescribe.setApiName(apiName);
        return this;
    }

    public DepartmentFieldDescribeBuilder label(String label) {
        departmentFieldDescribe.setLabel(label);
        return this;
    }

    public DepartmentFieldDescribeBuilder single(boolean isSingle) {
        departmentFieldDescribe.setIsSingle(isSingle);
        return this;
    }

    public DepartmentFieldDescribeBuilder required(boolean isRequired) {
        departmentFieldDescribe.setRequired(isRequired);
        return this;
    }

    public DepartmentFieldDescribeBuilder defaultValue(String defaultValue) {
        departmentFieldDescribe.setDefaultValue(defaultValue);
        return this;
    }

}
