package com.facishare.crm.task.sfa.common.describebuilder;

import com.facishare.paas.metadata.impl.describe.FormulaFieldDescribe;

import java.util.Map;

/**
 * <AUTHOR>
 * @date 2019/11/19
 */
public class FormulaFieldDescribeBuilder {
    private final FormulaFieldDescribe formulaFieldDescribe;

    public FormulaFieldDescribeBuilder() {
        formulaFieldDescribe = new FormulaFieldDescribe();
        formulaFieldDescribe.setActive(true);
        formulaFieldDescribe.setIsExtend(false);
        formulaFieldDescribe.setIndex(true);
        formulaFieldDescribe.setStatus("released");
        formulaFieldDescribe.setDefineType("package");
        formulaFieldDescribe.setUnique(false);
        formulaFieldDescribe.setFieldNum(null);
    }

    public static FormulaFieldDescribeBuilder builder() {
        return new FormulaFieldDescribeBuilder();
    }

    public FormulaFieldDescribe build() {
        return formulaFieldDescribe;
    }

    public FormulaFieldDescribeBuilder apiName(String apiName) {
        formulaFieldDescribe.setApiName(apiName);
        return this;
    }

    public FormulaFieldDescribeBuilder label(String label) {
        formulaFieldDescribe.setLabel(label);
        return this;
    }

    public FormulaFieldDescribeBuilder required(boolean required) {
        formulaFieldDescribe.setRequired(required);
        return this;
    }


    public FormulaFieldDescribeBuilder config(Map<String, Object> config) {
        formulaFieldDescribe.setConfig(config);
        return this;
    }

    public FormulaFieldDescribeBuilder expression(String expression) {
        formulaFieldDescribe.setExpression(expression);
        return this;
    }

    public FormulaFieldDescribeBuilder expressionType(String expressionType) {
        formulaFieldDescribe.setExpressionType(expressionType);
        return this;
    }

    public FormulaFieldDescribeBuilder returnType(String returnType) {
        formulaFieldDescribe.setReturnType(returnType);
        return this;
    }

    public FormulaFieldDescribeBuilder decimalPlaces(int decimalPlaces) {
        formulaFieldDescribe.setDecimalPlaces(decimalPlaces);
        return this;
    }

    public FormulaFieldDescribeBuilder defaultToZero(boolean defaultToZero) {
        formulaFieldDescribe.setDefaultToZero(defaultToZero);
        return this;
    }
}
