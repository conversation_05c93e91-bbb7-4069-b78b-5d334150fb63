package com.facishare.crm.task.sfa.common.constants;

/**
 * Create by baoxinxue 2019/01/10
 */
public interface SalesOrderConstants {
    enum SalesOrderField {
        ACCOUNT_ID("account_id", "客户名称"),
        PARTNER_ID("partner_id", "合作伙伴名称"),
        SETTLE_TYPE("settle_type", "结算方式"),
        ORDER_AMOUNT("order_amount", "销售订单金额"),
        FORCE_COMMIT("IsForceCommit", "是否强制提交"),
        OPPORTUNITY_ID("opportunity_id", "商机名称"),
        NEW_OPPORTUNITY_ID("new_opportunity_id", "商机2.0"),
        SHIP_TO_ID("ship_to_id", "收货人"),
        WORK_FLOW("WorkFlowInfo", "自由流程"),
        PRICE_BOOK_ID("price_book_id", "价目表"),
        LOGISTICS_STATUS("logistics_status", "发货状态"),
        SUBMIT_TIME("submit_time", "提交时间"),
        RECEIVABLE_AMOUNT("receivable_amount", "待回款金额"),
        ORDER_TIME("order_time", "下单日期"),
        PAYMENT_AMOUNT("payment_amount", "已回款金额"),
        INVOICE_AMOUNT("invoice_amount", "开票申请总额"),
        REFUND_AMOUNT("refund_amount", "已退款金额"),
        INVOICED_AMOUNT("invoiced_amount", "已开票金额"),
        NO_INVOICE_AMOUNT("no_invoice_amount", "待开票金额"),
        PRINT_HIERARCHY("print_hierarchy", "打印层级"),
        RETURNED_GOODS_AMOUNT("returned_goods_amount", "退货单金额"),
        PRICE_BOOK_SUBTOTAL("price_book_subtotal","价目表小计（报价小计"),
        DISCOUNT("discount", "整单折扣"),
        PRICE_BOOK_AMOUNT("price_book_amount", "价目表合计"),
        DYNAMIC_AMOUNT("dynamic_amount", "额外调整"),
        POLICY_DYNAMIC_AMOUNT("policy_dynamic_amount", "促销优惠额"),
        POLICY_TOTAL("policy_total", "促销后金额"),
        POLICY_DISCOUNT("policy_discount", "促销后折扣"),
        AREA("area__c", "片区");


        private final String filedApiName;

        SalesOrderField(String filedApiName, String label) {
            this.filedApiName = filedApiName;
        }

        public String getApiName() {
            return this.filedApiName;
        }
    }


    enum OrderStatus {
        TO_BE_CONFIRMED("6", "确认中"),
        CONFIRMED("7", "已确认"),
        REJECTED("8", "已驳回"),
        RECALLED("9", "已撤回"),
        DELIVERED("10", "已发货"),
        RECEIVED("11", "已收货"),
        INVALID("99", "作废");

        private final String value;

        OrderStatus(String value, String label) {
            this.value = value;
        }

        public String getValue() {
            return this.value;
        }
    }

    enum LogisticsStatus {
        TO_BE_SHIPPED("1", "待发货"),
        PARTIAL_DELIVERY("2", "部分发货"),
        CONSIGNED("3", "已发货"),
        PARTIAL_RECEIPT("4", "部分收货"),
        RECEIVED("5", "已收货");

        private final String value;

        LogisticsStatus(String value, String label) {
            this.value = value;
        }

        public String getValue() {
            return this.value;
        }
    }

    // 生命状态
    enum LifeStatus {
        INEFFECTIVE("ineffective","未生效"),
        UNDERREVIEW("under_review","审核中"),
        NORMAL("normal","正常"),
        INCHANGE("in_change","变更中"),
        INVALID("invalid","作废");

        private final String code;

        LifeStatus(String code, String statusName) {
            this.code = code;
        }

        public String getCode() {
            return code;
        }
    }

    enum SalesOrderProductField {
        PRICE_BOOK_PRODUCT_ID("price_book_product_id", "价目表明细"),
        PRODUCT_ID("product_id", "产品名称"),
        QUANTITY("quantity", "数量"),
        PRICE_BOOK_ID("price_book_id", "价目表"),
        ORDER_ID("order_id", "订单"),
        PROD_PKG("prod_pkg_key","bom虚拟key"),
        PARENT_PROD_PKG("parent_prod_pkg_key","父bom虚拟key"),
        ROOT_PROD_PKG("root_prod_pkg_key","根bom虚拟key"),
        SALES_PRICE("sales_price", "销售单价"),
        PRODUCT_PRICE("product_price", "价格"),
        IS_GIVEAWAY("is_giveaway", "是否赠品"),
        DISCOUNT("discount","折扣"),
        SUBTOTAL("subtotal","小计"),
        PRICE_BOOK_DISCOUNT("price_book_discount","价目表折扣"),
        PRICE_BOOK_PRICE("price_book_price","价目表价格"),
        PRICE_BOOK_SUBTOTAL("price_book_subtotal","价目表小计"),
        POLICY_DYNAMIC_AMOUNT("policy_dynamic_amount", "促销优惠额"),
        POLICY_SUBTOTAL("policy_subtotal", "促销后小计"),
        POLICY_PRICE("policy_price", "促销后价格"),
        POLICY_DISCOUNT("policy_discount","促销折扣"),
        DYNAMIC_AMOUNT("dynamic_amount","额外调整"),
        AMORTIZE_AMOUNT("amortize_amount","促销分摊金额"),
        AMORTIZE_SUBTOTAL("amortize_subtotal", "促销分摊后小计"),
        BASE_UNIT_COUNT("base_unit_count","基准单位数量"),
        GIFT_AMORTIZE_PRICE("gift_amortize_price","赠品分摊价格"),
        GIFT_AMORTIZE_SUBTOTAL("gift_amortize_subtotal","赠品分摊价小计"),
        NODE_PRICE("node_price","标准选配价格"),
        NODE_DISCOUNT("node_discount","子件折扣"),
        SHARE_RATE("share_rate","分摊比例"),
        NODE_SUBTOTAL("node_subtotal","子件小计"),
        DEALER("dealer__c", "经销商");

        private final String filedApiName;

        SalesOrderProductField(String filedApiName, String label) {
            this.filedApiName = filedApiName;
        }

        public String getApiName() {
            return this.filedApiName;
        }
    }
    // 生命状态
    enum GiveAwayEnum {
        GIVE("1","赠品");


        private final String code;

        GiveAwayEnum(String code, String statusName) {
            this.code = code;
        }

        public String getCode() {
            return code;
        }
    }
}
