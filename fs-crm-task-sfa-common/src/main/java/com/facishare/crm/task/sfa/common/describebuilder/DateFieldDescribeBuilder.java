package com.facishare.crm.task.sfa.common.describebuilder;

import com.facishare.paas.metadata.impl.describe.DateFieldDescribe;

public class DateFieldDescribeBuilder {
    private final DateFieldDescribe dateFieldDescribe;

    private DateFieldDescribeBuilder() {
        dateFieldDescribe = new DateFieldDescribe();
        dateFieldDescribe.setDateFormat("yyyy-MM-dd");
        dateFieldDescribe.setActive(true);
        dateFieldDescribe.setIsExtend(false);
        dateFieldDescribe.setStatus("released");
        dateFieldDescribe.setDefineType("package");
        dateFieldDescribe.setFieldNum(null);
        dateFieldDescribe.setUnique(false);
        dateFieldDescribe.setIndex(true);
        dateFieldDescribe.setCreateTime(System.currentTimeMillis());
    }

    public static DateFieldDescribeBuilder builder() {
        return new DateFieldDescribeBuilder();
    }

    public DateFieldDescribe build() {
        return dateFieldDescribe;
    }

    public DateFieldDescribeBuilder apiName(String apiName) {
        dateFieldDescribe.setApiName(apiName);
        return this;
    }

    public DateFieldDescribeBuilder label(String label) {
        dateFieldDescribe.setLabel(label);
        return this;
    }

    public DateFieldDescribeBuilder unique(boolean unique) {
        dateFieldDescribe.setUnique(unique);
        return this;
    }

    public DateFieldDescribeBuilder index(boolean index) {
        dateFieldDescribe.setIndex(index);
        return this;
    }

    public DateFieldDescribeBuilder required(boolean required) {
        dateFieldDescribe.setRequired(required);
        return this;
    }

    public DateFieldDescribeBuilder format(String format) {
        dateFieldDescribe.setDateFormat(format);
        return this;
    }

    public DateFieldDescribeBuilder defaultValue(String value) {
        dateFieldDescribe.setDefaultValue(value);
        return this;
    }

    public DateFieldDescribeBuilder defaultIsExpression(Boolean defaultIsExpression) {
        dateFieldDescribe.setDefaultIsExpression(defaultIsExpression);
        return this;
    }
}
