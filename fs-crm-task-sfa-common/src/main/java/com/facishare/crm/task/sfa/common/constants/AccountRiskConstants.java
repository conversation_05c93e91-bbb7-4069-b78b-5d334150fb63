package com.facishare.crm.task.sfa.common.constants;

public interface AccountRiskConstants {
    /**
     * 社会信用代码
     */
    String CREDIT_CODE = "uniform_social_credit_code";
    /**
     * 是否启用风险画像
     */
    String ENABLE_RISK_PORTRAIT = "enable_risk_portrait";
    /**
     * 是否开启风险监控
     */
    String IS_RISK_MONITOR = "is_risk_monitor";
    /**
     * 第三方风险等级
     */
    String THIRD_PARTY_RISK_LEVEL = "third_party_risk_level";
    /**
     * 第三方风险评分
     */
    String THIRD_PARTY_RISK_SCORES = "third_party_risk_scores";
    /**
     * 公式授信额度
     */
    String FORMULA_CREDIT_LIMIT = "formula_credit_limit";
    /**
     * 风险评级规则名称
     */
    String RISK_SCORES_MODEL_NAME = "risk_scores_model_name";
    /**
     * 风险评分规则名称
     */
    String SELF_SERVICE_SCORES_RULE_NAME = "self_service_scores_rule_name";
    /**
     * 授信规则名称
     */
    String CREDIT_RULE_NAME = "credit_rule_name";

    /**
     * 推荐授信额度下限
     */
    String CREDIT_LIMIT_LOWER = "credit_limit_lower";

    /**
     * 推荐授信额度上限
     */
    String CREDIT_LIMIT_UPPER = "credit_limit_upper";
}
