package com.facishare.crm.task.sfa.common.constants;

public interface RuleEngineOperate {
    String EQUALS = "equals";
    String NOT_EQUALS = "notequals";
    /**
     * 包含
     */
    String CONTAINS = "contains";
    /**
     * 不包含
     */
    String NOT_CONTAINS = "notcontains";
    /**
     * 以..开始
     */
    String START_WITH = "startwith";
    /**
     * 以..结尾
     */
    String END_WITH = "endwith";
    String IN = "IN";
    String NOT_IN = "NIN";
    String BETWEEN = "between";
    String NOT_BETWEEN = "notbetween";
    String NOT_START_WITH = "notstartwith";
    String NOT_END_WITH = "notendwith";
    /**
     * is null  为空
     */
    String IS = "IS";
    /**
     * is not null 不为空
     */
    String IS_NOT_NULL = "ISN";
    String NOT_LIKE = "NLIKE";
    String LIKE = "LIKE";
    String GT = "GT";
    String LT = "LT";
    String GTE = "GTE";
    String LTE = "LTE";
    String EQ = "EQ";
    String N = "N";
}
