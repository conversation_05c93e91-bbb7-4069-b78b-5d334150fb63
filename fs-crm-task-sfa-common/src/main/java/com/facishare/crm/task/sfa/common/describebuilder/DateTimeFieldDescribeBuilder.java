package com.facishare.crm.task.sfa.common.describebuilder;

import com.facishare.paas.metadata.impl.describe.DateTimeFieldDescribe;

import java.util.Map;

public class DateTimeFieldDescribeBuilder {
    private final DateTimeFieldDescribe dateTimeFieldDescribe;

    private DateTimeFieldDescribeBuilder() {
        dateTimeFieldDescribe = new DateTimeFieldDescribe();
        dateTimeFieldDescribe.setActive(true);
        dateTimeFieldDescribe.setIsExtend(false);
        dateTimeFieldDescribe.setFieldNum(null);
        dateTimeFieldDescribe.setIndex(true);
        dateTimeFieldDescribe.setDefineType("package");
        dateTimeFieldDescribe.setStatus("released");
        dateTimeFieldDescribe.setDateFormat("yyyy-MM-dd HH:mm:ss");
    }

    public static DateTimeFieldDescribeBuilder builder() {
        return new DateTimeFieldDescribeBuilder();
    }

    public DateTimeFieldDescribe build() {
        return dateTimeFieldDescribe;
    }

    public DateTimeFieldDescribeBuilder apiName(String apiName) {
        dateTimeFieldDescribe.setApiName(apiName);
        return this;
    }

    public DateTimeFieldDescribeBuilder label(String label) {
        dateTimeFieldDescribe.setLabel(label);
        return this;
    }

    public DateTimeFieldDescribeBuilder required(boolean required) {
        dateTimeFieldDescribe.setRequired(required);
        return this;
    }

    public DateTimeFieldDescribeBuilder unique(boolean unique) {
        dateTimeFieldDescribe.setUnique(unique);
        return this;
    }

    public DateTimeFieldDescribeBuilder status(String status) {
        dateTimeFieldDescribe.setStatus(status);
        return this;
    }

    public DateTimeFieldDescribeBuilder defineType(String defineType) {
        dateTimeFieldDescribe.setDefineType(defineType);
        return this;
    }

    public DateTimeFieldDescribeBuilder dateFormat(String dateFormat) {
        dateTimeFieldDescribe.setDateFormat(dateFormat);
        return this;
    }

    public DateTimeFieldDescribeBuilder config(Map<String, Object> config) {
        dateTimeFieldDescribe.setConfig(config);
        return this;
    }

    public DateTimeFieldDescribeBuilder index(boolean index){
        dateTimeFieldDescribe.setIndex(index);
        return this;
    }
}
