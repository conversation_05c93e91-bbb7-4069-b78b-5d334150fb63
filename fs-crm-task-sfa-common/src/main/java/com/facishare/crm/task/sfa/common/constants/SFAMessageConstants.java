package com.facishare.crm.task.sfa.common.constants;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

public interface SFAMessageConstants {

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    class SFAMessageArg {
        private String title, orderId, tenantId, typeDescribe, mgsId;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    class RebateManualCreateArg {
        private String title, msg, tenantId, userId;
    }
}
