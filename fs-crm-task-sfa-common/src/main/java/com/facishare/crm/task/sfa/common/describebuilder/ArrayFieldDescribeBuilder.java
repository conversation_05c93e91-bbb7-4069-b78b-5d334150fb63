package com.facishare.crm.task.sfa.common.describebuilder;

import com.facishare.paas.metadata.impl.describe.ArrayFieldDescribe;

public class ArrayFieldDescribeBuilder {
    private final ArrayFieldDescribe arrayFieldDescribe;

    private ArrayFieldDescribeBuilder() {
        arrayFieldDescribe = new ArrayFieldDescribe();
        arrayFieldDescribe.setActive(true);
        arrayFieldDescribe.setStatus("released");
        arrayFieldDescribe.setDefineType("package");
        arrayFieldDescribe.setIsExtend(false);
        arrayFieldDescribe.setUnique(false);
        arrayFieldDescribe.setRequired(false);
        arrayFieldDescribe.setDefaultValue(null);
        arrayFieldDescribe.setDefaultIsExpression(false);
        arrayFieldDescribe.setDefaultToZero(false);
        arrayFieldDescribe.setIndex(false);
        arrayFieldDescribe.setFieldNum(null);
        arrayFieldDescribe.setCreateTime(System.currentTimeMillis());
        arrayFieldDescribe.setHelpText(null);
    }

    public static ArrayFieldDescribeBuilder builder() {
        return new ArrayFieldDescribeBuilder();
    }

    public ArrayFieldDescribe build() {
        return arrayFieldDescribe;
    }

    public ArrayFieldDescribeBuilder apiName(String apiName) {
        arrayFieldDescribe.setApiName(apiName);
        return this;
    }

    public ArrayFieldDescribeBuilder label(String label) {
        arrayFieldDescribe.setLabel(label);
        return this;
    }

    public ArrayFieldDescribeBuilder required(Boolean isRequired) {
        arrayFieldDescribe.setRequired(isRequired);
        return this;
    }

    public ArrayFieldDescribeBuilder unique(Boolean isUnique) {
        arrayFieldDescribe.setUnique(isUnique);
        return this;
    }
}
