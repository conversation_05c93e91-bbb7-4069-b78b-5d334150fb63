package com.facishare.crm.task.sfa.common.util;

import com.facishare.paas.appframework.core.model.RequestContext;
import com.facishare.paas.appframework.core.model.User;
import com.fxiaoke.log.AuditLog;
import com.fxiaoke.log.BizLogClient;
import com.fxiaoke.log.dto.AuditLogDTO;
import com.fxiaoke.pb.Pojo2Protobuf;
import com.github.autoconf.helper.ConfigHelper;
import com.github.trace.TraceContext;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;

import java.util.List;

/**
 * 发送AuditLog工具类 参考：http://wiki.firstshare.cn/display/PlatformKnowledge/biz-log-client
 */
public class SFAVerifyBizLogUtil {

    private SFAVerifyBizLogUtil() {
        throw new IllegalStateException("Utility class");
    }

    private static final String APP_NAME = ConfigHelper.getProcessInfo().getName();
    private static final String SERVER_IP = ConfigHelper.getProcessInfo().getIp();
    private static final String PROFILE = ConfigHelper.getProcessInfo().getProfile();
    private static final String AUDIT_TOPIC = "biz-audit-log";

    /**
     * 可灰度发送日志
     *
     * @param arg 入参
     * @param context RequestContext
     */


    public static void sendAuditLog(Arg arg, RequestContext context) {
        sendLog(arg, context, AUDIT_TOPIC);
    }

    private static void sendLog(Arg arg, RequestContext context, String topic) {
        AuditLogDTO dto = AuditLogDTO.builder()
            .createTime(arg.createTime == null ? System.currentTimeMillis() : arg.createTime)
            .cost(arg.cost == null ? 0L : arg.cost)
            .num(arg.num == null ? 0 : arg.num)
            .appName(APP_NAME)
            .traceId(TraceContext.get().getTraceId())
            .tenantId(context.getTenantId())
            .userId(context.getUser().getUpstreamOwnerIdOrUserId())
            .action(arg.action)
            .status(arg.status)
            .objectApiNames(StringUtils.isBlank(arg.objectApiNames) ? "sfa" : arg.objectApiNames)
            .objectIds(arg.objectIds)
            .message(arg.message)
            .error(arg.error)
            .extra(arg.extra)
            .profile(PROFILE)
            .serverIp(SERVER_IP)
            .eventId(context.getEventId())
            .parameters(arg.parameters)
            .build();
        BizLogClient.send(topic, Pojo2Protobuf.toMessage(dto, AuditLog.class).toByteArray());
    }

    public static void sendAuditLog(Arg arg, User user) {
        sendLog(arg, user, AUDIT_TOPIC);
    }

    private static void sendLog(Arg arg, User user, String topic) {
        AuditLogDTO dto = AuditLogDTO.builder()
            .createTime(arg.createTime == null ? System.currentTimeMillis() : arg.createTime)
            .cost(arg.cost == null ? 0L : arg.cost)
            .num(arg.num == null ? 0 : arg.num)
            .appName(APP_NAME)
            .traceId(TraceContext.get().getTraceId())
            .tenantId(user.getTenantId())
            .userId(user.getUpstreamOwnerIdOrUserId())
            .action(arg.action)
            .status(arg.status)
            .objectApiNames(StringUtils.isBlank(arg.objectApiNames) ? "sfa" : arg.objectApiNames)
            .objectIds(arg.objectIds)
            .message(arg.message)
            .error(arg.error)
            .extra(arg.extra)
            .profile(PROFILE)
            .serverIp(SERVER_IP)
            .parameters(arg.parameters)
            .build();
        BizLogClient.send(topic, Pojo2Protobuf.toMessage(dto, AuditLog.class).toByteArray());
    }


    @Builder
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class Arg {

        private Long createTime;
        private Long cost;
        private Integer num;
        private String action;
        private String status;
        private String objectApiNames;
        private String objectIds;
        private String message;
        private String error;
        private String extra;
        private String parameters;
    }


    @Builder
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class Args {

        private List<Arg> data;
    }

}
