package com.facishare.crm.task.sfa.common.describebuilder;

import com.facishare.paas.metadata.impl.ui.layout.Layout;
import com.facishare.paas.metadata.impl.ui.layout.component.SimpleComponent;
import com.facishare.paas.metadata.ui.layout.IComponent;
import com.google.common.collect.Maps;

import java.util.List;
import java.util.Map;

public class LayoutBuilder {
    private final Layout layout;

    private LayoutBuilder() {
        layout = new Layout();
        layout.setDeleted(false);
        layout.setPackage("CRM");
        layout.setCreateTime(System.currentTimeMillis());
    }

    public static LayoutBuilder builder() {
        return new LayoutBuilder();
    }

    public Layout build() {
        return layout;
    }

    public LayoutBuilder components(List<IComponent> componentList) {
        layout.setComponents(componentList);
        return this;
    }

    public LayoutBuilder name(String apiName) {
        layout.setName(apiName);
        return this;
    }

    public LayoutBuilder refObjectApiName(String refObjectApiName) {
        layout.setRefObjectApiName(refObjectApiName);
        return this;
    }

    public LayoutBuilder displayName(String displayName) {
        layout.setDisplayName(displayName);
        return this;
    }

    public LayoutBuilder tenantId(String tenantId) {
        layout.setTenantId(tenantId);
        return this;
    }

    public LayoutBuilder createBy(String createBy) {
        layout.setCreatedBy(createBy);
        return this;
    }

    public LayoutBuilder isDefault(boolean isDefualt) {
        layout.setIsDefault(isDefualt);
        return this;
    }

    public LayoutBuilder layoutType(String layoutType) {
        layout.setLayoutType(layoutType);
        return this;
    }

    public LayoutBuilder isShowFieldName(boolean isShowFieldName) {
        layout.setIsShowFieldname(isShowFieldName);
        return this;
    }

    public LayoutBuilder agentType(String agentType) {
        layout.setAgentType(agentType);
        return this;
    }

    public LayoutBuilder topInfo(SimpleComponent simpleComponent) {
        layout.setTopInfo(simpleComponent);
        return this;
    }

    public LayoutBuilder config(Map<String, Object> config) {
        if (config == null) {
            return this;
        }
        Map map = layout.getConfig();
        if (map == null) {
            map = Maps.newHashMap();
        }
        map.putAll(config);
        return this;
    }
}
