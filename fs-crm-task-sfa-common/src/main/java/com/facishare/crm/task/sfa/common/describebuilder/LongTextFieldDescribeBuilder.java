package com.facishare.crm.task.sfa.common.describebuilder;

import com.facishare.paas.metadata.impl.describe.LongTextFieldDescribe;

import java.util.Map;

public class LongTextFieldDescribeBuilder {
    private final LongTextFieldDescribe longTextFieldDescribe;

    private LongTextFieldDescribeBuilder() {
        longTextFieldDescribe = new LongTextFieldDescribe();
        longTextFieldDescribe.setCreateTime(System.currentTimeMillis());
        longTextFieldDescribe.setDefineType("package");
        longTextFieldDescribe.setStatus("released");
        longTextFieldDescribe.setFieldNum(null);
        longTextFieldDescribe.setRequired(false);
        longTextFieldDescribe.setUnique(false);
        longTextFieldDescribe.setIsExtend(false);
        longTextFieldDescribe.setActive(true);
    }

    public static LongTextFieldDescribeBuilder builder() {
        return new LongTextFieldDescribeBuilder();
    }

    public LongTextFieldDescribe build() {
        return longTextFieldDescribe;
    }

    public LongTextFieldDescribeBuilder apiName(String apiName) {
        longTextFieldDescribe.setApiName(apiName);
        return this;
    }

    public LongTextFieldDescribeBuilder pattern(String pattern) {
        longTextFieldDescribe.setPattern(pattern);
        return this;
    }

    public LongTextFieldDescribeBuilder maxLength(int maxLength) {
        longTextFieldDescribe.setMaxLength(maxLength);
        return this;
    }

    public LongTextFieldDescribeBuilder label(String label) {
        longTextFieldDescribe.setLabel(label);
        longTextFieldDescribe.setDescription(label);
        return this;
    }

    public LongTextFieldDescribeBuilder required(boolean required) {
        longTextFieldDescribe.setRequired(required);
        return this;
    }

    public LongTextFieldDescribeBuilder config(Map<String, Object> config) {
        longTextFieldDescribe.setConfig(config);
        return this;
    }

    public LongTextFieldDescribeBuilder inheritType(Integer inheritType) {
        longTextFieldDescribe.setInheritType(inheritType);
        return this;
    }
}
