package com.facishare.crm.task.sfa.common;

import com.alibaba.fastjson.JSON;
import com.facishare.paas.appframework.common.mq.RocketMQException;
import com.facishare.paas.appframework.common.mq.RocketMQMessageListener;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.exception.SystemErrorCode;
import com.github.autoconf.ConfigFactory;
import com.github.autoconf.api.IConfig;
import com.google.common.base.Joiner;
import com.google.common.base.Strings;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.client.consumer.DefaultMQPushConsumer;
import org.apache.rocketmq.client.consumer.listener.ConsumeConcurrentlyStatus;
import org.apache.rocketmq.client.consumer.listener.ConsumeOrderlyStatus;
import org.apache.rocketmq.client.consumer.listener.MessageListenerConcurrently;
import org.apache.rocketmq.client.consumer.listener.MessageListenerOrderly;
import org.apache.rocketmq.common.consumer.ConsumeFromWhere;
import org.springframework.util.StringUtils;

import javax.annotation.PostConstruct;
import java.net.InetAddress;
import java.util.List;
import java.util.UUID;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@Slf4j
public class SFARockerMQProcessor {
    private RocketMQMessageListener rocketMQMessageListener;
    private volatile boolean shutdown = true;
    private volatile DefaultMQPushConsumer defaultMQPushConsumer;
    private volatile Config config;
    private String configName;

    public void setConfigName(String configName) {
        this.configName = configName;
    }

    public void setRocketMQMessageListener(RocketMQMessageListener rocketMQMessageListener) {
        this.rocketMQMessageListener = rocketMQMessageListener;
    }

    @PostConstruct
    public void init() {
        ConfigFactory.getInstance().getConfig(configName, config -> {
            try {
                reload(config);
            } catch (Exception e) {
                throw new RocketMQException(SystemErrorCode.MQ_INIT_ERROR, e);
            }
        });
    }

    private void reload(IConfig conf) {
        String content = new String(conf.getContent());
        if (Strings.isNullOrEmpty(content)) {
            log.error("{} config content is empty", configName);
            throw new RocketMQException("config error! key=" + configName);
        }
        this.config = JSON.parseObject(content, Config.class);
        log.info("reload config:{}", config);
        try {
            if (!StringUtils.isEmpty(config.getConsumerIpRegular())) {
                Pattern regex = Pattern.compile(config.getConsumerIpRegular());
                String localhost = InetAddress.getLocalHost().getHostAddress();
                Matcher matcher = regex.matcher(localhost);
                if (!matcher.matches()) {
                    shutdown();
                    log.info("当前ip[{}]已被[{}]过滤", localhost, config.getConsumerIpRegular());
                    return;
                }
            }
        } catch (Exception e) {
            log.error("过滤ip异常", e);
        }
        if (defaultMQPushConsumer == null) {
            createConsumer(config);
        } else {
            shutdown();
            createConsumer(config);
        }
    }

    private void createConsumer(Config config) {
        try {
            defaultMQPushConsumer = new DefaultMQPushConsumer(config.getConsumeGroup());
            defaultMQPushConsumer.setNamesrvAddr(config.getNameServer());
            defaultMQPushConsumer.subscribe(config.getTopic(), config.getTags());
            defaultMQPushConsumer.setInstanceName(UUID.randomUUID().toString());
            defaultMQPushConsumer.setConsumeFromWhere(config.getConsumeFromWhere());
            defaultMQPushConsumer.setConsumeMessageBatchMaxSize(config.getConsumeBatchSize());
            int maxThread = 64;
            int minThread = 20;
            if (config.maxThread > 0 && config.maxThread > config.minThread) {
                maxThread = config.maxThread;
            }
            if (config.minThread > 0) {
                minThread = config.minThread;
            }
            defaultMQPushConsumer.setConsumeThreadMin(minThread);
            defaultMQPushConsumer.setConsumeThreadMax(maxThread);

            if (config.isOrdered()) {
                registerOrderedMessageListener();
            } else {
                registerConcurrentMessageListener();
            }
            defaultMQPushConsumer.start();
            shutdown = false;
        } catch (Exception e) {
            log.error("create RocketMQ defaultMQPushConsumer failed!", e);
        }

    }

    private void registerOrderedMessageListener() {
        defaultMQPushConsumer.registerMessageListener((MessageListenerOrderly) (messages, context) -> {

            if (shutdown) {
                log.warn("consumer is shutdown,suspend queue a moment!");
                return ConsumeOrderlyStatus.SUSPEND_CURRENT_QUEUE_A_MOMENT;
            }

            try {
                rocketMQMessageListener.consumeMessage(messages);
                return ConsumeOrderlyStatus.SUCCESS;
            } catch (Exception e) {
                log.error("consume message failed!", e);
            }
            return ConsumeOrderlyStatus.SUSPEND_CURRENT_QUEUE_A_MOMENT;
        });
    }

    private void registerConcurrentMessageListener() {
        defaultMQPushConsumer.registerMessageListener((MessageListenerConcurrently) (messages, context) -> {
            if (shutdown) {
                log.warn("consumer is shutdown,reConsume later!");
                return ConsumeConcurrentlyStatus.RECONSUME_LATER;
            }

            try {
                rocketMQMessageListener.consumeMessage(messages);
                return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
            } catch (Exception e) {
                log.error("consume message failed!", e);
            }
            return ConsumeConcurrentlyStatus.RECONSUME_LATER;
        });
    }


    private void shutdown() {
        shutdown = true;
        if (defaultMQPushConsumer != null) {
            try {
                this.defaultMQPushConsumer.shutdown();
                this.defaultMQPushConsumer = null;
            } catch (Exception e) {
                log.error("shutdown RocketMQ consumer failed!", e);
            }
        }
    }

    @Data
    @Slf4j
    public static class Config {
        String nameServer;
        String consumeGroup;    //这个值决定了消费集群
        String topic;           //一个Processor 处理一个Topic
        List<String> tags;      //消费那些Tag
        int consumeBatchSize;   //每次消费的消息的数量
        String consumeFromWhere;
        boolean ordered = false;
        int maxThread;//最大线程数
        int minThread;//最小线程数
        /**
         * 正则表达式, 只有命中ip的才会消费, 空则表示全都会消费
         */
        String consumerIpRegular;

        public int getConsumeBatchSize() {
            if (consumeBatchSize > 0) {
                return consumeBatchSize;
            }
            return 1;
        }

        public ConsumeFromWhere getConsumeFromWhere() {
            try {
                ConsumeFromWhere from = ConsumeFromWhere.valueOf(consumeFromWhere);
                return from;
            } catch (Exception e) {
                log.warn("Unsupported ConsumeFromWhere enum:" + consumeFromWhere);
            }
            return ConsumeFromWhere.CONSUME_FROM_LAST_OFFSET;
        }

        public String getTags() {
            if (CollectionUtils.notEmpty(tags)) {
                return Joiner.on("||").skipNulls().join(tags);
            }
            return "*";
        }
    }
}

