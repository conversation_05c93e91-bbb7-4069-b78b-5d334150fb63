package com.facishare.crm.task.sfa.common.describebuilder;

import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.impl.describe.CountFieldDescribe;

import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * Created on 2018/8/28.
 */
public class CountFieldDescribeBuilder {

    private final CountFieldDescribe countFieldDescribe;

    private CountFieldDescribeBuilder() {
        countFieldDescribe = new CountFieldDescribe();
        countFieldDescribe.setActive(true);
        countFieldDescribe.setDefineType(IFieldDescribe.DEFINE_TYPE_PACKAGE);
        countFieldDescribe.setStatus("released");
        countFieldDescribe.setIndex(false);
        countFieldDescribe.setIsExtend(false);
        countFieldDescribe.setCreateTime(System.currentTimeMillis());
    }

    public static CountFieldDescribeBuilder builder() {
        return new CountFieldDescribeBuilder();
    }

    public CountFieldDescribe build() {
        return countFieldDescribe;
    }

    public CountFieldDescribeBuilder apiName(String apiName) {
        countFieldDescribe.setApiName(apiName);
        return this;
    }

    public CountFieldDescribeBuilder label(String label) {
        countFieldDescribe.setLabel(label);
        return this;
    }

    public CountFieldDescribeBuilder countType(String countType) {
        countFieldDescribe.setCountType(countType);
        return this;
    }

    public CountFieldDescribeBuilder subObjectDescribeApiName(String subObjectDescribeApiName) {
        countFieldDescribe.setSubObjectDescribeApiName(subObjectDescribeApiName);
        return this;
    }

    public CountFieldDescribeBuilder countScopeSearchInfo(Map countScopeSearchInfo) {
        countFieldDescribe.setCountScopeSearchInfo(countScopeSearchInfo);
        return this;
    }

    public CountFieldDescribeBuilder countFieldApiName(String countFieldApiName) {
        countFieldDescribe.setCountFieldApiName(countFieldApiName);
        return this;
    }

    public CountFieldDescribeBuilder countFieldType(String countFieldType) {
        countFieldDescribe.setCountFieldType(countFieldType);
        return this;
    }

    public CountFieldDescribeBuilder wheres(List<LinkedHashMap> wheres) {
        countFieldDescribe.setWheres(wheres);
        return this;
    }

    public CountFieldDescribeBuilder returnType(String returnType) {
        countFieldDescribe.setReturnType(returnType);
        return this;
    }

    public CountFieldDescribeBuilder decimalPlaces(Integer decimalPlaces){
        countFieldDescribe.setDecimalPlaces(decimalPlaces);
        return this;
    }

    public CountFieldDescribeBuilder fieldApiName(String fieldApiName) {
        countFieldDescribe.setFieldApiName(fieldApiName);
        return this;
    }

    public CountFieldDescribeBuilder required(boolean isRequired) {
        countFieldDescribe.setRequired(isRequired);
        return this;
    }

}
