package com.facishare.crm.task.sfa.common.model;

/**
 * <AUTHOR>
 * @time 2024-07-01 15:07
 * @Description
 */
public interface HeaderName {
    String X_FS_EI = "x-fs-ei";
    //用于流程判断是否成环 所有Action 接口必须透传
    String X_FS_EVENT_ID = "x-fs-eventId";
    String X_FS_USER_INFO = "x-fs-userInfo";
    String X_FS_PEER_NAME = "x-fs-peer-name";//审计日志记录修改来源
    String X_FS_OUT_TENANT_ID = "x-out-tenant-id";
    String X_FS_OUT_USER_ID = "x-out-user-id";
    String X_FS_UPSTREAM_OWNER_ID = "x-fs-upstream-owner-id";
    String X_APP_ID = "x-app-id";
    String X_FS_MODEL_NAME = "x-fs-model-name";
    String X_TENANT_ID = "x-tenant-id";
    String X_USER_ID = "x-user-id";
    String X_FS_EMPLOYEE_ID = "X-fs-Employee-Id";
    String X_FS_ENTERPRISE_ACCOUNT = "X-fs-Enterprise-Account";
    String X_FS_ENTERPRISE_ID = "X-fs-Enterprise-Id";
    String X_FS_LOCALE = "x-fs-locale";
}
