package com.facishare.crm.task.sfa.common.describebuilder;

import com.facishare.paas.metadata.impl.ui.layout.component.SimpleComponent;
import com.facishare.paas.metadata.ui.layout.IButton;
import com.facishare.paas.metadata.ui.layout.IFieldSection;
import org.apache.commons.collections.CollectionUtils;

import java.util.List;

/**
 * <AUTHOR>
 * @date 12/06/2018
 */
public class SimpleComponentBuilder {
    private final SimpleComponent simpleComponent;

    private SimpleComponentBuilder() {
        simpleComponent = new SimpleComponent();
    }

    public static SimpleComponentBuilder builder() {
        return new SimpleComponentBuilder();
    }

    public SimpleComponent build() {
        return simpleComponent;
    }

    public SimpleComponentBuilder name(String name) {
        simpleComponent.setName(name);
        return this;
    }

    public SimpleComponentBuilder buttons(List<IButton> buttons) {
        if (CollectionUtils.isNotEmpty(buttons)) {
            simpleComponent.setButtons(buttons);
        }
        return this;
    }

    public SimpleComponentBuilder fieldSections(List<IFieldSection> fieldSections) {
        simpleComponent.setFieldSections(fieldSections);
        return this;
    }

    public SimpleComponentBuilder header(String header) {
        simpleComponent.setHeader(header);
        return this;
    }

    public SimpleComponentBuilder order(int order) {
        simpleComponent.setOrder(order);
        return this;
    }

    public SimpleComponentBuilder isHidden(boolean isHidden) {
        simpleComponent.setIsHidden(isHidden);
        return this;
    }
}
