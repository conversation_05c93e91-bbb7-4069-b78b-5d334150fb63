package com.facishare.crm.task.sfa.common.describebuilder;

import com.facishare.paas.metadata.impl.describe.EmailFieldDescribe;

import java.util.Map;

/**
 * Created by guoyd on 2019/3/4.
 */
public class EmailFieldDescribeBuilder {
    private final EmailFieldDescribe emailFieldDescribe;

    private EmailFieldDescribeBuilder(){
        emailFieldDescribe=new EmailFieldDescribe();
        emailFieldDescribe.setActive(true);
        emailFieldDescribe.setIsExtend(false);
        emailFieldDescribe.setIndex(true);
        emailFieldDescribe.setStatus("released");
        emailFieldDescribe.setDefineType("package");
        emailFieldDescribe.setUnique(false);
    }
    public static EmailFieldDescribeBuilder builder(){return new EmailFieldDescribeBuilder();}

    public EmailFieldDescribe build(){return emailFieldDescribe;}

    public EmailFieldDescribeBuilder apiName(String apiName){
        emailFieldDescribe.setApiName(apiName);
        return this;
    }

    public EmailFieldDescribeBuilder label(String label){
        emailFieldDescribe.setLabel(label);
        return this;
    }

    public EmailFieldDescribeBuilder required(boolean required) {
        emailFieldDescribe.setRequired(required);
        return this;
    }

    public EmailFieldDescribeBuilder defaultValud(Object defaultValue) {
        emailFieldDescribe.setDefaultValue(defaultValue);
        return this;
    }

    public EmailFieldDescribeBuilder helpText(String helpText) {
        emailFieldDescribe.setHelpText(helpText);
        return this;
    }

    public EmailFieldDescribeBuilder config(Map<String, Object> config) {
        emailFieldDescribe.setConfig(config);
        return this;
    }
}
