package com.facishare.crm.task.sfa.common.describebuilder;

import com.facishare.paas.metadata.impl.describe.EmployeeFieldDescribe;

import java.util.Map;

public class EmployeeFieldDescribeBuilder {
    private final EmployeeFieldDescribe employeeFieldDescribe;

    private EmployeeFieldDescribeBuilder() {
        employeeFieldDescribe = new EmployeeFieldDescribe();
        employeeFieldDescribe.setUnique(false);
        employeeFieldDescribe.setStatus("released");
        employeeFieldDescribe.setIndex(true);
        employeeFieldDescribe.setDefineType("package");
        employeeFieldDescribe.setIsSingle(true);
        employeeFieldDescribe.setActive(true);
        employeeFieldDescribe.setIsExtend(false);
        employeeFieldDescribe.setFieldNum(null);
    }

    public static EmployeeFieldDescribeBuilder builder() {
        return new EmployeeFieldDescribeBuilder();
    }

    public EmployeeFieldDescribe build() {
        return employeeFieldDescribe;
    }

    public EmployeeFieldDescribeBuilder apiName(String apiName) {
        employeeFieldDescribe.setApiName(apiName);
        return this;
    }

    public EmployeeFieldDescribeBuilder label(String label) {
        employeeFieldDescribe.setLabel(label);
        return this;
    }

    public EmployeeFieldDescribeBuilder required(boolean required) {
        employeeFieldDescribe.setRequired(required);
        return this;
    }

    public EmployeeFieldDescribeBuilder config(Map<String, Object> config) {
        employeeFieldDescribe.setConfig(config);
        return this;
    }

    public EmployeeFieldDescribeBuilder single(boolean isSingle) {
        employeeFieldDescribe.setIsSingle(isSingle);
        return this;
    }
}
