package com.facishare.crm.task.sfa.common.constants;

import com.facishare.paas.metadata.api.DBRecord;
import com.google.common.collect.Lists;

import java.util.List;

public interface PriceBookConstants {
    String API_NAME = "PriceBookObj";
    String API_NAME_PRODUCT = "PriceBookProductObj";
    String PRICE_BOOK_DATA = "price_book_data";
    String ORIGIN_PRICEBOOK_ID = "origin_pricebook_id";
    /**
     * 复制价目表需要移除的字段
     */
    List<String> REMOVESYSFIELDS = Lists.newArrayList(DBRecord.ID,
            DBRecord.VERSION,
            DBRecord.IS_DELETED,
            SystemConstants.Field.TennantID.apiName,
            SystemConstants.Field.CreateBy.apiName,
            SystemConstants.Field.CreateTime.apiName,
            SystemConstants.Field.LastModifiedBy.apiName,
            SystemConstants.Field.LastModifiedTime.apiName,
            SystemConstants.Field.Owner.apiName,
            SystemConstants.Field.OwnerDepartment.apiName,
            SystemConstants.Field.LifeStatus.apiName,
            SystemConstants.Field.LockStatus.apiName,
            SystemConstants.Field.LockRule.apiName,
            SystemConstants.Field.LockUser.apiName,
            SystemConstants.Field.DataOwnDepartment.apiName);
    enum Field {
        ACTIVESTATUS("active_status", "启用状态"),
        STARTDATE("start_date", "有效开始时间"),
        ENDDATE("end_date", "有效开始时间"),
        ISSTANDARD("is_standard", "是否默认"),
        PARTNERRANGE("partner_range", "适用合作伙伴"),
        ACCOUNTRANGE("account_range", "适用客户"),
        //ISVALIDORDER("is_validorder", "过期或禁用时是否允许用该价目表校验"),
        PRODUCT_ID("product_id", "产品名称"),
        PRICE_BOOK_ID("price_book_id", "价目表"),
        PRICE_BOOK_PRODUCT_ID("price_book_product_id", "价目表明细"),
        PRIORITY("priority", "优先级");

        private final String apiName;
        private final String label;

        Field(String apiName, String label) {
            this.apiName = apiName;
            this.label = label;
        }

        public String getApiName() {
            return this.apiName;
        }

        public String getLabel() {
            return this.label;
        }
    }

    enum ProductField {
        LAST_MODIFIED_TIME("last_modified_time","最后修改时间"),
        PRICEBOOKID("pricebook_id", "价目表主键"), PRICEBOOKPRICE("pricebook_price", "价目表价格"),
        CREATETIME("create_time", "创建时间"),
        PRODUCTID("product_id", "产品主键"), PRODUCTNAME("product_name", "产品名称"),
        PRICEBOOKSELLINGPRICE("pricebook_sellingprice", "价目表售价"),
        DISCOUNT("discount","折扣"),
        FLOOR_PRICE("floor_price", "浮动下限价格"),
        CEILING_PRICE("ceiling_price", "浮动上限价格");
        private final String apiName;
        private final String label;

        ProductField(String apiName, String label) {
            this.apiName = apiName;
            this.label = label;
        }

        public String getApiName() {
            return this.apiName;
        }
    }

    enum ActiveStatus {
        ON("1", "启用"), OFF("0", "禁用");
        private final String status;
        private final String name;

        ActiveStatus(String status, String name) {
            this.status = status;
            this.name = name;
        }

        public String getStatus() {
            return status;
        }
    }
}
