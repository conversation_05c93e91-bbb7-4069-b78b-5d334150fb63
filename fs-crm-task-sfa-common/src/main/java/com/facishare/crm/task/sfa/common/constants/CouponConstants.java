package com.facishare.crm.task.sfa.common.constants;


import com.facishare.crm.openapi.Utils;
import com.facishare.paas.metadata.impl.ObjectData;
import com.google.common.collect.ImmutableSet;

/**
 * 优惠券常量
 *
 * <AUTHOR>
 */
public interface CouponConstants {
    String COUPON_PLAN_API_NAME = "CouponPlanObj";
    String COUPON_INSTANCE_API_NAME = "CouponInstanceObj";
    String D_TENANT_ID = "d_tenant_id";
    String PROGRAM_ID = "program_id";
    String COUPON_CONFIG_KEY = "coupon";
    //优惠券发放数量
    String QUANTITY = "quantity";


    /**
     * 编辑时可以修改字段
     */
    ImmutableSet<String> EDIT_CAN_MODIFY_FIELD = ImmutableSet.of(
            CouponPlanField.START_DATE.getApiName(),
            CouponPlanField.END_DATE.getApiName(),
            CouponPlanField.REMARK.getApiName(),
            ObjectData.NAME);
    String REFERENCE_SOURCE_LABEL = "优惠券方案";

    /**
     * 优惠券计划领域
     */
    enum CouponPlanField {
        ACTIVE_STATUS("active_status", "启用状态"),
        PLAN_TYPE("plan_type", "优惠券方案类型"),
        PAY_MODE("pay_mode", "限定支付方式"),
        USE_TYPE("use_type", "使用方式"),
        AMOUNT("amount", "面额"),
        USED_OBJECT_API_NAME("used_object_api_name", "使用对象"),
        LOWER_LIMIT("lower_limit", "满额"),
        START_DATE("start_date", "有效起期"),
        END_DATE("end_date", "有效止期"),
        REMARK("remark", "返利单备注"),
        PRODUCT_CONDITION_DESC("product_condition_desc", "产品条件描述"),
        PRODUCT_CONDITION_TYPE("product_condition_type", "产品条件类型"),
        PRODUCT_CONDITION_MODE("product_condition_mode", "产品条件模式"),
        PRODUCT_CONDITION_CONTENT("product_condition_content", "产品条件数据"),
        PRODUCT_CONDITION_RULE("product_condition_rule", "产品条件规则"),
        LAST_MODIFIED_TIME("last_modified_time", "最后更新时间");


        private final String filedApiName;

        CouponPlanField(String filedApiName, String label) {
            this.filedApiName = filedApiName;
        }

        public String getApiName() {
            return this.filedApiName;
        }
    }

    /**
     * 产品状态模式
     */
    enum ProductConditionMode {
        SIMPLE("SIMPLE", "简单"),
        HARD("HARD", "复杂");
        private final String value;

        ProductConditionMode(String value, String label) {
            this.value = value;
        }

        public String getValue() {
            return this.value;
        }
    }

    enum UsedObjectApiName {
        SALESORDEROBJ(Utils.SALES_ORDER_API_NAME, "销售订单"),
        SALESORDERPRODUCTOBJ(Utils.SALES_ORDER_PRODUCT_API_NAME, "销售订单产品");
        private final String value;

        UsedObjectApiName(String value, String label) {
            this.value = value;
        }

        public String getValue() {
            return this.value;
        }
    }

    /**
     * 产品状态类型
     */
    enum ProductConditionType {
        CONDITION("CONDITION", "指定范围"),
        FIXED("FIXED", "指定产品"),
        ALL("ALL", "全部"),
        HARD("HARD", "复杂产品条件");

        private final String value;

        ProductConditionType(String value, String label) {
            this.value = value;
        }

        public String getValue() {
            return this.value;
        }
    }

    /**
     * 启用状态状态
     */
    enum ActiveStatus {
        ENABLE("enable", "启用"),
        DISABLE("disable", "未启用");
        private final String value;

        ActiveStatus(String value, String label) {
            this.value = value;
        }

        public String getValue() {
            return this.value;
        }
    }

    /**
     * 方案类型
     */
    enum PlanType {
        REDUCE("Reduce", "满减券");
        private final String value;

        PlanType(String value, String label) {
            this.value = value;
        }

        public String getValue() {
            return this.value;
        }
    }

    /**
     * 使用类型
     */
    enum UseType {
        DISCOUNT("Discount", "分摊折价"),
        CASH("Cash", "冲抵回款");
        private final String value;

        UseType(String value, String label) {
            this.value = value;
        }

        public String getValue() {
            return this.value;
        }
    }

    enum MustRelation {
        ANYONE("ANYONE", "任一必含"),
        ALL("ALL", "全部必含"),
        ANYN("ANYN", "必含任N");
        private final String value;

        MustRelation(String value, String label) {
            this.value = value;
        }

        public String getValue() {
            return this.value;
        }
    }


    /**
     * 生活状态
     */
    enum LifeStatus {
        INEFFECTIVE("ineffective", "未生效"),
        UNDERREVIEW("under_review", "审核中"),
        NORMAL("normal", "正常"),
        INCHANGE("in_change", "变更中"),
        INVALID("invalid", "作废");

        private final String code;

        LifeStatus(String code, String statusName) {
            this.code = code;
        }

        public String getCode() {
            return code;
        }
    }

    /**
     * 优惠券实例字段
     */
    enum CouponInstanceField {

        ACCOUNT_ID("account_id", "客户名称"),
        COUPON_PLAN_ID("coupon_plan_id", "优惠券方案"),
        SEND_TIME("send_time", "领取时间"),
        USE_TIME("use_time", "使用时间"),
        LOYALTY_MEMBER_ID("loyalty_member_id", "会员"),
        USE_STATUS("use_status", "使用状态");

        private final String filedApiName;

        CouponInstanceField(String filedApiName, String label) {
            this.filedApiName = filedApiName;
        }

        public String getApiName() {
            return this.filedApiName;
        }
    }

    /**
     * 使用状态
     */
    enum UseStatus {
        USED("USED", "已使用"),
        UNUSED("UNUSED", "未使用");
        private final String value;

        UseStatus(String value, String label) {
            this.value = value;
        }

        public String getValue() {
            return this.value;
        }
    }

    /**
     * 插件主对象字段
     */
    class PluginField {
        public static final String REBATE_AMOUNT = "rebate_amount";
        public static final String COUPON_AMOUNT = "coupon_amount";
        public static final String MISC_CONTENT = "misc_content";
        public static final String LIFE_STATUS = "life_status";
    }

    /**
     * 插件从对象字段
     */
    class PluginDetailField {
        public static final String REBATE_AMORTIZE_AMOUNT = "rebate_amortize_amount";
        public static final String REBATE_DYNAMIC_AMOUNT = "rebate_dynamic_amount";
        public static final String COUPON_AMORTIZE_AMOUNT = "coupon_amortize_amount";// 优惠券优惠额
        public static final String COUPON_DYNAMIC_AMOUNT = "coupon_dynamic_amount";//优惠券分摊金额
        public static final String PRODUCT_PRICE = "product_price";
        public static final String SALES_PRICE = "sales_price";
        public static final String PRODUCT_ID = "product_id";
        public static final String QUANTITY = "quantity";
        public static final String POLICY_SUBTOTAL = "policy_subtotal";//促销后小计
        public static final String SUBTOTAL = "subtotal";
        public static final String PRICE_BOOK_SUBTOTAL = "price_book_subtotal";//价目表小计
        public static final String AMORTIZE_SUBTOTAL = "amortize_subtotal";
        public static final String ACTUAL_UNIT = "actual_unit";
        public static final String UNIT_FLAG = "unit__v";

    }
}
