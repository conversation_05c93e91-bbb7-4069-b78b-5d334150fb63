package com.facishare.crm.task.sfa.common.util;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;

import java.lang.reflect.ParameterizedType;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * ============================================================
 *
 * @Description:
 * @CreatedBy: Sundy on 2024-11-28
 * ============================================================
 */
public class ResponseParser {
    public static <T> T getResponseObject(TypeReference<T> typeReference, Object data) {
        if (data != null) {
            return JSON.parseObject(JSON.toJSONString(data), typeReference);
        }
        // 如果是集合类型，返回空集合
        if (!(typeReference.getType() instanceof ParameterizedType)) {
            return null;
        }
        ParameterizedType parameterizedType = (ParameterizedType) typeReference.getType();
        if (List.class.isAssignableFrom((Class<?>) parameterizedType.getRawType())) {
            return (T) Lists.newArrayList();
        }
        if (Set.class.isAssignableFrom((Class<?>) parameterizedType.getRawType())) {
            return (T) Lists.newArrayList();
        }
        if (Map.class.isAssignableFrom((Class<?>) parameterizedType.getRawType())) {
            return (T) Maps.newHashMap();
        }
        return null;
    }
}
