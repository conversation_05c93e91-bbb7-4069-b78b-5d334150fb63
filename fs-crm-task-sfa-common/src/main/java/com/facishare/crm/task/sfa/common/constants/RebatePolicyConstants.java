package com.facishare.crm.task.sfa.common.constants;

public interface RebatePolicyConstants {
    String EXPECTED_REBATE_REDIS_KEY_PREFIX ="EXPECTED_REBATE";
    String REBATE_POLICY_API_NAME = "RebatePolicyObj";
    String REBATE_POLICY_RULE_API_NAME = "RebatePolicyRuleObj";
    String REBATE_POLICY_LOG_API_NAME = "RebatePolicyLogObj";
    String CONDITION_FUNC_NAMESPACE = "condition";
    String EXECUTION_FUNC_NAMESPACE = "execution";
    String CHANGE_RESULT_FUNC_NAMESPACE = "change_result";
    String SYS_TENANT = "-100";
    String EMPTY_ARRAY_SIGN = "[]";
    int MAX_COUNT = 10000;

    class RebatePolicyField {
        public static final String ACTIVE_STATUS = "active_status";
        public static final String START_DATE = "start_date";
        public static final String END_DATE = "end_date";
        public static final String PRIORITY = "priority";
        public static final String ACCOUNT_TYPE = "account_type";
        public static final String ACCOUNT_RANGE = "account_range";
        public static final String CALCULATE_RANGE = "calculate_range";
        public static final String REBATE_BASIS = "rebate_basis";
        public static final String EXECUTE_MODE = "execute_mode";
        public static final String SPECIFY_CREATE_DATE = "specify_create_date";
        public static final String LAST_MODIFIED_TIME = "last_modified_time";
        public static final String TENANT_ID = "tenant_id";
        public static final String CYCLE_COUNT = "cycle_count";
        public static final String EXECUTE_COUNT = "execute_count";
        public static final String CYCLE_INFO = "cycle_info";

        public static final String REBATE_DIMENSION = "rebate_dimension";
    }

    class RebatePolicyRuleField {
        public static final String REBATE_POLICY_ID = "rebate_policy_id";
        public static final String SOURCE_OBJECT_API_NAME = "source_object_api_name";
        public static final String PRIORITY = "priority";
        public static final String RULE_TYPE = "rule_type";
        public static final String RULE_CONDITION = "rule_condition";
        public static final String EXECUTION_RESULT = "execution_result";
        public static final String TOPIC = "topic";
        public static final String USE_TYPE = "use_type";
        public static final String FUNCTION_INFO = "function_info";
        public static final String REBATE_USED_DATE = "rebate_used_date";
        public static final String REBATE_USED_DATE_TYPE = "type";
        public static final String REBATE_USED_DATE_START = "start_date";
        public static final String REBATE_USED_DATE_END = "end_date";
        public static final String PRODUCT_CONDITION_TYPE = "product_condition_type";
        public static final String PRODUCT_CONDITION_CONTENT = "product_condition_content";
        public static final String PRODUCT_RANGE = "product_range";
        public static final String PRODUCT_RANGE_TYPE = "product_range_type";
        public static final String REBATE_DIMENSION = "rebate_dimension";
        public static final String RULE_CONDITION_PRO = "rule_condition_pro";
    }

    class RebatePolicyLogField {
        public static final String REBATE_POLICY_ID = "rebate_policy_id";
        public static final String REBATE_POLICY_RULE_ID = "rebate_policy_rule_id";
        public static final String ACCOUNT_ID = "account_id";
        public static final String REBATE_IDS = "rebate_ids";
        public static final String SOURCE_OBJECT_API_NAME = "source_object_api_name";
        public static final String SOURCE_OBJECT_ID = "source_object_id";
        public static final String RUN_STATUS = "run_status";
        public static final String ERROR_MSG = "error_msg";
        public static final String LOG_TYPE = "log_type";
        public static final String TRIGGER_MODE = "trigger_mode";
        public static final String REBATE_POLICY_BATCH = "rebate_policy_batch";
        public static final String CONDITION_FUNCTION_RETURN = "condition_function_return";
        public static final String EXECUTION_FUNCTION_RETURN = "execution_function_return";
        public static final String CHANGE_RESULT_FUNCTION_RETURN = "change_result_function_return";
    }

    /**
     * 触发模式
     */
    enum TriggerMode {
        JOB("job", "任务"),
        MANUAL("manual", "手动触发");
        private final String value;

        TriggerMode(String value, String label) {
            this.value = value;
        }

        public String getValue() {
            return this.value;
        }
    }

    /**
     * 日志类型
     */
    enum LogType {
        JOB("job", "任务"),
        TENANT("tenant", "租户"),
        MASTER("master", "返利产生政策"),
        ACCOUNT("account", "客户"),
        DETAIL("detail", "返利产生规则");
        private final String value;

        LogType(String value, String label) {
            this.value = value;
        }

        public String getValue() {
            return this.value;
        }
    }

    /**
     * 运行状态
     */
    enum RunStatus {
        SUCCESS("success", "成功"),
        RUNNING("running", "执行中"),
        FAIL("fail", "失败");
        private final String value;

        RunStatus(String value, String label) {
            this.value = value;
        }

        public String getValue() {
            return this.value;
        }
    }

    /**
     * 使用类型
     */
    enum UseType {
        QUANTITY("Quantity", "按数量返"),
        AMOUNT("Amount", "按金额返"),
        DISCOUNT("Discount", "分摊折价"),
        CASH("Cash", "冲抵回款");
        private final String value;

        UseType(String value, String label) {
            this.value = value;
        }

        public String getValue() {
            return this.value;
        }
    }

    /**
     * 返利类型
     */
    enum RuleType {
        MONEY("Money", "金额返利"),
        PRODUCT("Product", "产品返利");


        private final String value;

        RuleType(String value, String label) {
            this.value = value;
        }

        public String getValue() {
            return this.value;
        }
    }

    /**
     * 启用状态状态
     */
    enum ActiveStatus {
        ENABLE("enable", "启用"),
        DISABLE("disable", "未启用");
        private final String value;

        ActiveStatus(String value, String label) {
            this.value = value;
        }

        public String getValue() {
            return this.value;
        }
    }

    /**
     * 适用客户范围类型
     */
    enum AccountType {
        CONDITION("CONDITION", "指定范围"),
        FIXED("FIXED", "指定客户"),
        ALL("ALL", "全部");

        private final String value;

        AccountType(String value, String label) {
            this.value = value;
        }

        public String getValue() {
            return this.value;
        }
    }

    enum RebateUsedType {
        UNLIMITED("UNLIMITED", "不限制"),
        DYNAMIC("DYNAMIC", "动态时间区间"),
        FIXED("FIXED", "固定时间区间");

        private final String value;

        RebateUsedType(String value, String label) {
            this.value = value;
        }

        public String getValue() {
            return this.value;
        }
    }

    /**
     * 产品条件类型
     */
    enum ProductConditionType {
        CONDITION("CONDITION", "指定范围"),
        FIXED("FIXED", "指定产品"),
        ALL("ALL", "全部");

        private final String value;

        ProductConditionType(String value, String label) {
            this.value = value;
        }

        public String getValue() {
            return this.value;
        }
    }

    /**
     * 计算范围
     */
    enum CalculateRange {
        HISTORY("history", "按历史累计值返"),
        CURRENT("current", "按当单值返");

        private final String value;

        CalculateRange(String value, String label) {
            this.value = value;
        }

        public String getValue() {
            return this.value;
        }
    }

    /**
     * 返利依据
     */
    enum RebateBasis {
        MASTER("master", "按交易单据返利"),
        DETAIL("detail", "按交易明细返利");

        private final String value;

        RebateBasis(String value, String label) {
            this.value = value;
        }

        public String getValue() {
            return this.value;
        }
    }

    /**
     * 运行模式
     */
    enum ExecuteMode {
        EACH("each", "逐单产生"),
        MANUAL("manual", "手动产生"),
        CYCLE("cycle", "周期性产生"),
        SPECIFY("specify", "指定时间一次性产生");

        private final String value;

        ExecuteMode(String value, String label) {
            this.value = value;
        }

        public String getValue() {
            return this.value;
        }
    }

    /**
     * 周期性产生类型
     */
    enum CycleType {
        DAY("DAY", "每天"),
        WEEK("WEEK", "每周"),
        MONTH("MONTH", "每月"),
        QUARTER("QUARTER", "每季度"),
        YEAR("YEAR", "每年");

        private final String value;

        CycleType(String value, String label) {
            this.value = value;
        }

        public String getValue() {
            return this.value;
        }
    }

    enum DynamicEnum {
        TODAY("TODAY", "今天"),
        TOMORROW("TOMORROW", "明天"),
        DAY_AFTER_TOMORROW("DAY_AFTER_TOMORROW", "后天"),
        THIS_QUARTER_START("THIS_QUARTER_START", "本季度第一天"),
        NEXT_QUARTER_START("NEXT_QUARTER_START", "下季度第一天"),
        THIS_MONTH_START("THIS_MONTH_START", "本月第一天"),
        NEXT_MONTH_START("NEXT_MONTH_START", "下月第一天"),
        THIS_WEEK_START("THIS_WEEK_START", "本周第一天"),
        NEXT_WEEK_START("NEXT_WEEK_START", "下周第一天"),
        THIS_YEAR_START("THIS_YEAR_START", "本年第一天"),
        NEXT_YEAR_START("NEXT_YEAR_START", "下年第一天"),
        THIS_QUARTER_END("THIS_QUARTER_END", "本季度最后一天"),
        NEXT_QUARTER_END("NEXT_QUARTER_END", "下季度最后一天"),
        THIS_MONTH_END("THIS_MONTH_END", "本月最后一天"),
        NEXT_MONTH_END("NEXT_MONTH_END", "下月最后一天"),
        THIS_WEEK_END("THIS_WEEK_END", "本周最后一天"),
        NEXT_WEEK_END("NEXT_WEEK_END", "下周最后一天"),
        THIS_YEAR_END("THIS_YEAR_END", "本年最后一天"),
        NEXT_YEAR_END("NEXT_YEAR_END", "下年最后一天");
        private String value;
        private String label;

        DynamicEnum(String value, String label) {
            this.value = value;
            this.label = label;
        }

        public String getValue() {
            return value;
        }

        public String getLabel() {
            return label;
        }
    }
}
