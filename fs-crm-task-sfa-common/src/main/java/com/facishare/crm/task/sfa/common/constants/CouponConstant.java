package com.facishare.crm.task.sfa.common.constants;

public interface CouponConstant {
    String COUPON_PLAN_API_NAME = "CouponPlanObj";
    String PROGRAM_ID = "program_id";
    String COUPON_CONFIG_KEY = "coupon";
    /**
     * 优惠券计划领域
     */
    enum CouponPlanField {
        PRODUCT_CONDITION_CONTENT("product_condition_content", "产品条件数据"),
        PRODUCT_CONDITION_RULE("product_condition_rule", "产品条件规则"),
        LAST_MODIFIED_TIME("last_modified_time", "最后更新时间");


        private final String filedApiName;

        CouponPlanField(String filedApiName, String label) {
            this.filedApiName = filedApiName;
        }

        public String getApiName() {
            return this.filedApiName;
        }
    }

    /**
     * 优惠券实例字段
     */
    enum CouponInstanceField {

        ACCOUNT_ID("account_id", "客户名称"),
        COUPON_PLAN_ID("coupon_plan_id", "优惠券方案"),
        SEND_TIME("send_time", "领取时间"),
        USE_TIME("use_time", "使用时间"),
        PAPER_COUPON_NO("paper_coupon_no", "纸质券号"),
        USE_STATUS("use_status", "使用状态");

        private final String filedApiName;

        CouponInstanceField(String filedApiName, String label) {
            this.filedApiName = filedApiName;
        }

        public String getApiName() {
            return this.filedApiName;
        }
    }

    /**
     * 使用状态
     */
    enum UseStatus {
        USED("USED", "已使用"),
        UNUSED("UNUSED", "未使用");
        private final String value;

        UseStatus(String value, String label) {
            this.value = value;
        }

        public String getValue() {
            return this.value;
        }
    }

}
